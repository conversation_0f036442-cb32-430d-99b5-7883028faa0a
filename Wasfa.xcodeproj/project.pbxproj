// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 60;
	objects = {

/* Begin PBXBuildFile section */
		001E606D7AEAE3CF3C2B418C /* StackCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 01E0AD9CBA71FEB4461FE5FC /* StackCell.swift */; };
		04F63B4036580CB328B3EB31 /* Productcard6Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9B0AAEC205F089AD3093E4BC /* Productcard6Cell.swift */; };
		0575DE67C49582B3EA1096EE /* SplashscreenViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = C4A20AFD289680B853934BA6 /* SplashscreenViewModel.swift */; };
		06903244263BB3DD9756756B /* OrderhistorycompletedViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = D2A7DBE9BCEBB56E359D31B9 /* OrderhistorycompletedViewModel.swift */; };
		070AE57BA8A6F5BA889CABF7 /* MyAccountViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 47DE2C55B95BBF79118D2DCA /* MyAccountViewModel.swift */; };
		0BFE9D80B0F7DEDD46F7C76E /* ProductCardCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58D96A51AEC17AD58FCEF048 /* ProductCardCell.swift */; };
		0D2681E195BAFC66FCA6B15E /* OrderhistoryViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A984103FD8219F53029FFF9E /* OrderhistoryViewModel.swift */; };
		13CAE7984487B97DB95AE808 /* AddressCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8F82E945766BEFD862C3FAEC /* AddressCardView.swift */; };
		16691E70134A638F593C5625 /* LowcateCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = C9E6FFC61F6B7E26030CB88F /* LowcateCell.swift */; };
		1806944B2D6365FA0094FDFA /* ToastView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 180694482D6365FA0094FDFA /* ToastView.swift */; };
		1806944C2D6365FA0094FDFA /* ToastModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 180694492D6365FA0094FDFA /* ToastModel.swift */; };
		1806944D2D6365FA0094FDFA /* ToastModifier.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1806944A2D6365FA0094FDFA /* ToastModifier.swift */; };
		180694502D63664D0094FDFA /* SwipeActions in Frameworks */ = {isa = PBXBuildFile; productRef = 1806944F2D63664D0094FDFA /* SwipeActions */; };
		180694562D63DE740094FDFA /* ContactUsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 180694552D63DE720094FDFA /* ContactUsView.swift */; };
		180694582D63DF7E0094FDFA /* ContactUsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 180694572D63DF7C0094FDFA /* ContactUsViewModel.swift */; };
		1806945E2D63EAA60094FDFA /* AboutUsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1806945D2D63EA9F0094FDFA /* AboutUsView.swift */; };
		180694602D63EABF0094FDFA /* AboutUsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1806945F2D63EABC0094FDFA /* AboutUsViewModel.swift */; };
		180EFEF22DF9654E005B3693 /* RxHistoryView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 180EFEF12DF96546005B3693 /* RxHistoryView.swift */; };
		181B61272D53B90D009570AC /* FilterSortView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 181B61262D53B90B009570AC /* FilterSortView.swift */; };
		181B61292D54219F009570AC /* ProductCategoryCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 181B61282D54219E009570AC /* ProductCategoryCell.swift */; };
		181E38432CA083FA00780921 /* WishlistView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 181E38422CA083F300780921 /* WishlistView.swift */; };
		1827998D2DB90FA900E275E8 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 1827998C2DB90FA900E275E8 /* GoogleService-Info.plist */; };
		1827998F2DB9113300E275E8 /* Message.apns in Resources */ = {isa = PBXBuildFile; fileRef = 1827998E2DB9113300E275E8 /* Message.apns */; };
		185CC6462D9C0EE60014074F /* PoppinsMedium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 185CC6452D9C0EE60014074F /* PoppinsMedium.ttf */; };
		185CC6482D9C162B0014074F /* PoppinsBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 185CC6472D9C162B0014074F /* PoppinsBold.ttf */; };
		185CC64A2D9D4EC50014074F /* PoppinsRegular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 185CC6492D9D4EC50014074F /* PoppinsRegular.ttf */; };
		185CC64C2D9D4F7C0014074F /* PoppinsSemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 185CC64B2D9D4F7C0014074F /* PoppinsSemiBold.ttf */; };
		186BC7822C987DB900DA5131 /* DashboardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 186BC7812C987DB900DA5131 /* DashboardView.swift */; };
		187FEED02CD670B300EAC51C /* SDWebImageSwiftUI in Frameworks */ = {isa = PBXBuildFile; productRef = 187FEECF2CD670B300EAC51C /* SDWebImageSwiftUI */; };
		187FEED32CD6729500EAC51C /* SDWebImageWebPCoder in Frameworks */ = {isa = PBXBuildFile; productRef = 187FEED22CD6729500EAC51C /* SDWebImageWebPCoder */; };
		187FEED52CD67A3700EAC51C /* ProductDetailsModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 187FEED42CD67A2900EAC51C /* ProductDetailsModel.swift */; };
		188221EB2CF396B4005FB2C9 /* ColorExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 188221EA2CF396B0005FB2C9 /* ColorExtension.swift */; };
		188221ED2CF39767005FB2C9 /* ProductListingModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 188221EC2CF39760005FB2C9 /* ProductListingModel.swift */; };
		188221F42CF5283E005FB2C9 /* PaginatedViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 188221F32CF52834005FB2C9 /* PaginatedViewModel.swift */; };
		188221FC2CF6336F005FB2C9 /* CalendarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 188221FB2CF6336E005FB2C9 /* CalendarView.swift */; };
		188221FE2CF641BA005FB2C9 /* AddressPopUpView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 188221FD2CF641B3005FB2C9 /* AddressPopUpView.swift */; };
		188222012CF65BA0005FB2C9 /* AddressModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 188222002CF65B9C005FB2C9 /* AddressModel.swift */; };
		188222032CF6723E005FB2C9 /* OrderSuccessView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 188222022CF67236005FB2C9 /* OrderSuccessView.swift */; };
		18925D8D2DEB2E42003ED506 /* Dev.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 18925D8C2DEB2E3F003ED506 /* Dev.xcconfig */; };
		18925D8F2DEB2E50003ED506 /* Prod.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 18925D8E2DEB2E4D003ED506 /* Prod.xcconfig */; };
		189ABE7E2D25BED700234B5D /* GenericPaginatedView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 189ABE7D2D25BED000234B5D /* GenericPaginatedView.swift */; };
		189ABE802D25BEDF00234B5D /* GenericPaginatedViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 189ABE7F2D25BEDE00234B5D /* GenericPaginatedViewModel.swift */; };
		189F9C9C2DE89D9E00191472 /* TapCheckoutView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 189F9C972DE89D9E00191472 /* TapCheckoutView.swift */; };
		189F9C9D2DE89D9E00191472 /* UIViewController+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 189F9C9A2DE89D9E00191472 /* UIViewController+Extensions.swift */; };
		189F9C9E2DE89D9E00191472 /* TapPaymentManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 189F9C992DE89D9E00191472 /* TapPaymentManager.swift */; };
		189F9C9F2DE89D9E00191472 /* TapPaymentConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = 189F9C982DE89D9E00191472 /* TapPaymentConfig.swift */; };
		189F9CA12DE8A2F100191472 /* TapPaymentPopupView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 189F9CA02DE8A2F100191472 /* TapPaymentPopupView.swift */; };
		189F9CB62DEA5D6C00191472 /* OrderFailureView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 189F9CB52DEA5D6500191472 /* OrderFailureView.swift */; };
		189F9CB82DEA759A00191472 /* LanguageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 189F9CB72DEA759900191472 /* LanguageView.swift */; };
		18A03B782D69C91900CD0DFC /* RXDetailsModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18A03B772D69C91200CD0DFC /* RXDetailsModel.swift */; };
		18A03B812D6B59A700CD0DFC /* NotificationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18A03B802D6B59A200CD0DFC /* NotificationView.swift */; };
		18A03B832D6B59F400CD0DFC /* NotificationViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18A03B822D6B59ED00CD0DFC /* NotificationViewModel.swift */; };
		18A7E8A62CBACAA5005D61C3 /* CheckoutModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18A7E8A52CBACA9E005D61C3 /* CheckoutModel.swift */; };
		18AE5D822D662A3B0049ED7A /* OrderStepperView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18AE5D812D662A390049ED7A /* OrderStepperView.swift */; };
		18B3186F2DB66FD3006539AF /* FirebaseMessaging in Frameworks */ = {isa = PBXBuildFile; productRef = 18B3186E2DB66FD3006539AF /* FirebaseMessaging */; };
		18B318722DB79877006539AF /* BrandListingTabContainerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18B318712DB79877006539AF /* BrandListingTabContainerView.swift */; };
		18B318742DB7988E006539AF /* BrandListingTabContainerViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18B318732DB7988E006539AF /* BrandListingTabContainerViewModel.swift */; };
		18B318762DB7A17F006539AF /* BrandListingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18B318752DB7A17F006539AF /* BrandListingView.swift */; };
		18B31ADD2D57815000471476 /* FilterSortViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18B31ADC2D57814F00471476 /* FilterSortViewModel.swift */; };
		18B31ADF2D57823400471476 /* DoubleExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18B31ADE2D57822D00471476 /* DoubleExtension.swift */; };
		18B31AEE2D57EFBA00471476 /* SDWebImageSVGCoder in Frameworks */ = {isa = PBXBuildFile; productRef = 18B31AED2D57EFBA00471476 /* SDWebImageSVGCoder */; };
		18B31AF02D57F75700471476 /* SideMenu.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18B31AEF2D57F75600471476 /* SideMenu.swift */; };
		18B31AF22D58078400471476 /* RichTextView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18B31AF12D58078200471476 /* RichTextView.swift */; };
		18B31AF62D5807B700471476 /* RichText in Frameworks */ = {isa = PBXBuildFile; productRef = 18B31AF52D5807B700471476 /* RichText */; };
		18B5CA6D2CA06F8400EA811B /* MainView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18B5CA6C2CA06F8300EA811B /* MainView.swift */; };
		18C2A5C42D67CA3C001AE292 /* RxDetailsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18C2A5C32D67CA36001AE292 /* RxDetailsView.swift */; };
		18C2A5C62D67CA66001AE292 /* RxDetailsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18C2A5C52D67CA5E001AE292 /* RxDetailsViewModel.swift */; };
		18C2A5C82D67CB31001AE292 /* RxProductCard.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18C2A5C72D67CB25001AE292 /* RxProductCard.swift */; };
		18C4580B2D613A4800EA8FF6 /* WishlistCellView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18C4580A2D613A4000EA8FF6 /* WishlistCellView.swift */; };
		18C4580E2D613AC600EA8FF6 /* WishlistViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18C4580D2D613AC000EA8FF6 /* WishlistViewModel.swift */; };
		18D1706B2DC233A4009E0CED /* IntegerExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18D1706A2DC2339C009E0CED /* IntegerExtension.swift */; };
		18D170A62DC3B11C009E0CED /* Localizable.xcstrings in Resources */ = {isa = PBXBuildFile; fileRef = 18D170A52DC3B11C009E0CED /* Localizable.xcstrings */; };
		18DBB1E22C9D845C00C41BF0 /* MainScrollBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18DBB1E12C9D845A00C41BF0 /* MainScrollBody.swift */; };
		18DBB1E62C9D84DD00C41BF0 /* RouterManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18DBB1E52C9D84DB00C41BF0 /* RouterManager.swift */; };
		18DBB1E92C9D854200C41BF0 /* AppState.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18DBB1E82C9D853F00C41BF0 /* AppState.swift */; };
		18DBB1EC2C9D881D00C41BF0 /* DashbordModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18DBB1EB2C9D881600C41BF0 /* DashbordModel.swift */; };
		18DBB1EE2C9D88F500C41BF0 /* Route.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18DBB1ED2C9D88F300C41BF0 /* Route.swift */; };
		18DBB1F02C9D8B5900C41BF0 /* StringExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18DBB1EF2C9D8B5400C41BF0 /* StringExtension.swift */; };
		18DBB1F22C9D8CDD00C41BF0 /* DashboardViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18DBB1F12C9D8CDC00C41BF0 /* DashboardViewModel.swift */; };
		18DBB1F52C9D8D7500C41BF0 /* TabExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18DBB1F42C9D8D7300C41BF0 /* TabExtension.swift */; };
		18E2DDF82D28757600674FE6 /* ExpandableView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18E2DDF72D28757000674FE6 /* ExpandableView.swift */; };
		18ECBB582D9E8C9A0016358A /* PoppinsLight.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 18ECBB572D9E8C9A0016358A /* PoppinsLight.ttf */; };
		18ECBB5B2D9E8D9A0016358A /* WrappingHStack in Frameworks */ = {isa = PBXBuildFile; productRef = 18ECBB5A2D9E8D9A0016358A /* WrappingHStack */; };
		18ECBB5E2D9E8F580016358A /* Sliders in Frameworks */ = {isa = PBXBuildFile; productRef = 18ECBB5D2D9E8F580016358A /* Sliders */; };
		18ECBB602DA071250016358A /* SampleCheckout.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18ECBB5F2DA0711D0016358A /* SampleCheckout.swift */; };
		18EED28F2C958F6300FFBCCA /* FSCalendar in Frameworks */ = {isa = PBXBuildFile; productRef = 18EED28E2C958F6300FFBCCA /* FSCalendar */; };
		18EED2922C958FAE00FFBCCA /* FSPagerView in Frameworks */ = {isa = PBXBuildFile; productRef = 18EED2912C958FAE00FFBCCA /* FSPagerView */; };
		18EED2952C95903900FFBCCA /* Alamofire in Frameworks */ = {isa = PBXBuildFile; productRef = 18EED2942C95903900FFBCCA /* Alamofire */; };
		18F735D72CCD160700F68F62 /* TargetType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F735D62CCD160400F68F62 /* TargetType.swift */; };
		18F735DB2CCD164600F68F62 /* UserDefaults.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F735DA2CCD164500F68F62 /* UserDefaults.swift */; };
		18F735DE2CCD165800F68F62 /* SecureDefaults in Frameworks */ = {isa = PBXBuildFile; productRef = 18F735DD2CCD165800F68F62 /* SecureDefaults */; };
		18F735E02CCD171500F68F62 /* BaseAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F735DF2CCD171400F68F62 /* BaseAPI.swift */; };
		18F735E32CCD173000F68F62 /* SwiftyJSON in Frameworks */ = {isa = PBXBuildFile; productRef = 18F735E22CCD173000F68F62 /* SwiftyJSON */; };
		18F735E52CCD175E00F68F62 /* DateExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F735E42CCD175800F68F62 /* DateExtension.swift */; };
		18F735E72CCD178400F68F62 /* DataExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F735E62CCD177F00F68F62 /* DataExtension.swift */; };
		18F735EA2CCD182600F68F62 /* APIEndPoints.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F735E92CCD182500F68F62 /* APIEndPoints.swift */; };
		18F735EC2CCD19F500F68F62 /* RepositoriesAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F735EB2CCD19F200F68F62 /* RepositoriesAPI.swift */; };
		18F735EE2CCD1A0100F68F62 /* RepositoriesNetworking.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F735ED2CCD19FF00F68F62 /* RepositoriesNetworking.swift */; };
		18F735F22CCE873B00F68F62 /* GenerateTokenModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F735F12CCE873100F68F62 /* GenerateTokenModel.swift */; };
		18F735F42CCE874B00F68F62 /* homePageModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F735F32CCE874400F68F62 /* homePageModel.swift */; };
		18F735F62CCE8B5800F68F62 /* WishlistModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F735F52CCE8B5100F68F62 /* WishlistModel.swift */; };
		18F735F82CCE8B8B00F68F62 /* SignUpModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F735F72CCE8B8600F68F62 /* SignUpModel.swift */; };
		18F735FC2CCEA9DC00F68F62 /* SuperViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F735FB2CCEA9DB00F68F62 /* SuperViewModel.swift */; };
		18F735FE2CCEAA1A00F68F62 /* SuperModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F735FD2CCEAA1200F68F62 /* SuperModel.swift */; };
		18F736002CCEAC6000F68F62 /* SuperView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F735FF2CCEAC5F00F68F62 /* SuperView.swift */; };
		18F736052CCEACC900F68F62 /* ActivityLoaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F736042CCEACC800F68F62 /* ActivityLoaderView.swift */; };
		18F736082CCEAD0000F68F62 /* AlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F736072CCEACFF00F68F62 /* AlertView.swift */; };
		18F7360A2CCEAE9C00F68F62 /* MainViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F736092CCEAE9C00F68F62 /* MainViewModel.swift */; };
		18F7360E2CCED94A00F68F62 /* NetworkImageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F7360D2CCED94900F68F62 /* NetworkImageView.swift */; };
		18F736172CCEE6FA00F68F62 /* SDWebImageSVGKitPlugin in Frameworks */ = {isa = PBXBuildFile; productRef = 18F736162CCEE6FA00F68F62 /* SDWebImageSVGKitPlugin */; };
		1CC4C0E647725361D3A7639B /* RobotoRomanRegular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 250156E9118521990D94D22E /* RobotoRomanRegular.ttf */; };
		1E987EBCCC68A80112478AF2 /* BeautycareCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = A62465835C7419AB7C854D1F /* BeautycareCell.swift */; };
		1F238975D6DBA68DA0BAB620 /* InterMedium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 2B18BF27D29E8F514C4B5E80 /* InterMedium.ttf */; };
		20929CADD630D98334856C94 /* CalendarModuleView.swift in Sources */ = {isa = PBXBuildFile; fileRef = D9100447B09160510177AC88 /* CalendarModuleView.swift */; };
		20E1F667D96C3568FFC07006 /* CheckoutViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = B4F4D98D72B53E42A4904A72 /* CheckoutViewModel.swift */; };
		215F9F084FEC6D38E5EA6BDD /* DatechooseView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 51AE3E001B30ADEB3AFEB48B /* DatechooseView.swift */; };
		2631853A54E05ECDBA5E4DAB /* TabsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A1EDB243A72AED75A238116 /* TabsView.swift */; };
		27F59F6BB01A3ED76BCC81DF /* ProductpageTabContainerViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A3A135E8A750529E0180BED6 /* ProductpageTabContainerViewModel.swift */; };
		2AEAB277F5353C7089BABA9E /* CategoryView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 626C8D3E9DF9292C27DF923C /* CategoryView.swift */; };
		2C3C8A8E172562422022DDE2 /* ColorConstants.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0281322832DA8198C3123B14 /* ColorConstants.swift */; };
		2E4C71A23B8C733973D63012 /* CategoryOneView.swift in Sources */ = {isa = PBXBuildFile; fileRef = D22CE6C9721C77E2C43C0DEC /* CategoryOneView.swift */; };
		30D08C90D9F5EE6E08E14B81 /* DatechooseViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6E8559E21CBD0F11C493A429 /* DatechooseViewModel.swift */; };
		31C78C2DE3AB1748732EA906 /* DeliverySectionCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F17640F9E8C4A6B69E2D7D6E /* DeliverySectionCell.swift */; };
		35F85B950AC2C5EEDB238066 /* Productcard1Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 859931B0FA198E72AC88A2BC /* Productcard1Cell.swift */; };
		35FC44CFFE9E8472658F340B /* OTPTextField.swift in Sources */ = {isa = PBXBuildFile; fileRef = 53A5F926836ADAAB2534A7FB /* OTPTextField.swift */; };
		3A591CFBE6BCE7D9C9FD7B14 /* SplashscreenView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 82690688ED928EEC3ADE999C /* SplashscreenView.swift */; };
		3C12AFC23FF328294F2003DF /* AccountSettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 84EEA9E58F23C2FA3D5255E7 /* AccountSettingsView.swift */; };
		3C8113748506E3024B5FF165 /* MenuViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 76A43417F7B920B8B7A83C87 /* MenuViewModel.swift */; };
		3CC020799080F9C9F2005A4E /* PageIndicator.swift in Sources */ = {isa = PBXBuildFile; fileRef = C40A6ABB225AE80BEAFC8CDD /* PageIndicator.swift */; };
		3D1F9989842E41212C229CF4 /* Productcard2Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = D084476EF5B36817F94AEF35 /* Productcard2Cell.swift */; };
		3D6AFF4BD3DBD3BB7AC504CE /* NunitoBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9B4787EDAC33DB4430DB0189 /* NunitoBold.ttf */; };
		40F43135AB731F4F33AEFD3E /* Pods_Wasfa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FEE84AEE9E6B402CDEE5750A /* Pods_Wasfa.framework */; };
		4463315CEFCE3748D6EE7C2B /* ProductDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1F81D3E1AD52CF47572FC7DC /* ProductDetailView.swift */; };
		493C2E7775B23433C9024EEC /* FSPageControlSUI.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D8CE4AF0B271CD421198D63 /* FSPageControlSUI.swift */; };
		4BAC3AB0B84981BD7FE2605C /* OTPFieldView.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* OTPFieldView.swift */; };
		4BFB9ABCF21BF30A541847AC /* NunitoLight.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BF6648BFCB22F3C759FDD7AA /* NunitoLight.ttf */; };
		4C4521BF346E2369BE9D19B3 /* CartView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 165EF7059588AF8F2E04133A /* CartView.swift */; };
		51AA30F345B43119BC63C2BE /* HomeViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = E75C211D8205BB0CB58ECD6A /* HomeViewModel.swift */; };
		617CABAF51E2E04AE6162FDA /* CategoryOneViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 90CD32E91BBE28299CE81595 /* CategoryOneViewModel.swift */; };
		646F838E326923AE33AD0112 /* OrderHistoryTabContainerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = EBAC41947D302532C5C27BA7 /* OrderHistoryTabContainerView.swift */; };
		688F515FA0453570AE83BBF8 /* PaymentMethodCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 17BF202F060531C516A72317 /* PaymentMethodCell.swift */; };
		6A448F67DB51D5E299B630F0 /* RobotoRomanLight.ttf in Resources */ = {isa = PBXBuildFile; fileRef = A49A84E18DA7E9FF2C5F8B79 /* RobotoRomanLight.ttf */; };
		6F2393F5A45A4B0105DF743D /* CheckoutView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 74386B1B71C1C565697B19FD /* CheckoutView.swift */; };
		6FFC1821B870FD36F01B863C /* TrackOrderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C8EDFDF46F6432EE189220BA /* TrackOrderView.swift */; };
		7220C121984C1EB93EB69C08 /* OrderProductCard.swift in Sources */ = {isa = PBXBuildFile; fileRef = EEA8355B63737F9DC69E1B41 /* OrderProductCard.swift */; };
		7711A4C4339F52FE128CBE49 /* SignInView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E5B07E98079E47AD876B4E1A /* SignInView.swift */; };
		78CDE4867E64D7DF60FF9121 /* ProductBrandCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = D603BDB8035B45A30C75C55E /* ProductBrandCell.swift */; };
		7B465BEFCB824C5046D62C42 /* BottomSheetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9A491F9E022A0009EBC023C0 /* BottomSheetView.swift */; };
		7C2B49D8D7BCFA5238104E95 /* Productcard3Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3C645BFAC946664102274441 /* Productcard3Cell.swift */; };
		7E17AD94070324249B0B6AEE /* DeliveryAddressViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = E0D403ED2809BBCDD8F73B75 /* DeliveryAddressViewModel.swift */; };
		7E91E1C894A500D06F4C4C05 /* ProductcardCartCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = C3AD2860630890974C698D61 /* ProductcardCartCell.swift */; };
		7EAF2FF95D0EDA172C845DF5 /* Forgotpassword2View.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22359425D8166C900F4AFEB1 /* Forgotpassword2View.swift */; };
		7F44DBA127422A5D00171570 /* ActivityLoader.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7F44DBA027422A5D00171570 /* ActivityLoader.swift */; };
		7FD16F2998F949A487F36C00 /* OrderhistorycancelledView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3FEF84CE717A70FEB8CE1C1D /* OrderhistorycancelledView.swift */; };
		8043024A0A398519479C301D /* RobotoRomanMedium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 719752489C153D1D2A88FBEB /* RobotoRomanMedium.ttf */; };
		8150C84049C4A21DCE5838E1 /* ChangepasswordViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = BC87155C63FF6DEC0F37277E /* ChangepasswordViewModel.swift */; };
		81F7DFA4C5CA928F36FB0986 /* SignupViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9AC12F9D42E70649004BB7D6 /* SignupViewModel.swift */; };
		82895C112F75B1D1FABA45F1 /* PlusJakartaSansRomanBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D86F707416658EE7F94DA08E /* PlusJakartaSansRomanBold.ttf */; };
		82D1AC340391CE43FD68D4CB /* DeliveryaddressaddView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 020E3107BA54432D1805F1C1 /* DeliveryaddressaddView.swift */; };
		836C29860E9901E815275C1C /* OrderHistoryCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5F33F14448AAC0CCA05E0AE5 /* OrderHistoryCell.swift */; };
		8417A308917650FC141BEE92 /* OTPReprasentable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 46A92342DBB5ED41F52BBDA2 /* OTPReprasentable.swift */; };
		84B66014EAFD53250EC5A01E /* DeviceListCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6CB7705312B7FD19A98C1F8F /* DeviceListCell.swift */; };
		880771E41F55E883CEEB44BB /* AddaddressView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 075B17679140BF013294B4E0 /* AddaddressView.swift */; };
		884F3E5127B0E65A00963FC4 /* RoundedCornersView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 884F3E5027B0E65A00963FC4 /* RoundedCornersView.swift */; };
		8853052627718DC500B04E6F /* ViewExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8853052527718DC500B04E6F /* ViewExtension.swift */; };
		8853052A27718E2D00B04E6F /* EncodableExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8853052927718E2D00B04E6F /* EncodableExtension.swift */; };
		8853052C2771949600B04E6F /* UINavigationController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8853052B2771949600B04E6F /* UINavigationController.swift */; };
		88680D1C2775C601002E964F /* ViewportHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 88680D1B2775C601002E964F /* ViewportHelper.swift */; };
		888CB47527686A000041116C /* APIExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 888CB47427686A000041116C /* APIExtensions.swift */; };
		88B333623800EAC1FDECC4A2 /* DeliveryaddressaddViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0AB6AA6A279F0427F6ACF0C7 /* DeliveryaddressaddViewModel.swift */; };
		88ED74EC272FFE6F0088E3EF /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 88ED74EB272FFE6F0088E3EF /* Assets.xcassets */; };
		88ED74EF272FFE6F0088E3EF /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 88ED74EE272FFE6F0088E3EF /* Preview Assets.xcassets */; };
		88ED7500272FFECA0088E3EF /* Utilities.swift in Sources */ = {isa = PBXBuildFile; fileRef = 88ED74F7272FFECA0088E3EF /* Utilities.swift */; };
		88ED7501272FFECA0088E3EF /* AppConstants.swift in Sources */ = {isa = PBXBuildFile; fileRef = 88ED74F9272FFECA0088E3EF /* AppConstants.swift */; };
		88ED7503272FFECA0088E3EF /* APIManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 88ED74FD272FFECA0088E3EF /* APIManager.swift */; };
		88ED7504272FFECA0088E3EF /* WasfaApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 88ED74FF272FFECA0088E3EF /* WasfaApp.swift */; };
		89E3AD60014ECA00AA92B96D /* OrderinfoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BCE5951963B5E18C6BFD7158 /* OrderinfoView.swift */; };
		8C5571CE8F944C5241A927F6 /* OrderhistorycompletedView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 71947EF1E94183995AE1BA4B /* OrderhistorycompletedView.swift */; };
		8C66E70C08E23160688AA28F /* ForgotpasswordTwoViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 574C2B57A1DA9E9D2AC57D4F /* ForgotpasswordTwoViewModel.swift */; };
		93F5021C27B897008C2FF55D /* FSPagerViewSUIOrigin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 63246D195B5E21309F7F6E09 /* FSPagerViewSUIOrigin.swift */; };
		97DFC2483288B894DFD38C02 /* AccountSettingsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D2C580EA5A649784D4566AD /* AccountSettingsViewModel.swift */; };
		9A59706C5607C2BC2EBECE8D /* OrderhistorycancelledViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4DD345A24E27FDBC0A9C764 /* OrderhistorycancelledViewModel.swift */; };
		9BBA0060EC30E3573D453EB2 /* ProductListingTabContainerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E9AEAE937F4A8FA53012AE4 /* ProductListingTabContainerView.swift */; };
		9EE53AA536F601B2E2362DCD /* Addressitem1Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* Addressitem1Cell.swift */; };
		9F1A5898E36A356F218AF9CE /* PasswordSucessView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7CCF94E4F3BC423646F33EAF /* PasswordSucessView.swift */; };
		A1552E0BF36235C7F4F195C5 /* MyAccountView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3917C355BC1C7DA1693896D2 /* MyAccountView.swift */; };
		A17F333FDDC83CDE80517EF1 /* Userprofile1Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 863D907C13E8D3CE49045921 /* Userprofile1Cell.swift */; };
		A8690BF1DC8F21ABE7973E57 /* RadioGroupButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8EBAC3CD23BB31C3C31C05D5 /* RadioGroupButton.swift */; };
		ACDC77D560235E989A87BE8A /* ProductpageViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8FB586222E2A9B60E44F6AA3 /* ProductpageViewModel.swift */; };
		************************ /* TrackorderOneViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4D7D14A12D9A8B07D067BAA /* TrackorderOneViewModel.swift */; };
		B0E48694141B67BFB52F9F49 /* ChangepasswordView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5327EDFCB8C650FBA9CB3BB4 /* ChangepasswordView.swift */; };
		B11EBAF289C08EE60A658C7E /* MenuView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1AAF68B22785451DE9827671 /* MenuView.swift */; };
		B2E4988EFC2450C1E1371232 /* CartViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = FEEC83E8541E4BD2170C6E95 /* CartViewModel.swift */; };
		B7B5283BB3122EF032CAB711 /* OrderHistoryTabContainerViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2FBC52EBF82A435AFB0ADDD2 /* OrderHistoryTabContainerViewModel.swift */; };
		BB186D0D942A0EF817D3754A /* ProductDetailViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = F38A735960E25CD821F6FAC7 /* ProductDetailViewModel.swift */; };
		BC59814AA2656A8A602DCD10 /* TrackorderOneView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 91946767B5F35106036B5AA2 /* TrackorderOneView.swift */; };
		C3E1E4193F7A3A0802D03CE4 /* PasswordSucessViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 77671335D4D12EF51C115957 /* PasswordSucessViewModel.swift */; };
		C40AB9F82754D79E006BC5D7 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = C40AB9F72754D79E006BC5D7 /* AppDelegate.swift */; };
		C4ED323E83E0730B18199294 /* TrackorderViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2640FF755176F9B687C0AD69 /* TrackorderViewModel.swift */; };
		C71867BD89E0D1EC5585E5DD /* Forgotpassword2ViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6788D559B8964EB95B5A5988 /* Forgotpassword2ViewModel.swift */; };
		C810C84A073A9CFE559B8CA4 /* InterRegular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 1BEE0BCA34B58BADC2DFAB60 /* InterRegular.ttf */; };
		CAF71292179972BFF6FB75AB /* PagerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1F642E71D0C23D0FE26C33D7 /* PagerView.swift */; };
		************************ /* CategoryViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = C6BE93C21C9E031B6572511C /* CategoryViewModel.swift */; };
		D0B6DF7F327E1639084D0BAE /* OrderinfoViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 931DB24A50E3F606F2D696F2 /* OrderinfoViewModel.swift */; };
		D48A055C563F4D19CEE47A6D /* OrderHistoryView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3C8E86E953F0E84B2AEA14B0 /* OrderHistoryView.swift */; };
		DBBAF17AA379F9E7A5C19807 /* StringScheme.swift in Sources */ = {isa = PBXBuildFile; fileRef = E22D942B72847B3BF3B91911 /* StringScheme.swift */; };
		DE1C45A69F48E837A5D1071F /* CategoryItemCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 50247C4B51CB6487D1C0657E /* CategoryItemCell.swift */; };
		DE5A4446C7DD0868A8812E12 /* NunitoRegular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 22D0F9339EA8063E6FDB4C36 /* NunitoRegular.ttf */; };
		DF89A93DDDC9300DAFD3DCDD /* FSPagerViewSUI.swift in Sources */ = {isa = PBXBuildFile; fileRef = 14F0712BA907035D7D022DB1 /* FSPagerViewSUI.swift */; };
		E092BF8719BF60100134D33E /* AddaddressViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = C728A6BF8B79179D16146D0B /* AddaddressViewModel.swift */; };
		E57C4B16D271189044040C7A /* NunitoSemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BE393313FCF9CE797728A7A5 /* NunitoSemiBold.ttf */; };
		E73A96FAB123CD6EEECB1553 /* NunitoMedium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F10432B3F69C43FFB1368E96 /* NunitoMedium.ttf */; };
		EA1821672CD03E52254998D1 /* DeliveryAddressView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE1285E860102A568F411C4F /* DeliveryAddressView.swift */; };
		EF72B9FF93605E008C13BF63 /* ForgotpasswordViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 03D71EBFE9B252B58BEB0941 /* ForgotpasswordViewModel.swift */; };
		EFE272F19D3DB4566BD7A99F /* HomeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0CC1D21C5BD8BB6DBA2734FB /* HomeView.swift */; };
		F12384D4C83B4AE5FF6E8E68 /* SignInViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = F6385B03CC7A0E02CA91F4DE /* SignInViewModel.swift */; };
		F207954CBC74188F9F564E23 /* ProductListingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 666FC2DA60FA482E27312D30 /* ProductListingView.swift */; };
		F2F8986DBE817FB4E082B664 /* Productcard4Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 196C9CBCAFE738B3B883EDD5 /* Productcard4Cell.swift */; };
		F4FFEB7CD6B55033E0E726B3 /* FontScheme.swift in Sources */ = {isa = PBXBuildFile; fileRef = C28310FD416BE67238C4CDEC /* FontScheme.swift */; };
		F9BDD0A871DDB68E02F0D0A1 /* ForgotpasswordView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7940193B20956A15CFB72960 /* ForgotpasswordView.swift */; };
		F9C2A3D520D89E894659A3B7 /* SignupView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E0987F85AAF7E3A29C48E35 /* SignupView.swift */; };
		FA1BC6C9AAABFD0B94B8D378 /* Userprofile3Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56CB11FD0889722F2B94A695 /* Userprofile3Cell.swift */; };
		FE4AAFAAFA12161D185E7E0D /* NunitoExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9E9DBB638FB481410EFAF382 /* NunitoExtraBold.ttf */; };
		FFFB6EACE98AF9B45BDF6399 /* OTPView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3ABF169E30CC672F53577818 /* OTPView.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		01E0AD9CBA71FEB4461FE5FC /* StackCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = StackCell.swift; sourceTree = "<group>"; };
		020E3107BA54432D1805F1C1 /* DeliveryaddressaddView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = DeliveryaddressaddView.swift; sourceTree = "<group>"; };
		0281322832DA8198C3123B14 /* ColorConstants.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ColorConstants.swift; sourceTree = "<group>"; };
		03D71EBFE9B252B58BEB0941 /* ForgotpasswordViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ForgotpasswordViewModel.swift; sourceTree = "<group>"; };
		075B17679140BF013294B4E0 /* AddaddressView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = AddaddressView.swift; sourceTree = "<group>"; };
		0AB6AA6A279F0427F6ACF0C7 /* DeliveryaddressaddViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = DeliveryaddressaddViewModel.swift; sourceTree = "<group>"; };
		0CC1D21C5BD8BB6DBA2734FB /* HomeView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = HomeView.swift; sourceTree = "<group>"; };
		14F0712BA907035D7D022DB1 /* FSPagerViewSUI.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = FSPagerViewSUI.swift; sourceTree = "<group>"; };
		165EF7059588AF8F2E04133A /* CartView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = CartView.swift; sourceTree = "<group>"; };
		17BF202F060531C516A72317 /* PaymentMethodCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = PaymentMethodCell.swift; sourceTree = "<group>"; };
		180694482D6365FA0094FDFA /* ToastView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ToastView.swift; sourceTree = "<group>"; };
		180694492D6365FA0094FDFA /* ToastModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ToastModel.swift; sourceTree = "<group>"; };
		1806944A2D6365FA0094FDFA /* ToastModifier.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ToastModifier.swift; sourceTree = "<group>"; };
		180694552D63DE720094FDFA /* ContactUsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContactUsView.swift; sourceTree = "<group>"; };
		180694572D63DF7C0094FDFA /* ContactUsViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContactUsViewModel.swift; sourceTree = "<group>"; };
		1806945D2D63EA9F0094FDFA /* AboutUsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AboutUsView.swift; sourceTree = "<group>"; };
		1806945F2D63EABC0094FDFA /* AboutUsViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AboutUsViewModel.swift; sourceTree = "<group>"; };
		180EFEF12DF96546005B3693 /* RxHistoryView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RxHistoryView.swift; sourceTree = "<group>"; };
		181B61262D53B90B009570AC /* FilterSortView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FilterSortView.swift; sourceTree = "<group>"; };
		181B61282D54219E009570AC /* ProductCategoryCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProductCategoryCell.swift; sourceTree = "<group>"; };
		181E38422CA083F300780921 /* WishlistView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WishlistView.swift; sourceTree = "<group>"; };
		1827998C2DB90FA900E275E8 /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		1827998E2DB9113300E275E8 /* Message.apns */ = {isa = PBXFileReference; lastKnownFileType = text; path = Message.apns; sourceTree = "<group>"; };
		185CC6452D9C0EE60014074F /* PoppinsMedium.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = PoppinsMedium.ttf; sourceTree = "<group>"; };
		185CC6472D9C162B0014074F /* PoppinsBold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = PoppinsBold.ttf; sourceTree = "<group>"; };
		185CC6492D9D4EC50014074F /* PoppinsRegular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = PoppinsRegular.ttf; sourceTree = "<group>"; };
		185CC64B2D9D4F7C0014074F /* PoppinsSemiBold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = PoppinsSemiBold.ttf; sourceTree = "<group>"; };
		186BC7812C987DB900DA5131 /* DashboardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DashboardView.swift; sourceTree = "<group>"; };
		187FEED42CD67A2900EAC51C /* ProductDetailsModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProductDetailsModel.swift; sourceTree = "<group>"; };
		188221EA2CF396B0005FB2C9 /* ColorExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ColorExtension.swift; sourceTree = "<group>"; };
		188221EC2CF39760005FB2C9 /* ProductListingModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProductListingModel.swift; sourceTree = "<group>"; };
		188221F32CF52834005FB2C9 /* PaginatedViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PaginatedViewModel.swift; sourceTree = "<group>"; };
		188221FB2CF6336E005FB2C9 /* CalendarView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CalendarView.swift; sourceTree = "<group>"; };
		188221FD2CF641B3005FB2C9 /* AddressPopUpView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddressPopUpView.swift; sourceTree = "<group>"; };
		188222002CF65B9C005FB2C9 /* AddressModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddressModel.swift; sourceTree = "<group>"; };
		188222022CF67236005FB2C9 /* OrderSuccessView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OrderSuccessView.swift; sourceTree = "<group>"; };
		18925D8C2DEB2E3F003ED506 /* Dev.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = Dev.xcconfig; sourceTree = "<group>"; };
		18925D8E2DEB2E4D003ED506 /* Prod.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = Prod.xcconfig; sourceTree = "<group>"; };
		189ABE7D2D25BED000234B5D /* GenericPaginatedView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GenericPaginatedView.swift; sourceTree = "<group>"; };
		189ABE7F2D25BEDE00234B5D /* GenericPaginatedViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GenericPaginatedViewModel.swift; sourceTree = "<group>"; };
		189F9C972DE89D9E00191472 /* TapCheckoutView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TapCheckoutView.swift; sourceTree = "<group>"; };
		189F9C982DE89D9E00191472 /* TapPaymentConfig.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TapPaymentConfig.swift; sourceTree = "<group>"; };
		189F9C992DE89D9E00191472 /* TapPaymentManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TapPaymentManager.swift; sourceTree = "<group>"; };
		189F9C9A2DE89D9E00191472 /* UIViewController+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIViewController+Extensions.swift"; sourceTree = "<group>"; };
		189F9CA02DE8A2F100191472 /* TapPaymentPopupView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TapPaymentPopupView.swift; sourceTree = "<group>"; };
		189F9CB52DEA5D6500191472 /* OrderFailureView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OrderFailureView.swift; sourceTree = "<group>"; };
		189F9CB72DEA759900191472 /* LanguageView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LanguageView.swift; sourceTree = "<group>"; };
		18A03B772D69C91200CD0DFC /* RXDetailsModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RXDetailsModel.swift; sourceTree = "<group>"; };
		18A03B802D6B59A200CD0DFC /* NotificationView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationView.swift; sourceTree = "<group>"; };
		18A03B822D6B59ED00CD0DFC /* NotificationViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationViewModel.swift; sourceTree = "<group>"; };
		18A7E8A52CBACA9E005D61C3 /* CheckoutModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CheckoutModel.swift; sourceTree = "<group>"; };
		18AE5D812D662A390049ED7A /* OrderStepperView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OrderStepperView.swift; sourceTree = "<group>"; };
		18B318712DB79877006539AF /* BrandListingTabContainerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BrandListingTabContainerView.swift; sourceTree = "<group>"; };
		18B318732DB7988E006539AF /* BrandListingTabContainerViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BrandListingTabContainerViewModel.swift; sourceTree = "<group>"; };
		18B318752DB7A17F006539AF /* BrandListingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BrandListingView.swift; sourceTree = "<group>"; };
		18B318792DB90EEF006539AF /* Wasfa.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = Wasfa.entitlements; sourceTree = "<group>"; };
		18B31ADC2D57814F00471476 /* FilterSortViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FilterSortViewModel.swift; sourceTree = "<group>"; };
		18B31ADE2D57822D00471476 /* DoubleExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DoubleExtension.swift; sourceTree = "<group>"; };
		18B31AEF2D57F75600471476 /* SideMenu.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SideMenu.swift; sourceTree = "<group>"; };
		18B31AF12D58078200471476 /* RichTextView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RichTextView.swift; sourceTree = "<group>"; };
		18B5CA6C2CA06F8300EA811B /* MainView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainView.swift; sourceTree = "<group>"; };
		18C2A5C32D67CA36001AE292 /* RxDetailsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RxDetailsView.swift; sourceTree = "<group>"; };
		18C2A5C52D67CA5E001AE292 /* RxDetailsViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RxDetailsViewModel.swift; sourceTree = "<group>"; };
		18C2A5C72D67CB25001AE292 /* RxProductCard.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RxProductCard.swift; sourceTree = "<group>"; };
		18C4580A2D613A4000EA8FF6 /* WishlistCellView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WishlistCellView.swift; sourceTree = "<group>"; };
		18C4580D2D613AC000EA8FF6 /* WishlistViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WishlistViewModel.swift; sourceTree = "<group>"; };
		18D1706A2DC2339C009E0CED /* IntegerExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IntegerExtension.swift; sourceTree = "<group>"; };
		18D170A52DC3B11C009E0CED /* Localizable.xcstrings */ = {isa = PBXFileReference; lastKnownFileType = text.json.xcstrings; path = Localizable.xcstrings; sourceTree = "<group>"; };
		18DBB1E12C9D845A00C41BF0 /* MainScrollBody.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainScrollBody.swift; sourceTree = "<group>"; };
		18DBB1E52C9D84DB00C41BF0 /* RouterManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RouterManager.swift; sourceTree = "<group>"; };
		18DBB1E82C9D853F00C41BF0 /* AppState.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppState.swift; sourceTree = "<group>"; };
		18DBB1EB2C9D881600C41BF0 /* DashbordModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DashbordModel.swift; sourceTree = "<group>"; };
		18DBB1ED2C9D88F300C41BF0 /* Route.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Route.swift; sourceTree = "<group>"; };
		18DBB1EF2C9D8B5400C41BF0 /* StringExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StringExtension.swift; sourceTree = "<group>"; };
		18DBB1F12C9D8CDC00C41BF0 /* DashboardViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DashboardViewModel.swift; sourceTree = "<group>"; };
		18DBB1F42C9D8D7300C41BF0 /* TabExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TabExtension.swift; sourceTree = "<group>"; };
		18E2DDF72D28757000674FE6 /* ExpandableView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExpandableView.swift; sourceTree = "<group>"; };
		18ECBB572D9E8C9A0016358A /* PoppinsLight.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = PoppinsLight.ttf; sourceTree = "<group>"; };
		18ECBB5F2DA0711D0016358A /* SampleCheckout.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SampleCheckout.swift; sourceTree = "<group>"; };
		18F735D62CCD160400F68F62 /* TargetType.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TargetType.swift; sourceTree = "<group>"; };
		18F735DA2CCD164500F68F62 /* UserDefaults.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserDefaults.swift; sourceTree = "<group>"; };
		18F735DF2CCD171400F68F62 /* BaseAPI.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BaseAPI.swift; sourceTree = "<group>"; };
		18F735E42CCD175800F68F62 /* DateExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DateExtension.swift; sourceTree = "<group>"; };
		18F735E62CCD177F00F68F62 /* DataExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DataExtension.swift; sourceTree = "<group>"; };
		18F735E92CCD182500F68F62 /* APIEndPoints.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = APIEndPoints.swift; sourceTree = "<group>"; };
		18F735EB2CCD19F200F68F62 /* RepositoriesAPI.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RepositoriesAPI.swift; sourceTree = "<group>"; };
		18F735ED2CCD19FF00F68F62 /* RepositoriesNetworking.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RepositoriesNetworking.swift; sourceTree = "<group>"; };
		18F735F12CCE873100F68F62 /* GenerateTokenModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GenerateTokenModel.swift; sourceTree = "<group>"; };
		18F735F32CCE874400F68F62 /* homePageModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = homePageModel.swift; sourceTree = "<group>"; };
		18F735F52CCE8B5100F68F62 /* WishlistModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WishlistModel.swift; sourceTree = "<group>"; };
		18F735F72CCE8B8600F68F62 /* SignUpModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SignUpModel.swift; sourceTree = "<group>"; };
		18F735FB2CCEA9DB00F68F62 /* SuperViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SuperViewModel.swift; sourceTree = "<group>"; };
		18F735FD2CCEAA1200F68F62 /* SuperModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SuperModel.swift; sourceTree = "<group>"; };
		18F735FF2CCEAC5F00F68F62 /* SuperView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SuperView.swift; sourceTree = "<group>"; };
		18F736042CCEACC800F68F62 /* ActivityLoaderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ActivityLoaderView.swift; sourceTree = "<group>"; };
		18F736072CCEACFF00F68F62 /* AlertView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AlertView.swift; sourceTree = "<group>"; };
		18F736092CCEAE9C00F68F62 /* MainViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainViewModel.swift; sourceTree = "<group>"; };
		18F7360D2CCED94900F68F62 /* NetworkImageView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NetworkImageView.swift; sourceTree = "<group>"; };
		196C9CBCAFE738B3B883EDD5 /* Productcard4Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Productcard4Cell.swift; sourceTree = "<group>"; };
		1AAF68B22785451DE9827671 /* MenuView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = MenuView.swift; sourceTree = "<group>"; };
		1BEE0BCA34B58BADC2DFAB60 /* InterRegular.ttf */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = file; path = InterRegular.ttf; sourceTree = "<group>"; };
		1D8CE4AF0B271CD421198D63 /* FSPageControlSUI.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = FSPageControlSUI.swift; sourceTree = "<group>"; };
		1F642E71D0C23D0FE26C33D7 /* PagerView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = PagerView.swift; sourceTree = "<group>"; };
		1F81D3E1AD52CF47572FC7DC /* ProductDetailView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ProductDetailView.swift; sourceTree = "<group>"; };
		22359425D8166C900F4AFEB1 /* Forgotpassword2View.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Forgotpassword2View.swift; sourceTree = "<group>"; };
		22D0F9339EA8063E6FDB4C36 /* NunitoRegular.ttf */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = file; path = NunitoRegular.ttf; sourceTree = "<group>"; };
		250156E9118521990D94D22E /* RobotoRomanRegular.ttf */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = file; path = RobotoRomanRegular.ttf; sourceTree = "<group>"; };
		2640FF755176F9B687C0AD69 /* TrackorderViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = TrackorderViewModel.swift; sourceTree = "<group>"; };
		2B18BF27D29E8F514C4B5E80 /* InterMedium.ttf */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = file; path = InterMedium.ttf; sourceTree = "<group>"; };
		2FBC52EBF82A435AFB0ADDD2 /* OrderHistoryTabContainerViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = OrderHistoryTabContainerViewModel.swift; sourceTree = "<group>"; };
		3917C355BC1C7DA1693896D2 /* MyAccountView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = MyAccountView.swift; sourceTree = "<group>"; };
		3ABF169E30CC672F53577818 /* OTPView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = OTPView.swift; sourceTree = "<group>"; };
		3C645BFAC946664102274441 /* Productcard3Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Productcard3Cell.swift; sourceTree = "<group>"; };
		3C8E86E953F0E84B2AEA14B0 /* OrderHistoryView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = OrderHistoryView.swift; sourceTree = "<group>"; };
		3EA704986E45580EBFD52160 /* Pods-Wasfa.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Wasfa.release.xcconfig"; path = "Target Support Files/Pods-Wasfa/Pods-Wasfa.release.xcconfig"; sourceTree = "<group>"; };
		3FEF84CE717A70FEB8CE1C1D /* OrderhistorycancelledView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = OrderhistorycancelledView.swift; sourceTree = "<group>"; };
		46A92342DBB5ED41F52BBDA2 /* OTPReprasentable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = OTPReprasentable.swift; sourceTree = "<group>"; };
		47DE2C55B95BBF79118D2DCA /* MyAccountViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = MyAccountViewModel.swift; sourceTree = "<group>"; };
		4D2C580EA5A649784D4566AD /* AccountSettingsViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = AccountSettingsViewModel.swift; sourceTree = "<group>"; };
		50247C4B51CB6487D1C0657E /* CategoryItemCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = CategoryItemCell.swift; sourceTree = "<group>"; };
		51AE3E001B30ADEB3AFEB48B /* DatechooseView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = DatechooseView.swift; sourceTree = "<group>"; };
		5327EDFCB8C650FBA9CB3BB4 /* ChangepasswordView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ChangepasswordView.swift; sourceTree = "<group>"; };
		53A5F926836ADAAB2534A7FB /* OTPTextField.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = OTPTextField.swift; sourceTree = "<group>"; };
		56CB11FD0889722F2B94A695 /* Userprofile3Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Userprofile3Cell.swift; sourceTree = "<group>"; };
		574C2B57A1DA9E9D2AC57D4F /* ForgotpasswordTwoViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ForgotpasswordTwoViewModel.swift; sourceTree = "<group>"; };
		58D96A51AEC17AD58FCEF048 /* ProductCardCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ProductCardCell.swift; sourceTree = "<group>"; };
		5F33F14448AAC0CCA05E0AE5 /* OrderHistoryCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = OrderHistoryCell.swift; sourceTree = "<group>"; };
		626C8D3E9DF9292C27DF923C /* CategoryView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = CategoryView.swift; sourceTree = "<group>"; };
		63246D195B5E21309F7F6E09 /* FSPagerViewSUIOrigin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = FSPagerViewSUIOrigin.swift; sourceTree = "<group>"; };
		666FC2DA60FA482E27312D30 /* ProductListingView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ProductListingView.swift; sourceTree = "<group>"; };
		6788D559B8964EB95B5A5988 /* Forgotpassword2ViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Forgotpassword2ViewModel.swift; sourceTree = "<group>"; };
		6CB7705312B7FD19A98C1F8F /* DeviceListCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = DeviceListCell.swift; sourceTree = "<group>"; };
		6E8559E21CBD0F11C493A429 /* DatechooseViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = DatechooseViewModel.swift; sourceTree = "<group>"; };
		71947EF1E94183995AE1BA4B /* OrderhistorycompletedView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = OrderhistorycompletedView.swift; sourceTree = "<group>"; };
		719752489C153D1D2A88FBEB /* RobotoRomanMedium.ttf */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = file; path = RobotoRomanMedium.ttf; sourceTree = "<group>"; };
		74386B1B71C1C565697B19FD /* CheckoutView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = CheckoutView.swift; sourceTree = "<group>"; };
		76A43417F7B920B8B7A83C87 /* MenuViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = MenuViewModel.swift; sourceTree = "<group>"; };
		77671335D4D12EF51C115957 /* PasswordSucessViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = PasswordSucessViewModel.swift; sourceTree = "<group>"; };
		7940193B20956A15CFB72960 /* ForgotpasswordView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ForgotpasswordView.swift; sourceTree = "<group>"; };
		7CCF94E4F3BC423646F33EAF /* PasswordSucessView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = PasswordSucessView.swift; sourceTree = "<group>"; };
		7E9AEAE937F4A8FA53012AE4 /* ProductListingTabContainerView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ProductListingTabContainerView.swift; sourceTree = "<group>"; };
		7F44DBA027422A5D00171570 /* ActivityLoader.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ActivityLoader.swift; sourceTree = "<group>"; };
		82690688ED928EEC3ADE999C /* SplashscreenView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = SplashscreenView.swift; sourceTree = "<group>"; };
		84EEA9E58F23C2FA3D5255E7 /* AccountSettingsView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = AccountSettingsView.swift; sourceTree = "<group>"; };
		859931B0FA198E72AC88A2BC /* Productcard1Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Productcard1Cell.swift; sourceTree = "<group>"; };
		863D907C13E8D3CE49045921 /* Userprofile1Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Userprofile1Cell.swift; sourceTree = "<group>"; };
		884F3E5027B0E65A00963FC4 /* RoundedCornersView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoundedCornersView.swift; sourceTree = "<group>"; };
		8853052527718DC500B04E6F /* ViewExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewExtension.swift; sourceTree = "<group>"; };
		8853052927718E2D00B04E6F /* EncodableExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EncodableExtension.swift; sourceTree = "<group>"; };
		8853052B2771949600B04E6F /* UINavigationController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UINavigationController.swift; sourceTree = "<group>"; };
		88680D1B2775C601002E964F /* ViewportHelper.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewportHelper.swift; sourceTree = "<group>"; };
		888CB47427686A000041116C /* APIExtensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = APIExtensions.swift; sourceTree = "<group>"; };
		88ED74E4272FFE6E0088E3EF /* Wasfa.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Wasfa.app; sourceTree = BUILT_PRODUCTS_DIR; };
		88ED74EB272FFE6F0088E3EF /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		88ED74EE272FFE6F0088E3EF /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		88ED74F0272FFE6F0088E3EF /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		88ED74F7272FFECA0088E3EF /* Utilities.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Utilities.swift; sourceTree = "<group>"; };
		88ED74F9272FFECA0088E3EF /* AppConstants.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppConstants.swift; sourceTree = "<group>"; };
		88ED74FD272FFECA0088E3EF /* APIManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = APIManager.swift; sourceTree = "<group>"; };
		88ED74FF272FFECA0088E3EF /* WasfaApp.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WasfaApp.swift; sourceTree = "<group>"; };
		8A1EDB243A72AED75A238116 /* TabsView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = TabsView.swift; sourceTree = "<group>"; };
		8E0987F85AAF7E3A29C48E35 /* SignupView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = SignupView.swift; sourceTree = "<group>"; };
		8EBAC3CD23BB31C3C31C05D5 /* RadioGroupButton.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = RadioGroupButton.swift; sourceTree = "<group>"; };
		8F82E945766BEFD862C3FAEC /* AddressCardView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = AddressCardView.swift; sourceTree = "<group>"; };
		8FB586222E2A9B60E44F6AA3 /* ProductpageViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ProductpageViewModel.swift; sourceTree = "<group>"; };
		90CD32E91BBE28299CE81595 /* CategoryOneViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = CategoryOneViewModel.swift; sourceTree = "<group>"; };
		91946767B5F35106036B5AA2 /* TrackorderOneView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = TrackorderOneView.swift; sourceTree = "<group>"; };
		931DB24A50E3F606F2D696F2 /* OrderinfoViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = OrderinfoViewModel.swift; sourceTree = "<group>"; };
		9A491F9E022A0009EBC023C0 /* BottomSheetView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = BottomSheetView.swift; sourceTree = "<group>"; };
		9AC12F9D42E70649004BB7D6 /* SignupViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = SignupViewModel.swift; sourceTree = "<group>"; };
		9B0AAEC205F089AD3093E4BC /* Productcard6Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Productcard6Cell.swift; sourceTree = "<group>"; };
		9B4787EDAC33DB4430DB0189 /* NunitoBold.ttf */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = file; path = NunitoBold.ttf; sourceTree = "<group>"; };
		9E9DBB638FB481410EFAF382 /* NunitoExtraBold.ttf */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = file; path = NunitoExtraBold.ttf; sourceTree = "<group>"; };
		A3A135E8A750529E0180BED6 /* ProductpageTabContainerViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ProductpageTabContainerViewModel.swift; sourceTree = "<group>"; };
		A49A84E18DA7E9FF2C5F8B79 /* RobotoRomanLight.ttf */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = file; path = RobotoRomanLight.ttf; sourceTree = "<group>"; };
		A62465835C7419AB7C854D1F /* BeautycareCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = BeautycareCell.swift; sourceTree = "<group>"; };
		A984103FD8219F53029FFF9E /* OrderhistoryViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = OrderhistoryViewModel.swift; sourceTree = "<group>"; };
		************************ /* OTPFieldView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = OTPFieldView.swift; sourceTree = "<group>"; };
		B4F4D98D72B53E42A4904A72 /* CheckoutViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = CheckoutViewModel.swift; sourceTree = "<group>"; };
		BC87155C63FF6DEC0F37277E /* ChangepasswordViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ChangepasswordViewModel.swift; sourceTree = "<group>"; };
		BCE5951963B5E18C6BFD7158 /* OrderinfoView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = OrderinfoView.swift; sourceTree = "<group>"; };
		BE393313FCF9CE797728A7A5 /* NunitoSemiBold.ttf */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = file; path = NunitoSemiBold.ttf; sourceTree = "<group>"; };
		BF6648BFCB22F3C759FDD7AA /* NunitoLight.ttf */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = file; path = NunitoLight.ttf; sourceTree = "<group>"; };
		C28310FD416BE67238C4CDEC /* FontScheme.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = FontScheme.swift; sourceTree = "<group>"; };
		C3AD2860630890974C698D61 /* ProductcardCartCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ProductcardCartCell.swift; sourceTree = "<group>"; };
		C40A6ABB225AE80BEAFC8CDD /* PageIndicator.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = PageIndicator.swift; sourceTree = "<group>"; };
		C40AB9F72754D79E006BC5D7 /* AppDelegate.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		C4A20AFD289680B853934BA6 /* SplashscreenViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = SplashscreenViewModel.swift; sourceTree = "<group>"; };
		C6BE93C21C9E031B6572511C /* CategoryViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = CategoryViewModel.swift; sourceTree = "<group>"; };
		C728A6BF8B79179D16146D0B /* AddaddressViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = AddaddressViewModel.swift; sourceTree = "<group>"; };
		C8EDFDF46F6432EE189220BA /* TrackOrderView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = TrackOrderView.swift; sourceTree = "<group>"; };
		C9E6FFC61F6B7E26030CB88F /* LowcateCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = LowcateCell.swift; sourceTree = "<group>"; };
		************************ /* Addressitem1Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Addressitem1Cell.swift; sourceTree = "<group>"; };
		D084476EF5B36817F94AEF35 /* Productcard2Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Productcard2Cell.swift; sourceTree = "<group>"; };
		D22CE6C9721C77E2C43C0DEC /* CategoryOneView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = CategoryOneView.swift; sourceTree = "<group>"; };
		D2A7DBE9BCEBB56E359D31B9 /* OrderhistorycompletedViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = OrderhistorycompletedViewModel.swift; sourceTree = "<group>"; };
		D603BDB8035B45A30C75C55E /* ProductBrandCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ProductBrandCell.swift; sourceTree = "<group>"; };
		D86F707416658EE7F94DA08E /* PlusJakartaSansRomanBold.ttf */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = file; path = PlusJakartaSansRomanBold.ttf; sourceTree = "<group>"; };
		D9100447B09160510177AC88 /* CalendarModuleView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = CalendarModuleView.swift; sourceTree = "<group>"; };
		DE1285E860102A568F411C4F /* DeliveryAddressView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = DeliveryAddressView.swift; sourceTree = "<group>"; };
		DF9A55322CBE29B17A2B52E9 /* Pods-Wasfa.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Wasfa.debug.xcconfig"; path = "Target Support Files/Pods-Wasfa/Pods-Wasfa.debug.xcconfig"; sourceTree = "<group>"; };
		E0D403ED2809BBCDD8F73B75 /* DeliveryAddressViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = DeliveryAddressViewModel.swift; sourceTree = "<group>"; };
		E22D942B72847B3BF3B91911 /* StringScheme.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = StringScheme.swift; sourceTree = "<group>"; };
		E4D7D14A12D9A8B07D067BAA /* TrackorderOneViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = TrackorderOneViewModel.swift; sourceTree = "<group>"; };
		E4DD345A24E27FDBC0A9C764 /* OrderhistorycancelledViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = OrderhistorycancelledViewModel.swift; sourceTree = "<group>"; };
		E5B07E98079E47AD876B4E1A /* SignInView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = SignInView.swift; sourceTree = "<group>"; };
		E75C211D8205BB0CB58ECD6A /* HomeViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = HomeViewModel.swift; sourceTree = "<group>"; };
		EBAC41947D302532C5C27BA7 /* OrderHistoryTabContainerView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = OrderHistoryTabContainerView.swift; sourceTree = "<group>"; };
		EEA8355B63737F9DC69E1B41 /* OrderProductCard.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = OrderProductCard.swift; sourceTree = "<group>"; };
		F10432B3F69C43FFB1368E96 /* NunitoMedium.ttf */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = file; path = NunitoMedium.ttf; sourceTree = "<group>"; };
		F17640F9E8C4A6B69E2D7D6E /* DeliverySectionCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = DeliverySectionCell.swift; sourceTree = "<group>"; };
		F38A735960E25CD821F6FAC7 /* ProductDetailViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ProductDetailViewModel.swift; sourceTree = "<group>"; };
		F6385B03CC7A0E02CA91F4DE /* SignInViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = SignInViewModel.swift; sourceTree = "<group>"; };
		FEE84AEE9E6B402CDEE5750A /* Pods_Wasfa.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Wasfa.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		FEEC83E8541E4BD2170C6E95 /* CartViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = CartViewModel.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		88ED74E1272FFE6E0088E3EF /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				187FEED02CD670B300EAC51C /* SDWebImageSwiftUI in Frameworks */,
				18EED2952C95903900FFBCCA /* Alamofire in Frameworks */,
				18B3186F2DB66FD3006539AF /* FirebaseMessaging in Frameworks */,
				18ECBB5E2D9E8F580016358A /* Sliders in Frameworks */,
				18F735DE2CCD165800F68F62 /* SecureDefaults in Frameworks */,
				18B31AF62D5807B700471476 /* RichText in Frameworks */,
				18ECBB5B2D9E8D9A0016358A /* WrappingHStack in Frameworks */,
				18B31AEE2D57EFBA00471476 /* SDWebImageSVGCoder in Frameworks */,
				18EED2922C958FAE00FFBCCA /* FSPagerView in Frameworks */,
				18F735E32CCD173000F68F62 /* SwiftyJSON in Frameworks */,
				18F736172CCEE6FA00F68F62 /* SDWebImageSVGKitPlugin in Frameworks */,
				187FEED32CD6729500EAC51C /* SDWebImageWebPCoder in Frameworks */,
				18EED28F2C958F6300FFBCCA /* FSCalendar in Frameworks */,
				180694502D63664D0094FDFA /* SwipeActions in Frameworks */,
				40F43135AB731F4F33AEFD3E /* Pods_Wasfa.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		077584CE75FE746FA6C5C0F5 /* Cell */ = {
			isa = PBXGroup;
			children = (
				F17640F9E8C4A6B69E2D7D6E /* DeliverySectionCell.swift */,
				17BF202F060531C516A72317 /* PaymentMethodCell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		10584DE072A808C95D5F7594 /* HomeView */ = {
			isa = PBXGroup;
			children = (
				0CC1D21C5BD8BB6DBA2734FB /* HomeView.swift */,
				56749B1EABC2CA087A187CD5 /* Cell */,
				E75C211D8205BB0CB58ECD6A /* HomeViewModel.swift */,
			);
			path = HomeView;
			sourceTree = "<group>";
		};
		180694472D6365EF0094FDFA /* Toast */ = {
			isa = PBXGroup;
			children = (
				180694482D6365FA0094FDFA /* ToastView.swift */,
				180694492D6365FA0094FDFA /* ToastModel.swift */,
				1806944A2D6365FA0094FDFA /* ToastModifier.swift */,
			);
			path = Toast;
			sourceTree = "<group>";
		};
		180694512D63DE430094FDFA /* ContactUs */ = {
			isa = PBXGroup;
			children = (
				180694542D63DE610094FDFA /* Model */,
				180694522D63DE4E0094FDFA /* View */,
				180694532D63DE570094FDFA /* ViewModel */,
			);
			path = ContactUs;
			sourceTree = "<group>";
		};
		180694522D63DE4E0094FDFA /* View */ = {
			isa = PBXGroup;
			children = (
				180694552D63DE720094FDFA /* ContactUsView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		180694532D63DE570094FDFA /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				180694572D63DF7C0094FDFA /* ContactUsViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		180694542D63DE610094FDFA /* Model */ = {
			isa = PBXGroup;
			children = (
			);
			path = Model;
			sourceTree = "<group>";
		};
		180694592D63EA710094FDFA /* AboutUs */ = {
			isa = PBXGroup;
			children = (
				1806945A2D63EA800094FDFA /* Model */,
				1806945B2D63EA850094FDFA /* View */,
				1806945C2D63EA8A0094FDFA /* ViewModel */,
			);
			path = AboutUs;
			sourceTree = "<group>";
		};
		1806945A2D63EA800094FDFA /* Model */ = {
			isa = PBXGroup;
			children = (
			);
			path = Model;
			sourceTree = "<group>";
		};
		1806945B2D63EA850094FDFA /* View */ = {
			isa = PBXGroup;
			children = (
				1806945D2D63EA9F0094FDFA /* AboutUsView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		1806945C2D63EA8A0094FDFA /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				1806945F2D63EABC0094FDFA /* AboutUsViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		181E38402CA083BD00780921 /* Wishlist */ = {
			isa = PBXGroup;
			children = (
				181E38412CA083C900780921 /* View */,
				18C4580C2D613AAC00EA8FF6 /* ViewModel */,
			);
			path = Wishlist;
			sourceTree = "<group>";
		};
		181E38412CA083C900780921 /* View */ = {
			isa = PBXGroup;
			children = (
				18C4580A2D613A4000EA8FF6 /* WishlistCellView.swift */,
				181E38422CA083F300780921 /* WishlistView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		186BC77E2C987D6900DA5131 /* Dashboard */ = {
			isa = PBXGroup;
			children = (
				18DBB1EA2C9D87FC00C41BF0 /* Model */,
				186BC77F2C987D7F00DA5131 /* View */,
				186BC7802C987D8900DA5131 /* ViewModel */,
				18DBB1F32C9D8D6600C41BF0 /* Tabs */,
			);
			path = Dashboard;
			sourceTree = "<group>";
		};
		186BC77F2C987D7F00DA5131 /* View */ = {
			isa = PBXGroup;
			children = (
				186BC7812C987DB900DA5131 /* DashboardView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		186BC7802C987D8900DA5131 /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				18DBB1F12C9D8CDC00C41BF0 /* DashboardViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		188221F22CF5281D005FB2C9 /* ApiPagination */ = {
			isa = PBXGroup;
			children = (
				188221F32CF52834005FB2C9 /* PaginatedViewModel.swift */,
			);
			path = ApiPagination;
			sourceTree = "<group>";
		};
		189ABE7C2D25BEC600234B5D /* GenericPagination */ = {
			isa = PBXGroup;
			children = (
				189ABE7D2D25BED000234B5D /* GenericPaginatedView.swift */,
				189ABE7F2D25BEDE00234B5D /* GenericPaginatedViewModel.swift */,
			);
			path = GenericPagination;
			sourceTree = "<group>";
		};
		189F9C9B2DE89D9E00191472 /* TapPayments */ = {
			isa = PBXGroup;
			children = (
				189F9C972DE89D9E00191472 /* TapCheckoutView.swift */,
				189F9C982DE89D9E00191472 /* TapPaymentConfig.swift */,
				189F9C992DE89D9E00191472 /* TapPaymentManager.swift */,
				189F9C9A2DE89D9E00191472 /* UIViewController+Extensions.swift */,
			);
			path = TapPayments;
			sourceTree = "<group>";
		};
		18A03B792D6B587900CD0DFC /* Notification */ = {
			isa = PBXGroup;
			children = (
				18A03B7A2D6B588400CD0DFC /* Model */,
				18A03B7B2D6B588800CD0DFC /* View */,
				18A03B7C2D6B588900CD0DFC /* ViewModel */,
			);
			path = Notification;
			sourceTree = "<group>";
		};
		18A03B7A2D6B588400CD0DFC /* Model */ = {
			isa = PBXGroup;
			children = (
			);
			path = Model;
			sourceTree = "<group>";
		};
		18A03B7B2D6B588800CD0DFC /* View */ = {
			isa = PBXGroup;
			children = (
				18A03B802D6B59A200CD0DFC /* NotificationView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		18A03B7C2D6B588900CD0DFC /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				18A03B822D6B59ED00CD0DFC /* NotificationViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		18B318702DB7984A006539AF /* Brand */ = {
			isa = PBXGroup;
			children = (
				18B318752DB7A17F006539AF /* BrandListingView.swift */,
				18B318712DB79877006539AF /* BrandListingTabContainerView.swift */,
				18B318732DB7988E006539AF /* BrandListingTabContainerViewModel.swift */,
			);
			path = Brand;
			sourceTree = "<group>";
		};
		18B31AD82D5780C800471476 /* FilterSort */ = {
			isa = PBXGroup;
			children = (
				18B31ADA2D57813100471476 /* Model */,
				18B31AD92D57812900471476 /* View */,
				18B31ADB2D57813900471476 /* ViewModel */,
			);
			path = FilterSort;
			sourceTree = "<group>";
		};
		18B31AD92D57812900471476 /* View */ = {
			isa = PBXGroup;
			children = (
				181B61262D53B90B009570AC /* FilterSortView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		18B31ADA2D57813100471476 /* Model */ = {
			isa = PBXGroup;
			children = (
			);
			path = Model;
			sourceTree = "<group>";
		};
		18B31ADB2D57813900471476 /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				18B31ADC2D57814F00471476 /* FilterSortViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		18B31AF32D58078B00471476 /* RichText */ = {
			isa = PBXGroup;
			children = (
				18B31AF12D58078200471476 /* RichTextView.swift */,
			);
			path = RichText;
			sourceTree = "<group>";
		};
		18B5CA6B2CA06EA000EA811B /* Main */ = {
			isa = PBXGroup;
			children = (
				18B5CA6C2CA06F8300EA811B /* MainView.swift */,
				18F736092CCEAE9C00F68F62 /* MainViewModel.swift */,
			);
			path = Main;
			sourceTree = "<group>";
		};
		18C2A5BE2D67CA02001AE292 /* RxDetails */ = {
			isa = PBXGroup;
			children = (
				18C2A5C22D67CA2D001AE292 /* Model */,
				18C2A5BF2D67CA0E001AE292 /* View */,
				18C2A5C02D67CA12001AE292 /* ViewModel */,
			);
			path = RxDetails;
			sourceTree = "<group>";
		};
		18C2A5BF2D67CA0E001AE292 /* View */ = {
			isa = PBXGroup;
			children = (
				180EFEF12DF96546005B3693 /* RxHistoryView.swift */,
				18C2A5C32D67CA36001AE292 /* RxDetailsView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		18C2A5C02D67CA12001AE292 /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				18C2A5C72D67CB25001AE292 /* RxProductCard.swift */,
				18C2A5C52D67CA5E001AE292 /* RxDetailsViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		18C2A5C22D67CA2D001AE292 /* Model */ = {
			isa = PBXGroup;
			children = (
				18A03B772D69C91200CD0DFC /* RXDetailsModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		18C4580C2D613AAC00EA8FF6 /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				18C4580D2D613AC000EA8FF6 /* WishlistViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		18DBB1DF2C9D843E00C41BF0 /* SuperBase */ = {
			isa = PBXGroup;
			children = (
				18F735FA2CCEA9CF00F68F62 /* Model */,
				18F735F92CCEA9C600F68F62 /* ViewModel */,
				18DBB1E02C9D844F00C41BF0 /* View */,
			);
			path = SuperBase;
			sourceTree = "<group>";
		};
		18DBB1E02C9D844F00C41BF0 /* View */ = {
			isa = PBXGroup;
			children = (
				18F735FF2CCEAC5F00F68F62 /* SuperView.swift */,
				18DBB1E12C9D845A00C41BF0 /* MainScrollBody.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		18DBB1E32C9D84C400C41BF0 /* Core */ = {
			isa = PBXGroup;
			children = (
				189F9C9B2DE89D9E00191472 /* TapPayments */,
				18F735D32CCD15BC00F68F62 /* APIServices */,
				18DBB1E72C9D853800C41BF0 /* AppState */,
				18DBB1E42C9D84D100C41BF0 /* Route */,
			);
			path = Core;
			sourceTree = "<group>";
		};
		18DBB1E42C9D84D100C41BF0 /* Route */ = {
			isa = PBXGroup;
			children = (
				18DBB1ED2C9D88F300C41BF0 /* Route.swift */,
				18DBB1E52C9D84DB00C41BF0 /* RouterManager.swift */,
			);
			path = Route;
			sourceTree = "<group>";
		};
		18DBB1E72C9D853800C41BF0 /* AppState */ = {
			isa = PBXGroup;
			children = (
				18DBB1E82C9D853F00C41BF0 /* AppState.swift */,
			);
			path = AppState;
			sourceTree = "<group>";
		};
		18DBB1EA2C9D87FC00C41BF0 /* Model */ = {
			isa = PBXGroup;
			children = (
				18DBB1EB2C9D881600C41BF0 /* DashbordModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		18DBB1F32C9D8D6600C41BF0 /* Tabs */ = {
			isa = PBXGroup;
			children = (
				18DBB1F42C9D8D7300C41BF0 /* TabExtension.swift */,
			);
			path = Tabs;
			sourceTree = "<group>";
		};
		18F735D32CCD15BC00F68F62 /* APIServices */ = {
			isa = PBXGroup;
			children = (
				18F735D42CCD15C600F68F62 /* Repositories */,
				18F735D52CCD15D000F68F62 /* BaseAPI */,
			);
			path = APIServices;
			sourceTree = "<group>";
		};
		18F735D42CCD15C600F68F62 /* Repositories */ = {
			isa = PBXGroup;
			children = (
				18F735EB2CCD19F200F68F62 /* RepositoriesAPI.swift */,
				18F735ED2CCD19FF00F68F62 /* RepositoriesNetworking.swift */,
				18F735E92CCD182500F68F62 /* APIEndPoints.swift */,
				18F735E82CCD17F400F68F62 /* Models */,
			);
			path = Repositories;
			sourceTree = "<group>";
		};
		18F735D52CCD15D000F68F62 /* BaseAPI */ = {
			isa = PBXGroup;
			children = (
				18F735DF2CCD171400F68F62 /* BaseAPI.swift */,
				18F735D62CCD160400F68F62 /* TargetType.swift */,
			);
			path = BaseAPI;
			sourceTree = "<group>";
		};
		18F735D82CCD162500F68F62 /* UserDefaults */ = {
			isa = PBXGroup;
			children = (
				18F735DA2CCD164500F68F62 /* UserDefaults.swift */,
			);
			path = UserDefaults;
			sourceTree = "<group>";
		};
		18F735E82CCD17F400F68F62 /* Models */ = {
			isa = PBXGroup;
			children = (
				18F735F32CCE874400F68F62 /* homePageModel.swift */,
				18F735F52CCE8B5100F68F62 /* WishlistModel.swift */,
				18F735F12CCE873100F68F62 /* GenerateTokenModel.swift */,
				18F735F72CCE8B8600F68F62 /* SignUpModel.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		18F735F92CCEA9C600F68F62 /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				18F735FB2CCEA9DB00F68F62 /* SuperViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		18F735FA2CCEA9CF00F68F62 /* Model */ = {
			isa = PBXGroup;
			children = (
				18F735FD2CCEAA1200F68F62 /* SuperModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		18F736012CCEACA800F68F62 /* Global */ = {
			isa = PBXGroup;
			children = (
				616C5DCB4F82A04E436287CD /* CustomViews */,
				88ED74F8272FFECA0088E3EF /* Constants */,
				88ED74FA272FFECA0088E3EF /* Extensions */,
				88ED74F6272FFECA0088E3EF /* Utilities */,
				7F44DB9E27422A4200171570 /* Helper */,
				18F736022CCEACB300F68F62 /* Components */,
			);
			path = Global;
			sourceTree = "<group>";
		};
		18F736022CCEACB300F68F62 /* Components */ = {
			isa = PBXGroup;
			children = (
				180694472D6365EF0094FDFA /* Toast */,
				188221F22CF5281D005FB2C9 /* ApiPagination */,
				18F7360C2CCED94100F68F62 /* NetworkImage */,
				18F736062CCEACF700F68F62 /* AlertView */,
				18F736032CCEACBE00F68F62 /* ActivityLoader */,
			);
			path = Components;
			sourceTree = "<group>";
		};
		18F736032CCEACBE00F68F62 /* ActivityLoader */ = {
			isa = PBXGroup;
			children = (
				18F736042CCEACC800F68F62 /* ActivityLoaderView.swift */,
			);
			path = ActivityLoader;
			sourceTree = "<group>";
		};
		18F736062CCEACF700F68F62 /* AlertView */ = {
			isa = PBXGroup;
			children = (
				18F736072CCEACFF00F68F62 /* AlertView.swift */,
			);
			path = AlertView;
			sourceTree = "<group>";
		};
		18F7360C2CCED94100F68F62 /* NetworkImage */ = {
			isa = PBXGroup;
			children = (
				18F7360D2CCED94900F68F62 /* NetworkImageView.swift */,
			);
			path = NetworkImage;
			sourceTree = "<group>";
		};
		1C732733234590FBCA7051F2 /* ForgotpasswordTwoView */ = {
			isa = PBXGroup;
			children = (
				3ABF169E30CC672F53577818 /* OTPView.swift */,
				574C2B57A1DA9E9D2AC57D4F /* ForgotpasswordTwoViewModel.swift */,
			);
			path = ForgotpasswordTwoView;
			sourceTree = "<group>";
		};
		1CE79412E2A1181C8D24A0D4 /* OrderhistorycancelledView */ = {
			isa = PBXGroup;
			children = (
				3FEF84CE717A70FEB8CE1C1D /* OrderhistorycancelledView.swift */,
				F3E1072449B68AE0F9BEF298 /* Cell */,
				E4DD345A24E27FDBC0A9C764 /* OrderhistorycancelledViewModel.swift */,
			);
			path = OrderhistorycancelledView;
			sourceTree = "<group>";
		};
		24FB2A7936F0E0F52B277C75 /* CartView */ = {
			isa = PBXGroup;
			children = (
				18E2DDF72D28757000674FE6 /* ExpandableView.swift */,
				165EF7059588AF8F2E04133A /* CartView.swift */,
				67C922CB96E82D033A4B6FBC /* Cell */,
				FEEC83E8541E4BD2170C6E95 /* CartViewModel.swift */,
			);
			path = CartView;
			sourceTree = "<group>";
		};
		27438E51001E2B813C821357 /* OrderinfoView */ = {
			isa = PBXGroup;
			children = (
				BCE5951963B5E18C6BFD7158 /* OrderinfoView.swift */,
				4435EDED7BBA2C98A6AD6258 /* Cell */,
				931DB24A50E3F606F2D696F2 /* OrderinfoViewModel.swift */,
			);
			path = OrderinfoView;
			sourceTree = "<group>";
		};
		2AA34476FA6DF109AAB2416C /* MenuView */ = {
			isa = PBXGroup;
			children = (
				189F9CB72DEA759900191472 /* LanguageView.swift */,
				18B31AEF2D57F75600471476 /* SideMenu.swift */,
				1AAF68B22785451DE9827671 /* MenuView.swift */,
				76A43417F7B920B8B7A83C87 /* MenuViewModel.swift */,
			);
			path = MenuView;
			sourceTree = "<group>";
		};
		3105FBA908BC40824059451C /* BottomSheetView */ = {
			isa = PBXGroup;
			children = (
				9A491F9E022A0009EBC023C0 /* BottomSheetView.swift */,
			);
			path = BottomSheetView;
			sourceTree = "<group>";
		};
		310BA6AFB863CFC0BD2C37DD /* Cell */ = {
			isa = PBXGroup;
			children = (
				8F82E945766BEFD862C3FAEC /* AddressCardView.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		35CD58E9544C31E66AE60979 /* AccoutnsettingsView */ = {
			isa = PBXGroup;
			children = (
				84EEA9E58F23C2FA3D5255E7 /* AccountSettingsView.swift */,
				4D2C580EA5A649784D4566AD /* AccountSettingsViewModel.swift */,
			);
			path = AccoutnsettingsView;
			sourceTree = "<group>";
		};
		384FEDE106566C6EDAC8A240 /* PageIndicator */ = {
			isa = PBXGroup;
			children = (
				C40A6ABB225AE80BEAFC8CDD /* PageIndicator.swift */,
			);
			path = PageIndicator;
			sourceTree = "<group>";
		};
		39259F1C449AAFB588CC324B /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				FEE84AEE9E6B402CDEE5750A /* Pods_Wasfa.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		4435EDED7BBA2C98A6AD6258 /* Cell */ = {
			isa = PBXGroup;
			children = (
				EEA8355B63737F9DC69E1B41 /* OrderProductCard.swift */,
				************************ /* Addressitem1Cell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		4792DDD5860BE1601491A6F0 /* ProductpageView */ = {
			isa = PBXGroup;
			children = (
				666FC2DA60FA482E27312D30 /* ProductListingView.swift */,
				59B1F04609B9E70EBD6D1CE2 /* Cell */,
				8FB586222E2A9B60E44F6AA3 /* ProductpageViewModel.swift */,
			);
			path = ProductpageView;
			sourceTree = "<group>";
		};
		4E1B5482937EA5E6EFB0F35A /* SignupView */ = {
			isa = PBXGroup;
			children = (
				8E0987F85AAF7E3A29C48E35 /* SignupView.swift */,
				9AC12F9D42E70649004BB7D6 /* SignupViewModel.swift */,
			);
			path = SignupView;
			sourceTree = "<group>";
		};
		53374312AA8E7AFD989A43D6 /* RadioGroup */ = {
			isa = PBXGroup;
			children = (
				8EBAC3CD23BB31C3C31C05D5 /* RadioGroupButton.swift */,
			);
			path = RadioGroup;
			sourceTree = "<group>";
		};
		5666B45CB35F5760276096E9 /* AddaddressView */ = {
			isa = PBXGroup;
			children = (
				075B17679140BF013294B4E0 /* AddaddressView.swift */,
				C728A6BF8B79179D16146D0B /* AddaddressViewModel.swift */,
			);
			path = AddaddressView;
			sourceTree = "<group>";
		};
		56749B1EABC2CA087A187CD5 /* Cell */ = {
			isa = PBXGroup;
			children = (
				50247C4B51CB6487D1C0657E /* CategoryItemCell.swift */,
				58D96A51AEC17AD58FCEF048 /* ProductCardCell.swift */,
				01E0AD9CBA71FEB4461FE5FC /* StackCell.swift */,
				859931B0FA198E72AC88A2BC /* Productcard1Cell.swift */,
				D084476EF5B36817F94AEF35 /* Productcard2Cell.swift */,
				D603BDB8035B45A30C75C55E /* ProductBrandCell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		59B1F04609B9E70EBD6D1CE2 /* Cell */ = {
			isa = PBXGroup;
			children = (
				3C645BFAC946664102274441 /* Productcard3Cell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		616C5DCB4F82A04E436287CD /* CustomViews */ = {
			isa = PBXGroup;
			children = (
				18B31AF32D58078B00471476 /* RichText */,
				8A4A3251B8C780B61FB711B2 /* TabAndPager */,
				384FEDE106566C6EDAC8A240 /* PageIndicator */,
				91494341AC2D020B2AF18AAB /* CalendarModuleView */,
				C94389AD780B622A3DF3FCCD /* OTPView */,
				3105FBA908BC40824059451C /* BottomSheetView */,
				53374312AA8E7AFD989A43D6 /* RadioGroup */,
				C0570473625446BB33D4EDBF /* FSPagerViewSwiftUI */,
			);
			path = CustomViews;
			sourceTree = "<group>";
		};
		622085217E29F215EC68D9A7 /* ChangepasswordView */ = {
			isa = PBXGroup;
			children = (
				5327EDFCB8C650FBA9CB3BB4 /* ChangepasswordView.swift */,
				BC87155C63FF6DEC0F37277E /* ChangepasswordViewModel.swift */,
			);
			path = ChangepasswordView;
			sourceTree = "<group>";
		};
		6526FB9D431DC709727A161E /* Pods */ = {
			isa = PBXGroup;
			children = (
				DF9A55322CBE29B17A2B52E9 /* Pods-Wasfa.debug.xcconfig */,
				3EA704986E45580EBFD52160 /* Pods-Wasfa.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		67C922CB96E82D033A4B6FBC /* Cell */ = {
			isa = PBXGroup;
			children = (
				C3AD2860630890974C698D61 /* ProductcardCartCell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		680674DB413262051CD3AEAB /* OrderhistorycompletedView */ = {
			isa = PBXGroup;
			children = (
				71947EF1E94183995AE1BA4B /* OrderhistorycompletedView.swift */,
				848CE589803961E9F07B3E24 /* Cell */,
				D2A7DBE9BCEBB56E359D31B9 /* OrderhistorycompletedViewModel.swift */,
			);
			path = OrderhistorycompletedView;
			sourceTree = "<group>";
		};
		691F56B36443E9C33DFD5CEC /* Cell */ = {
			isa = PBXGroup;
			children = (
				56CB11FD0889722F2B94A695 /* Userprofile3Cell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		72EC0BF9468C46988A7DAB8F /* SigninView */ = {
			isa = PBXGroup;
			children = (
				E5B07E98079E47AD876B4E1A /* SignInView.swift */,
				F6385B03CC7A0E02CA91F4DE /* SignInViewModel.swift */,
			);
			path = SigninView;
			sourceTree = "<group>";
		};
		770E37FADC1F876F961991F6 /* ForgotpasswordView */ = {
			isa = PBXGroup;
			children = (
				7940193B20956A15CFB72960 /* ForgotpasswordView.swift */,
				03D71EBFE9B252B58BEB0941 /* ForgotpasswordViewModel.swift */,
			);
			path = ForgotpasswordView;
			sourceTree = "<group>";
		};
		7A6A3E2F7AD4CFC64F115CA5 /* Cell */ = {
			isa = PBXGroup;
			children = (
				5F33F14448AAC0CCA05E0AE5 /* OrderHistoryCell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		7AB4AB0070C47392B4D92E68 /* DatechooseView */ = {
			isa = PBXGroup;
			children = (
				51AE3E001B30ADEB3AFEB48B /* DatechooseView.swift */,
				6E8559E21CBD0F11C493A429 /* DatechooseViewModel.swift */,
			);
			path = DatechooseView;
			sourceTree = "<group>";
		};
		7F44DB9E27422A4200171570 /* Helper */ = {
			isa = PBXGroup;
			children = (
				18F735D82CCD162500F68F62 /* UserDefaults */,
				88680D1A2775C5BD002E964F /* Viewport */,
				7F44DB9F27422A4200171570 /* ActivityLoader */,
			);
			path = Helper;
			sourceTree = "<group>";
		};
		7F44DB9F27422A4200171570 /* ActivityLoader */ = {
			isa = PBXGroup;
			children = (
				7F44DBA027422A5D00171570 /* ActivityLoader.swift */,
			);
			path = ActivityLoader;
			sourceTree = "<group>";
		};
		801A87F1D41A97EA1A18CE3D /* OrderhistoryTabContainerView */ = {
			isa = PBXGroup;
			children = (
				EBAC41947D302532C5C27BA7 /* OrderHistoryTabContainerView.swift */,
				2FBC52EBF82A435AFB0ADDD2 /* OrderHistoryTabContainerViewModel.swift */,
			);
			path = OrderhistoryTabContainerView;
			sourceTree = "<group>";
		};
		80C2CC95F87152734B132A9A /* TrackorderOneView */ = {
			isa = PBXGroup;
			children = (
				91946767B5F35106036B5AA2 /* TrackorderOneView.swift */,
				E4D7D14A12D9A8B07D067BAA /* TrackorderOneViewModel.swift */,
			);
			path = TrackorderOneView;
			sourceTree = "<group>";
		};
		80E6D5B441A216A81FDFB539 /* OrderhistoryView */ = {
			isa = PBXGroup;
			children = (
				3C8E86E953F0E84B2AEA14B0 /* OrderHistoryView.swift */,
				7A6A3E2F7AD4CFC64F115CA5 /* Cell */,
				A984103FD8219F53029FFF9E /* OrderhistoryViewModel.swift */,
			);
			path = OrderhistoryView;
			sourceTree = "<group>";
		};
		848CE589803961E9F07B3E24 /* Cell */ = {
			isa = PBXGroup;
			children = (
				863D907C13E8D3CE49045921 /* Userprofile1Cell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		86EB88F000042406F6330327 /* Cell */ = {
			isa = PBXGroup;
			children = (
				196C9CBCAFE738B3B883EDD5 /* Productcard4Cell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		872DBA3356413D28F2E9D880 /* SplashscreenView */ = {
			isa = PBXGroup;
			children = (
				82690688ED928EEC3ADE999C /* SplashscreenView.swift */,
				C4A20AFD289680B853934BA6 /* SplashscreenViewModel.swift */,
			);
			path = SplashscreenView;
			sourceTree = "<group>";
		};
		88680D1A2775C5BD002E964F /* Viewport */ = {
			isa = PBXGroup;
			children = (
				88680D1B2775C601002E964F /* ViewportHelper.swift */,
			);
			path = Viewport;
			sourceTree = "<group>";
		};
		88ED74DB272FFE6E0088E3EF = {
			isa = PBXGroup;
			children = (
				88ED74E6272FFE6E0088E3EF /* Wasfa */,
				88ED74E5272FFE6E0088E3EF /* Products */,
				6526FB9D431DC709727A161E /* Pods */,
				39259F1C449AAFB588CC324B /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		88ED74E5272FFE6E0088E3EF /* Products */ = {
			isa = PBXGroup;
			children = (
				88ED74E4272FFE6E0088E3EF /* Wasfa.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		88ED74E6272FFE6E0088E3EF /* Wasfa */ = {
			isa = PBXGroup;
			children = (
				18925D8C2DEB2E3F003ED506 /* Dev.xcconfig */,
				18925D8E2DEB2E4D003ED506 /* Prod.xcconfig */,
				18B318792DB90EEF006539AF /* Wasfa.entitlements */,
				88ED74FE272FFECA0088E3EF /* Application */,
				88ED74FC272FFECA0088E3EF /* Services */,
				18DBB1E32C9D84C400C41BF0 /* Core */,
				************************ /* Views */,
				18F736012CCEACA800F68F62 /* Global */,
				88ED74EB272FFE6F0088E3EF /* Assets.xcassets */,
				1827998C2DB90FA900E275E8 /* GoogleService-Info.plist */,
				88ED74F0272FFE6F0088E3EF /* Info.plist */,
				88ED74ED272FFE6F0088E3EF /* Preview Content */,
				A051F212BF9A3E262BEEEAA9 /* Resources */,
				18D170A52DC3B11C009E0CED /* Localizable.xcstrings */,
			);
			path = Wasfa;
			sourceTree = "<group>";
		};
		88ED74ED272FFE6F0088E3EF /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				88ED74EE272FFE6F0088E3EF /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		88ED74F6272FFECA0088E3EF /* Utilities */ = {
			isa = PBXGroup;
			children = (
				88ED74F7272FFECA0088E3EF /* Utilities.swift */,
			);
			path = Utilities;
			sourceTree = "<group>";
		};
		88ED74F8272FFECA0088E3EF /* Constants */ = {
			isa = PBXGroup;
			children = (
				88ED74F9272FFECA0088E3EF /* AppConstants.swift */,
				E22D942B72847B3BF3B91911 /* StringScheme.swift */,
				0281322832DA8198C3123B14 /* ColorConstants.swift */,
				C28310FD416BE67238C4CDEC /* FontScheme.swift */,
			);
			path = Constants;
			sourceTree = "<group>";
		};
		88ED74FA272FFECA0088E3EF /* Extensions */ = {
			isa = PBXGroup;
			children = (
				18D1706A2DC2339C009E0CED /* IntegerExtension.swift */,
				18B31ADE2D57822D00471476 /* DoubleExtension.swift */,
				188221EA2CF396B0005FB2C9 /* ColorExtension.swift */,
				18F735E62CCD177F00F68F62 /* DataExtension.swift */,
				18F735E42CCD175800F68F62 /* DateExtension.swift */,
				18DBB1EF2C9D8B5400C41BF0 /* StringExtension.swift */,
				888CB47427686A000041116C /* APIExtensions.swift */,
				8853052527718DC500B04E6F /* ViewExtension.swift */,
				8853052B2771949600B04E6F /* UINavigationController.swift */,
				8853052927718E2D00B04E6F /* EncodableExtension.swift */,
				884F3E5027B0E65A00963FC4 /* RoundedCornersView.swift */,
			);
			path = Extensions;
			sourceTree = "<group>";
		};
		88ED74FC272FFECA0088E3EF /* Services */ = {
			isa = PBXGroup;
			children = (
				88ED74FD272FFECA0088E3EF /* APIManager.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		88ED74FE272FFECA0088E3EF /* Application */ = {
			isa = PBXGroup;
			children = (
				1827998E2DB9113300E275E8 /* Message.apns */,
				C40AB9F72754D79E006BC5D7 /* AppDelegate.swift */,
				88ED74FF272FFECA0088E3EF /* WasfaApp.swift */,
			);
			path = Application;
			sourceTree = "<group>";
		};
		8A4A3251B8C780B61FB711B2 /* TabAndPager */ = {
			isa = PBXGroup;
			children = (
				8A1EDB243A72AED75A238116 /* TabsView.swift */,
				1F642E71D0C23D0FE26C33D7 /* PagerView.swift */,
			);
			path = TabAndPager;
			sourceTree = "<group>";
		};
		8D96C4A9A2740020E1597C69 /* DeliveryaddressaddView */ = {
			isa = PBXGroup;
			children = (
				020E3107BA54432D1805F1C1 /* DeliveryaddressaddView.swift */,
				691F56B36443E9C33DFD5CEC /* Cell */,
				0AB6AA6A279F0427F6ACF0C7 /* DeliveryaddressaddViewModel.swift */,
			);
			path = DeliveryaddressaddView;
			sourceTree = "<group>";
		};
		91494341AC2D020B2AF18AAB /* CalendarModuleView */ = {
			isa = PBXGroup;
			children = (
				D9100447B09160510177AC88 /* CalendarModuleView.swift */,
			);
			path = CalendarModuleView;
			sourceTree = "<group>";
		};
		962C1F0E48D28052C9F46B6F /* ProductDetailView */ = {
			isa = PBXGroup;
			children = (
				187FEED42CD67A2900EAC51C /* ProductDetailsModel.swift */,
				1F81D3E1AD52CF47572FC7DC /* ProductDetailView.swift */,
				86EB88F000042406F6330327 /* Cell */,
				F38A735960E25CD821F6FAC7 /* ProductDetailViewModel.swift */,
			);
			path = ProductDetailView;
			sourceTree = "<group>";
		};
		9C81AE2C8F58E51B94D5EB97 /* DeliveryaddressView */ = {
			isa = PBXGroup;
			children = (
				DE1285E860102A568F411C4F /* DeliveryAddressView.swift */,
				310BA6AFB863CFC0BD2C37DD /* Cell */,
				E0D403ED2809BBCDD8F73B75 /* DeliveryAddressViewModel.swift */,
			);
			path = DeliveryaddressView;
			sourceTree = "<group>";
		};
		A051F212BF9A3E262BEEEAA9 /* Resources */ = {
			isa = PBXGroup;
			children = (
				D68CECA87D38A3341437EBC8 /* Fonts */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		AC9BD22027DBCBB7B28B0680 /* Cell */ = {
			isa = PBXGroup;
			children = (
				C9E6FFC61F6B7E26030CB88F /* LowcateCell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		B1D5A7AE78CDC92C20386206 /* CategoryOneView */ = {
			isa = PBXGroup;
			children = (
				D22CE6C9721C77E2C43C0DEC /* CategoryOneView.swift */,
				B901D15FDF79FCE7A25D0894 /* Cell */,
				90CD32E91BBE28299CE81595 /* CategoryOneViewModel.swift */,
			);
			path = CategoryOneView;
			sourceTree = "<group>";
		};
		B901D15FDF79FCE7A25D0894 /* Cell */ = {
			isa = PBXGroup;
			children = (
				A62465835C7419AB7C854D1F /* BeautycareCell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		BDF6AA7A839BECD79CEADCDF /* Cell */ = {
			isa = PBXGroup;
			children = (
				6CB7705312B7FD19A98C1F8F /* DeviceListCell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		C0570473625446BB33D4EDBF /* FSPagerViewSwiftUI */ = {
			isa = PBXGroup;
			children = (
				1D8CE4AF0B271CD421198D63 /* FSPageControlSUI.swift */,
				14F0712BA907035D7D022DB1 /* FSPagerViewSUI.swift */,
				63246D195B5E21309F7F6E09 /* FSPagerViewSUIOrigin.swift */,
			);
			path = FSPagerViewSwiftUI;
			sourceTree = "<group>";
		};
		C0ED5FF2B8DA5DE86DC7E81E /* CheckoutView */ = {
			isa = PBXGroup;
			children = (
				189F9CB52DEA5D6500191472 /* OrderFailureView.swift */,
				18ECBB5F2DA0711D0016358A /* SampleCheckout.swift */,
				188222022CF67236005FB2C9 /* OrderSuccessView.swift */,
				188222002CF65B9C005FB2C9 /* AddressModel.swift */,
				188221FD2CF641B3005FB2C9 /* AddressPopUpView.swift */,
				188221FB2CF6336E005FB2C9 /* CalendarView.swift */,
				18A7E8A52CBACA9E005D61C3 /* CheckoutModel.swift */,
				74386B1B71C1C565697B19FD /* CheckoutView.swift */,
				077584CE75FE746FA6C5C0F5 /* Cell */,
				B4F4D98D72B53E42A4904A72 /* CheckoutViewModel.swift */,
				189F9CA02DE8A2F100191472 /* TapPaymentPopupView.swift */,
			);
			path = CheckoutView;
			sourceTree = "<group>";
		};
		C94389AD780B622A3DF3FCCD /* OTPView */ = {
			isa = PBXGroup;
			children = (
				46A92342DBB5ED41F52BBDA2 /* OTPReprasentable.swift */,
				53A5F926836ADAAB2534A7FB /* OTPTextField.swift */,
				************************ /* OTPFieldView.swift */,
			);
			path = OTPView;
			sourceTree = "<group>";
		};
		CB7EA4397E0E1A684FC9DEB0 /* CategoryView */ = {
			isa = PBXGroup;
			children = (
				626C8D3E9DF9292C27DF923C /* CategoryView.swift */,
				BDF6AA7A839BECD79CEADCDF /* Cell */,
				C6BE93C21C9E031B6572511C /* CategoryViewModel.swift */,
			);
			path = CategoryView;
			sourceTree = "<group>";
		};
		************************ /* MyAccountView */ = {
			isa = PBXGroup;
			children = (
				3917C355BC1C7DA1693896D2 /* MyAccountView.swift */,
				47DE2C55B95BBF79118D2DCA /* MyAccountViewModel.swift */,
			);
			path = MyAccountView;
			sourceTree = "<group>";
		};
		************************ /* Views */ = {
			isa = PBXGroup;
			children = (
				18B5CA6B2CA06EA000EA811B /* Main */,
				18DBB1DF2C9D843E00C41BF0 /* SuperBase */,
				186BC77E2C987D6900DA5131 /* Dashboard */,
				18A03B792D6B587900CD0DFC /* Notification */,
				18C2A5BE2D67CA02001AE292 /* RxDetails */,
				180694592D63EA710094FDFA /* AboutUs */,
				180694512D63DE430094FDFA /* ContactUs */,
				189ABE7C2D25BEC600234B5D /* GenericPagination */,
				872DBA3356413D28F2E9D880 /* SplashscreenView */,
				10584DE072A808C95D5F7594 /* HomeView */,
				CB7EA4397E0E1A684FC9DEB0 /* CategoryView */,
				18B318702DB7984A006539AF /* Brand */,
				181E38402CA083BD00780921 /* Wishlist */,
				B1D5A7AE78CDC92C20386206 /* CategoryOneView */,
				4792DDD5860BE1601491A6F0 /* ProductpageView */,
				18B31AD82D5780C800471476 /* FilterSort */,
				E840A4684EE3E40B46FC4905 /* ProductpageTabContainerView */,
				962C1F0E48D28052C9F46B6F /* ProductDetailView */,
				24FB2A7936F0E0F52B277C75 /* CartView */,
				C0ED5FF2B8DA5DE86DC7E81E /* CheckoutView */,
				7AB4AB0070C47392B4D92E68 /* DatechooseView */,
				5666B45CB35F5760276096E9 /* AddaddressView */,
				72EC0BF9468C46988A7DAB8F /* SigninView */,
				4E1B5482937EA5E6EFB0F35A /* SignupView */,
				770E37FADC1F876F961991F6 /* ForgotpasswordView */,
				1C732733234590FBCA7051F2 /* ForgotpasswordTwoView */,
				************************ /* Forgotpassword2View */,
				************************ /* PasswordSucessView */,
				************************ /* MyAccountView */,
				35CD58E9544C31E66AE60979 /* AccoutnsettingsView */,
				80E6D5B441A216A81FDFB539 /* OrderhistoryView */,
				801A87F1D41A97EA1A18CE3D /* OrderhistoryTabContainerView */,
				680674DB413262051CD3AEAB /* OrderhistorycompletedView */,
				1CE79412E2A1181C8D24A0D4 /* OrderhistorycancelledView */,
				FE4B814E7366220E49CE2F6F /* TrackorderView */,
				27438E51001E2B813C821357 /* OrderinfoView */,
				9C81AE2C8F58E51B94D5EB97 /* DeliveryaddressView */,
				8D96C4A9A2740020E1597C69 /* DeliveryaddressaddView */,
				622085217E29F215EC68D9A7 /* ChangepasswordView */,
				80C2CC95F87152734B132A9A /* TrackorderOneView */,
				2AA34476FA6DF109AAB2416C /* MenuView */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		D68CECA87D38A3341437EBC8 /* Fonts */ = {
			isa = PBXGroup;
			children = (
				2B18BF27D29E8F514C4B5E80 /* InterMedium.ttf */,
				9B4787EDAC33DB4430DB0189 /* NunitoBold.ttf */,
				719752489C153D1D2A88FBEB /* RobotoRomanMedium.ttf */,
				1BEE0BCA34B58BADC2DFAB60 /* InterRegular.ttf */,
				BE393313FCF9CE797728A7A5 /* NunitoSemiBold.ttf */,
				BF6648BFCB22F3C759FDD7AA /* NunitoLight.ttf */,
				9E9DBB638FB481410EFAF382 /* NunitoExtraBold.ttf */,
				185CC6492D9D4EC50014074F /* PoppinsRegular.ttf */,
				185CC6472D9C162B0014074F /* PoppinsBold.ttf */,
				18ECBB572D9E8C9A0016358A /* PoppinsLight.ttf */,
				185CC64B2D9D4F7C0014074F /* PoppinsSemiBold.ttf */,
				185CC6452D9C0EE60014074F /* PoppinsMedium.ttf */,
				A49A84E18DA7E9FF2C5F8B79 /* RobotoRomanLight.ttf */,
				F10432B3F69C43FFB1368E96 /* NunitoMedium.ttf */,
				D86F707416658EE7F94DA08E /* PlusJakartaSansRomanBold.ttf */,
				250156E9118521990D94D22E /* RobotoRomanRegular.ttf */,
				22D0F9339EA8063E6FDB4C36 /* NunitoRegular.ttf */,
			);
			path = Fonts;
			sourceTree = "<group>";
		};
		E840A4684EE3E40B46FC4905 /* ProductpageTabContainerView */ = {
			isa = PBXGroup;
			children = (
				181B61282D54219E009570AC /* ProductCategoryCell.swift */,
				188221EC2CF39760005FB2C9 /* ProductListingModel.swift */,
				7E9AEAE937F4A8FA53012AE4 /* ProductListingTabContainerView.swift */,
				AC9BD22027DBCBB7B28B0680 /* Cell */,
				A3A135E8A750529E0180BED6 /* ProductpageTabContainerViewModel.swift */,
			);
			path = ProductpageTabContainerView;
			sourceTree = "<group>";
		};
		F3E1072449B68AE0F9BEF298 /* Cell */ = {
			isa = PBXGroup;
			children = (
				9B0AAEC205F089AD3093E4BC /* Productcard6Cell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		************************ /* PasswordSucessView */ = {
			isa = PBXGroup;
			children = (
				7CCF94E4F3BC423646F33EAF /* PasswordSucessView.swift */,
				77671335D4D12EF51C115957 /* PasswordSucessViewModel.swift */,
			);
			path = PasswordSucessView;
			sourceTree = "<group>";
		};
		************************ /* Forgotpassword2View */ = {
			isa = PBXGroup;
			children = (
				22359425D8166C900F4AFEB1 /* Forgotpassword2View.swift */,
				6788D559B8964EB95B5A5988 /* Forgotpassword2ViewModel.swift */,
			);
			path = Forgotpassword2View;
			sourceTree = "<group>";
		};
		FE4B814E7366220E49CE2F6F /* TrackorderView */ = {
			isa = PBXGroup;
			children = (
				18AE5D812D662A390049ED7A /* OrderStepperView.swift */,
				C8EDFDF46F6432EE189220BA /* TrackOrderView.swift */,
				2640FF755176F9B687C0AD69 /* TrackorderViewModel.swift */,
			);
			path = TrackorderView;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		88ED74E3272FFE6E0088E3EF /* Wasfa */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 88ED74F3272FFE6F0088E3EF /* Build configuration list for PBXNativeTarget "Wasfa" */;
			buildPhases = (
				8F53D19BABE41854509980E0 /* [CP] Check Pods Manifest.lock */,
				88ED74E0272FFE6E0088E3EF /* Sources */,
				88ED74E1272FFE6E0088E3EF /* Frameworks */,
				88ED74E2272FFE6E0088E3EF /* Resources */,
				79A62FF95FB70B6405C50036 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Wasfa;
			packageProductDependencies = (
				18EED28E2C958F6300FFBCCA /* FSCalendar */,
				18EED2912C958FAE00FFBCCA /* FSPagerView */,
				18EED2942C95903900FFBCCA /* Alamofire */,
				18F735DD2CCD165800F68F62 /* SecureDefaults */,
				18F735E22CCD173000F68F62 /* SwiftyJSON */,
				18F736162CCEE6FA00F68F62 /* SDWebImageSVGKitPlugin */,
				187FEECF2CD670B300EAC51C /* SDWebImageSwiftUI */,
				187FEED22CD6729500EAC51C /* SDWebImageWebPCoder */,
				18B31AED2D57EFBA00471476 /* SDWebImageSVGCoder */,
				18B31AF52D5807B700471476 /* RichText */,
				1806944F2D63664D0094FDFA /* SwipeActions */,
				18ECBB5A2D9E8D9A0016358A /* WrappingHStack */,
				18ECBB5D2D9E8F580016358A /* Sliders */,
				18B3186E2DB66FD3006539AF /* FirebaseMessaging */,
			);
			productName = Wasfa;
			productReference = 88ED74E4272FFE6E0088E3EF /* Wasfa.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		88ED74DC272FFE6E0088E3EF /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1250;
				LastUpgradeCheck = 1250;
				TargetAttributes = {
					88ED74E3272FFE6E0088E3EF = {
						CreatedOnToolsVersion = 12.5;
					};
				};
			};
			buildConfigurationList = 88ED74DF272FFE6E0088E3EF /* Build configuration list for PBXProject "Wasfa" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				ar,
			);
			mainGroup = 88ED74DB272FFE6E0088E3EF;
			packageReferences = (
				18EED28D2C958F6300FFBCCA /* XCRemoteSwiftPackageReference "FSCalendar" */,
				18EED2902C958FAE00FFBCCA /* XCLocalSwiftPackageReference "FSPagerView-master" */,
				18EED2932C95903900FFBCCA /* XCRemoteSwiftPackageReference "Alamofire" */,
				18F735DC2CCD165800F68F62 /* XCRemoteSwiftPackageReference "SecureDefaults" */,
				18F735E12CCD173000F68F62 /* XCRemoteSwiftPackageReference "SwiftyJSON" */,
				18F736152CCEE6FA00F68F62 /* XCRemoteSwiftPackageReference "SDWebImageSVGKitPlugin" */,
				187FEECE2CD670B300EAC51C /* XCRemoteSwiftPackageReference "SDWebImageSwiftUI" */,
				187FEED12CD6729500EAC51C /* XCRemoteSwiftPackageReference "SDWebImageWebPCoder" */,
				18B31AEC2D57EFBA00471476 /* XCRemoteSwiftPackageReference "SDWebImageSVGCoder" */,
				18B31AF42D5807B700471476 /* XCRemoteSwiftPackageReference "RichText" */,
				1806944E2D63664D0094FDFA /* XCRemoteSwiftPackageReference "SwipeActions" */,
				18ECBB592D9E8D9A0016358A /* XCLocalSwiftPackageReference "WrappingHStack-main" */,
				18ECBB5C2D9E8F580016358A /* XCRemoteSwiftPackageReference "swiftui-sliders" */,
				18B3186D2DB66FD3006539AF /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */,
			);
			productRefGroup = 88ED74E5272FFE6E0088E3EF /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				88ED74E3272FFE6E0088E3EF /* Wasfa */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		88ED74E2272FFE6E0088E3EF /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				88ED74EF272FFE6F0088E3EF /* Preview Assets.xcassets in Resources */,
				18925D8F2DEB2E50003ED506 /* Prod.xcconfig in Resources */,
				18925D8D2DEB2E42003ED506 /* Dev.xcconfig in Resources */,
				88ED74EC272FFE6F0088E3EF /* Assets.xcassets in Resources */,
				1F238975D6DBA68DA0BAB620 /* InterMedium.ttf in Resources */,
				185CC6482D9C162B0014074F /* PoppinsBold.ttf in Resources */,
				3D6AFF4BD3DBD3BB7AC504CE /* NunitoBold.ttf in Resources */,
				185CC64A2D9D4EC50014074F /* PoppinsRegular.ttf in Resources */,
				8043024A0A398519479C301D /* RobotoRomanMedium.ttf in Resources */,
				C810C84A073A9CFE559B8CA4 /* InterRegular.ttf in Resources */,
				1827998D2DB90FA900E275E8 /* GoogleService-Info.plist in Resources */,
				E57C4B16D271189044040C7A /* NunitoSemiBold.ttf in Resources */,
				4BFB9ABCF21BF30A541847AC /* NunitoLight.ttf in Resources */,
				18ECBB582D9E8C9A0016358A /* PoppinsLight.ttf in Resources */,
				FE4AAFAAFA12161D185E7E0D /* NunitoExtraBold.ttf in Resources */,
				185CC6462D9C0EE60014074F /* PoppinsMedium.ttf in Resources */,
				6A448F67DB51D5E299B630F0 /* RobotoRomanLight.ttf in Resources */,
				E73A96FAB123CD6EEECB1553 /* NunitoMedium.ttf in Resources */,
				18D170A62DC3B11C009E0CED /* Localizable.xcstrings in Resources */,
				82895C112F75B1D1FABA45F1 /* PlusJakartaSansRomanBold.ttf in Resources */,
				185CC64C2D9D4F7C0014074F /* PoppinsSemiBold.ttf in Resources */,
				1CC4C0E647725361D3A7639B /* RobotoRomanRegular.ttf in Resources */,
				1827998F2DB9113300E275E8 /* Message.apns in Resources */,
				DE5A4446C7DD0868A8812E12 /* NunitoRegular.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		79A62FF95FB70B6405C50036 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Wasfa/Pods-Wasfa-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Wasfa/Pods-Wasfa-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Wasfa/Pods-Wasfa-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		8F53D19BABE41854509980E0 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Wasfa-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		88ED74E0272FFE6E0088E3EF /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				18F735F22CCE873B00F68F62 /* GenerateTokenModel.swift in Sources */,
				888CB47527686A000041116C /* APIExtensions.swift in Sources */,
				8853052A27718E2D00B04E6F /* EncodableExtension.swift in Sources */,
				181B61292D54219F009570AC /* ProductCategoryCell.swift in Sources */,
				8853052C2771949600B04E6F /* UINavigationController.swift in Sources */,
				88ED7503272FFECA0088E3EF /* APIManager.swift in Sources */,
				7F44DBA127422A5D00171570 /* ActivityLoader.swift in Sources */,
				8853052627718DC500B04E6F /* ViewExtension.swift in Sources */,
				18A03B782D69C91900CD0DFC /* RXDetailsModel.swift in Sources */,
				88ED7500272FFECA0088E3EF /* Utilities.swift in Sources */,
				88ED7504272FFECA0088E3EF /* WasfaApp.swift in Sources */,
				C40AB9F82754D79E006BC5D7 /* AppDelegate.swift in Sources */,
				18F736052CCEACC900F68F62 /* ActivityLoaderView.swift in Sources */,
				88680D1C2775C601002E964F /* ViewportHelper.swift in Sources */,
				88ED7501272FFECA0088E3EF /* AppConstants.swift in Sources */,
				18ECBB602DA071250016358A /* SampleCheckout.swift in Sources */,
				18DBB1E22C9D845C00C41BF0 /* MainScrollBody.swift in Sources */,
				884F3E5127B0E65A00963FC4 /* RoundedCornersView.swift in Sources */,
				18A03B812D6B59A700CD0DFC /* NotificationView.swift in Sources */,
				18F736002CCEAC6000F68F62 /* SuperView.swift in Sources */,
				18F735FC2CCEA9DC00F68F62 /* SuperViewModel.swift in Sources */,
				1806945E2D63EAA60094FDFA /* AboutUsView.swift in Sources */,
				DBBAF17AA379F9E7A5C19807 /* StringScheme.swift in Sources */,
				2C3C8A8E172562422022DDE2 /* ColorConstants.swift in Sources */,
				18F736082CCEAD0000F68F62 /* AlertView.swift in Sources */,
				F4FFEB7CD6B55033E0E726B3 /* FontScheme.swift in Sources */,
				189ABE802D25BEDF00234B5D /* GenericPaginatedViewModel.swift in Sources */,
				3A591CFBE6BCE7D9C9FD7B14 /* SplashscreenView.swift in Sources */,
				EFE272F19D3DB4566BD7A99F /* HomeView.swift in Sources */,
				DE1C45A69F48E837A5D1071F /* CategoryItemCell.swift in Sources */,
				0BFE9D80B0F7DEDD46F7C76E /* ProductCardCell.swift in Sources */,
				001E606D7AEAE3CF3C2B418C /* StackCell.swift in Sources */,
				35F85B950AC2C5EEDB238066 /* Productcard1Cell.swift in Sources */,
				3D1F9989842E41212C229CF4 /* Productcard2Cell.swift in Sources */,
				18C2A5C62D67CA66001AE292 /* RxDetailsViewModel.swift in Sources */,
				78CDE4867E64D7DF60FF9121 /* ProductBrandCell.swift in Sources */,
				2AEAB277F5353C7089BABA9E /* CategoryView.swift in Sources */,
				84B66014EAFD53250EC5A01E /* DeviceListCell.swift in Sources */,
				2E4C71A23B8C733973D63012 /* CategoryOneView.swift in Sources */,
				1E987EBCCC68A80112478AF2 /* BeautycareCell.swift in Sources */,
				F207954CBC74188F9F564E23 /* ProductListingView.swift in Sources */,
				7C2B49D8D7BCFA5238104E95 /* Productcard3Cell.swift in Sources */,
				18C4580E2D613AC600EA8FF6 /* WishlistViewModel.swift in Sources */,
				9BBA0060EC30E3573D453EB2 /* ProductListingTabContainerView.swift in Sources */,
				16691E70134A638F593C5625 /* LowcateCell.swift in Sources */,
				181B61272D53B90D009570AC /* FilterSortView.swift in Sources */,
				180EFEF22DF9654E005B3693 /* RxHistoryView.swift in Sources */,
				4463315CEFCE3748D6EE7C2B /* ProductDetailView.swift in Sources */,
				F2F8986DBE817FB4E082B664 /* Productcard4Cell.swift in Sources */,
				4C4521BF346E2369BE9D19B3 /* CartView.swift in Sources */,
				180694582D63DF7E0094FDFA /* ContactUsViewModel.swift in Sources */,
				7E91E1C894A500D06F4C4C05 /* ProductcardCartCell.swift in Sources */,
				181E38432CA083FA00780921 /* WishlistView.swift in Sources */,
				6F2393F5A45A4B0105DF743D /* CheckoutView.swift in Sources */,
				180694562D63DE740094FDFA /* ContactUsView.swift in Sources */,
				31C78C2DE3AB1748732EA906 /* DeliverySectionCell.swift in Sources */,
				18F735EE2CCD1A0100F68F62 /* RepositoriesNetworking.swift in Sources */,
				688F515FA0453570AE83BBF8 /* PaymentMethodCell.swift in Sources */,
				215F9F084FEC6D38E5EA6BDD /* DatechooseView.swift in Sources */,
				18A03B832D6B59F400CD0DFC /* NotificationViewModel.swift in Sources */,
				880771E41F55E883CEEB44BB /* AddaddressView.swift in Sources */,
				7711A4C4339F52FE128CBE49 /* SignInView.swift in Sources */,
				F9C2A3D520D89E894659A3B7 /* SignupView.swift in Sources */,
				188221FC2CF6336F005FB2C9 /* CalendarView.swift in Sources */,
				18B5CA6D2CA06F8400EA811B /* MainView.swift in Sources */,
				F9BDD0A871DDB68E02F0D0A1 /* ForgotpasswordView.swift in Sources */,
				187FEED52CD67A3700EAC51C /* ProductDetailsModel.swift in Sources */,
				FFFB6EACE98AF9B45BDF6399 /* OTPView.swift in Sources */,
				18F735F82CCE8B8B00F68F62 /* SignUpModel.swift in Sources */,
				7EAF2FF95D0EDA172C845DF5 /* Forgotpassword2View.swift in Sources */,
				18F7360E2CCED94A00F68F62 /* NetworkImageView.swift in Sources */,
				9F1A5898E36A356F218AF9CE /* PasswordSucessView.swift in Sources */,
				A1552E0BF36235C7F4F195C5 /* MyAccountView.swift in Sources */,
				3C12AFC23FF328294F2003DF /* AccountSettingsView.swift in Sources */,
				18C2A5C42D67CA3C001AE292 /* RxDetailsView.swift in Sources */,
				D48A055C563F4D19CEE47A6D /* OrderHistoryView.swift in Sources */,
				18AE5D822D662A3B0049ED7A /* OrderStepperView.swift in Sources */,
				836C29860E9901E815275C1C /* OrderHistoryCell.swift in Sources */,
				18A7E8A62CBACAA5005D61C3 /* CheckoutModel.swift in Sources */,
				189F9CA12DE8A2F100191472 /* TapPaymentPopupView.swift in Sources */,
				646F838E326923AE33AD0112 /* OrderHistoryTabContainerView.swift in Sources */,
				8C5571CE8F944C5241A927F6 /* OrderhistorycompletedView.swift in Sources */,
				A17F333FDDC83CDE80517EF1 /* Userprofile1Cell.swift in Sources */,
				189F9CB82DEA759A00191472 /* LanguageView.swift in Sources */,
				186BC7822C987DB900DA5131 /* DashboardView.swift in Sources */,
				188221F42CF5283E005FB2C9 /* PaginatedViewModel.swift in Sources */,
				7FD16F2998F949A487F36C00 /* OrderhistorycancelledView.swift in Sources */,
				18F735EA2CCD182600F68F62 /* APIEndPoints.swift in Sources */,
				18F735E72CCD178400F68F62 /* DataExtension.swift in Sources */,
				04F63B4036580CB328B3EB31 /* Productcard6Cell.swift in Sources */,
				6FFC1821B870FD36F01B863C /* TrackOrderView.swift in Sources */,
				89E3AD60014ECA00AA92B96D /* OrderinfoView.swift in Sources */,
				18DBB1F22C9D8CDD00C41BF0 /* DashboardViewModel.swift in Sources */,
				18F735EC2CCD19F500F68F62 /* RepositoriesAPI.swift in Sources */,
				18B318722DB79877006539AF /* BrandListingTabContainerView.swift in Sources */,
				1806944B2D6365FA0094FDFA /* ToastView.swift in Sources */,
				1806944C2D6365FA0094FDFA /* ToastModel.swift in Sources */,
				1806944D2D6365FA0094FDFA /* ToastModifier.swift in Sources */,
				7220C121984C1EB93EB69C08 /* OrderProductCard.swift in Sources */,
				18F735F62CCE8B5800F68F62 /* WishlistModel.swift in Sources */,
				9EE53AA536F601B2E2362DCD /* Addressitem1Cell.swift in Sources */,
				EA1821672CD03E52254998D1 /* DeliveryAddressView.swift in Sources */,
				13CAE7984487B97DB95AE808 /* AddressCardView.swift in Sources */,
				188221ED2CF39767005FB2C9 /* ProductListingModel.swift in Sources */,
				82D1AC340391CE43FD68D4CB /* DeliveryaddressaddView.swift in Sources */,
				18F735DB2CCD164600F68F62 /* UserDefaults.swift in Sources */,
				FA1BC6C9AAABFD0B94B8D378 /* Userprofile3Cell.swift in Sources */,
				B0E48694141B67BFB52F9F49 /* ChangepasswordView.swift in Sources */,
				18B318762DB7A17F006539AF /* BrandListingView.swift in Sources */,
				BC59814AA2656A8A602DCD10 /* TrackorderOneView.swift in Sources */,
				18C4580B2D613A4800EA8FF6 /* WishlistCellView.swift in Sources */,
				18F735E02CCD171500F68F62 /* BaseAPI.swift in Sources */,
				18DBB1F02C9D8B5900C41BF0 /* StringExtension.swift in Sources */,
				B11EBAF289C08EE60A658C7E /* MenuView.swift in Sources */,
				2631853A54E05ECDBA5E4DAB /* TabsView.swift in Sources */,
				CAF71292179972BFF6FB75AB /* PagerView.swift in Sources */,
				189F9CB62DEA5D6C00191472 /* OrderFailureView.swift in Sources */,
				3CC020799080F9C9F2005A4E /* PageIndicator.swift in Sources */,
				20929CADD630D98334856C94 /* CalendarModuleView.swift in Sources */,
				8417A308917650FC141BEE92 /* OTPReprasentable.swift in Sources */,
				35FC44CFFE9E8472658F340B /* OTPTextField.swift in Sources */,
				4BAC3AB0B84981BD7FE2605C /* OTPFieldView.swift in Sources */,
				189ABE7E2D25BED700234B5D /* GenericPaginatedView.swift in Sources */,
				18DBB1EE2C9D88F500C41BF0 /* Route.swift in Sources */,
				7B465BEFCB824C5046D62C42 /* BottomSheetView.swift in Sources */,
				188222012CF65BA0005FB2C9 /* AddressModel.swift in Sources */,
				A8690BF1DC8F21ABE7973E57 /* RadioGroupButton.swift in Sources */,
				493C2E7775B23433C9024EEC /* FSPageControlSUI.swift in Sources */,
				18DBB1EC2C9D881D00C41BF0 /* DashbordModel.swift in Sources */,
				DF89A93DDDC9300DAFD3DCDD /* FSPagerViewSUI.swift in Sources */,
				93F5021C27B897008C2FF55D /* FSPagerViewSUIOrigin.swift in Sources */,
				EF72B9FF93605E008C13BF63 /* ForgotpasswordViewModel.swift in Sources */,
				C3E1E4193F7A3A0802D03CE4 /* PasswordSucessViewModel.swift in Sources */,
				188221FE2CF641BA005FB2C9 /* AddressPopUpView.swift in Sources */,
				51AA30F345B43119BC63C2BE /* HomeViewModel.swift in Sources */,
				18B31AF22D58078400471476 /* RichTextView.swift in Sources */,
				18D1706B2DC233A4009E0CED /* IntegerExtension.swift in Sources */,
				18E2DDF82D28757600674FE6 /* ExpandableView.swift in Sources */,
				617CABAF51E2E04AE6162FDA /* CategoryOneViewModel.swift in Sources */,
				C4ED323E83E0730B18199294 /* TrackorderViewModel.swift in Sources */,
				188222032CF6723E005FB2C9 /* OrderSuccessView.swift in Sources */,
				BB186D0D942A0EF817D3754A /* ProductDetailViewModel.swift in Sources */,
				ACDC77D560235E989A87BE8A /* ProductpageViewModel.swift in Sources */,
				B7B5283BB3122EF032CAB711 /* OrderHistoryTabContainerViewModel.swift in Sources */,
				18F735E52CCD175E00F68F62 /* DateExtension.swift in Sources */,
				188221EB2CF396B4005FB2C9 /* ColorExtension.swift in Sources */,
				D0B6DF7F327E1639084D0BAE /* OrderinfoViewModel.swift in Sources */,
				18B31ADF2D57823400471476 /* DoubleExtension.swift in Sources */,
				18B318742DB7988E006539AF /* BrandListingTabContainerViewModel.swift in Sources */,
				B2E4988EFC2450C1E1371232 /* CartViewModel.swift in Sources */,
				97DFC2483288B894DFD38C02 /* AccountSettingsViewModel.swift in Sources */,
				18DBB1F52C9D8D7500C41BF0 /* TabExtension.swift in Sources */,
				18F7360A2CCEAE9C00F68F62 /* MainViewModel.swift in Sources */,
				0575DE67C49582B3EA1096EE /* SplashscreenViewModel.swift in Sources */,
				************************ /* TrackorderOneViewModel.swift in Sources */,
				18F735FE2CCEAA1A00F68F62 /* SuperModel.swift in Sources */,
				18B31AF02D57F75700471476 /* SideMenu.swift in Sources */,
				81F7DFA4C5CA928F36FB0986 /* SignupViewModel.swift in Sources */,
				F12384D4C83B4AE5FF6E8E68 /* SignInViewModel.swift in Sources */,
				20E1F667D96C3568FFC07006 /* CheckoutViewModel.swift in Sources */,
				180694602D63EABF0094FDFA /* AboutUsViewModel.swift in Sources */,
				3C8113748506E3024B5FF165 /* MenuViewModel.swift in Sources */,
				8150C84049C4A21DCE5838E1 /* ChangepasswordViewModel.swift in Sources */,
				88B333623800EAC1FDECC4A2 /* DeliveryaddressaddViewModel.swift in Sources */,
				18B31ADD2D57815000471476 /* FilterSortViewModel.swift in Sources */,
				30D08C90D9F5EE6E08E14B81 /* DatechooseViewModel.swift in Sources */,
				18C2A5C82D67CB31001AE292 /* RxProductCard.swift in Sources */,
				9A59706C5607C2BC2EBECE8D /* OrderhistorycancelledViewModel.swift in Sources */,
				18F735F42CCE874B00F68F62 /* homePageModel.swift in Sources */,
				E092BF8719BF60100134D33E /* AddaddressViewModel.swift in Sources */,
				06903244263BB3DD9756756B /* OrderhistorycompletedViewModel.swift in Sources */,
				070AE57BA8A6F5BA889CABF7 /* MyAccountViewModel.swift in Sources */,
				7E17AD94070324249B0B6AEE /* DeliveryAddressViewModel.swift in Sources */,
				8C66E70C08E23160688AA28F /* ForgotpasswordTwoViewModel.swift in Sources */,
				C71867BD89E0D1EC5585E5DD /* Forgotpassword2ViewModel.swift in Sources */,
				18DBB1E92C9D854200C41BF0 /* AppState.swift in Sources */,
				27F59F6BB01A3ED76BCC81DF /* ProductpageTabContainerViewModel.swift in Sources */,
				************************ /* CategoryViewModel.swift in Sources */,
				18DBB1E62C9D84DD00C41BF0 /* RouterManager.swift in Sources */,
				18F735D72CCD160700F68F62 /* TargetType.swift in Sources */,
				189F9C9C2DE89D9E00191472 /* TapCheckoutView.swift in Sources */,
				189F9C9D2DE89D9E00191472 /* UIViewController+Extensions.swift in Sources */,
				189F9C9E2DE89D9E00191472 /* TapPaymentManager.swift in Sources */,
				189F9C9F2DE89D9E00191472 /* TapPaymentConfig.swift in Sources */,
				0D2681E195BAFC66FCA6B15E /* OrderhistoryViewModel.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		88ED74F1272FFE6F0088E3EF /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 18925D8C2DEB2E3F003ED506 /* Dev.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.5;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		88ED74F2272FFE6F0088E3EF /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 18925D8E2DEB2E4D003ED506 /* Prod.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.5;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		88ED74F4272FFE6F0088E3EF /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DF9A55322CBE29B17A2B52E9 /* Pods-Wasfa.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Wasfa/Wasfa.entitlements;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_ASSET_PATHS = "\"Wasfa/Preview Content\"";
				DEVELOPMENT_TEAM = Z52876K599;
				ENABLE_PREVIEWS = YES;
				INFOPLIST_FILE = Wasfa/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Wasfa Rx";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.wasfa.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		88ED74F5272FFE6F0088E3EF /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3EA704986E45580EBFD52160 /* Pods-Wasfa.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Wasfa/Wasfa.entitlements;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_ASSET_PATHS = "\"Wasfa/Preview Content\"";
				DEVELOPMENT_TEAM = Z52876K599;
				ENABLE_PREVIEWS = YES;
				INFOPLIST_FILE = Wasfa/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Wasfa Rx";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.wasfa.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		88ED74DF272FFE6E0088E3EF /* Build configuration list for PBXProject "Wasfa" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				88ED74F1272FFE6F0088E3EF /* Debug */,
				88ED74F2272FFE6F0088E3EF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		88ED74F3272FFE6F0088E3EF /* Build configuration list for PBXNativeTarget "Wasfa" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				88ED74F4272FFE6F0088E3EF /* Debug */,
				88ED74F5272FFE6F0088E3EF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCLocalSwiftPackageReference section */
		18ECBB592D9E8D9A0016358A /* XCLocalSwiftPackageReference "WrappingHStack-main" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = "WrappingHStack-main";
		};
		18EED2902C958FAE00FFBCCA /* XCLocalSwiftPackageReference "FSPagerView-master" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = "FSPagerView-master";
		};
/* End XCLocalSwiftPackageReference section */

/* Begin XCRemoteSwiftPackageReference section */
		1806944E2D63664D0094FDFA /* XCRemoteSwiftPackageReference "SwipeActions" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/aheze/SwipeActions.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.1.0;
			};
		};
		187FEECE2CD670B300EAC51C /* XCRemoteSwiftPackageReference "SDWebImageSwiftUI" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SDWebImage/SDWebImageSwiftUI.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 3.1.3;
			};
		};
		187FEED12CD6729500EAC51C /* XCRemoteSwiftPackageReference "SDWebImageWebPCoder" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SDWebImage/SDWebImageWebPCoder.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.14.6;
			};
		};
		18B3186D2DB66FD3006539AF /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/firebase/firebase-ios-sdk.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 11.11.0;
			};
		};
		18B31AEC2D57EFBA00471476 /* XCRemoteSwiftPackageReference "SDWebImageSVGCoder" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SDWebImage/SDWebImageSVGCoder.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.8.0;
			};
		};
		18B31AF42D5807B700471476 /* XCRemoteSwiftPackageReference "RichText" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/NuPlay/RichText.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.5.0;
			};
		};
		18ECBB5C2D9E8F580016358A /* XCRemoteSwiftPackageReference "swiftui-sliders" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/spacenation/swiftui-sliders";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.1.0;
			};
		};
		18EED28D2C958F6300FFBCCA /* XCRemoteSwiftPackageReference "FSCalendar" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/WenchaoD/FSCalendar.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.8.4;
			};
		};
		18EED2932C95903900FFBCCA /* XCRemoteSwiftPackageReference "Alamofire" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/Alamofire/Alamofire";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.9.1;
			};
		};
		18F735DC2CCD165800F68F62 /* XCRemoteSwiftPackageReference "SecureDefaults" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/vpeschenkov/SecureDefaults.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.2.2;
			};
		};
		18F735E12CCD173000F68F62 /* XCRemoteSwiftPackageReference "SwiftyJSON" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SwiftyJSON/SwiftyJSON.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.0.2;
			};
		};
		18F736152CCEE6FA00F68F62 /* XCRemoteSwiftPackageReference "SDWebImageSVGKitPlugin" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SDWebImage/SDWebImageSVGKitPlugin.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.4.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		1806944F2D63664D0094FDFA /* SwipeActions */ = {
			isa = XCSwiftPackageProductDependency;
			package = 1806944E2D63664D0094FDFA /* XCRemoteSwiftPackageReference "SwipeActions" */;
			productName = SwipeActions;
		};
		187FEECF2CD670B300EAC51C /* SDWebImageSwiftUI */ = {
			isa = XCSwiftPackageProductDependency;
			package = 187FEECE2CD670B300EAC51C /* XCRemoteSwiftPackageReference "SDWebImageSwiftUI" */;
			productName = SDWebImageSwiftUI;
		};
		187FEED22CD6729500EAC51C /* SDWebImageWebPCoder */ = {
			isa = XCSwiftPackageProductDependency;
			package = 187FEED12CD6729500EAC51C /* XCRemoteSwiftPackageReference "SDWebImageWebPCoder" */;
			productName = SDWebImageWebPCoder;
		};
		18B3186E2DB66FD3006539AF /* FirebaseMessaging */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18B3186D2DB66FD3006539AF /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseMessaging;
		};
		18B31AED2D57EFBA00471476 /* SDWebImageSVGCoder */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18B31AEC2D57EFBA00471476 /* XCRemoteSwiftPackageReference "SDWebImageSVGCoder" */;
			productName = SDWebImageSVGCoder;
		};
		18B31AF52D5807B700471476 /* RichText */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18B31AF42D5807B700471476 /* XCRemoteSwiftPackageReference "RichText" */;
			productName = RichText;
		};
		18ECBB5A2D9E8D9A0016358A /* WrappingHStack */ = {
			isa = XCSwiftPackageProductDependency;
			productName = WrappingHStack;
		};
		18ECBB5D2D9E8F580016358A /* Sliders */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18ECBB5C2D9E8F580016358A /* XCRemoteSwiftPackageReference "swiftui-sliders" */;
			productName = Sliders;
		};
		18EED28E2C958F6300FFBCCA /* FSCalendar */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18EED28D2C958F6300FFBCCA /* XCRemoteSwiftPackageReference "FSCalendar" */;
			productName = FSCalendar;
		};
		18EED2912C958FAE00FFBCCA /* FSPagerView */ = {
			isa = XCSwiftPackageProductDependency;
			productName = FSPagerView;
		};
		18EED2942C95903900FFBCCA /* Alamofire */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18EED2932C95903900FFBCCA /* XCRemoteSwiftPackageReference "Alamofire" */;
			productName = Alamofire;
		};
		18F735DD2CCD165800F68F62 /* SecureDefaults */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18F735DC2CCD165800F68F62 /* XCRemoteSwiftPackageReference "SecureDefaults" */;
			productName = SecureDefaults;
		};
		18F735E22CCD173000F68F62 /* SwiftyJSON */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18F735E12CCD173000F68F62 /* XCRemoteSwiftPackageReference "SwiftyJSON" */;
			productName = SwiftyJSON;
		};
		18F736162CCEE6FA00F68F62 /* SDWebImageSVGKitPlugin */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18F736152CCEE6FA00F68F62 /* XCRemoteSwiftPackageReference "SDWebImageSVGKitPlugin" */;
			productName = SDWebImageSVGKitPlugin;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 88ED74DC272FFE6E0088E3EF /* Project object */;
}
