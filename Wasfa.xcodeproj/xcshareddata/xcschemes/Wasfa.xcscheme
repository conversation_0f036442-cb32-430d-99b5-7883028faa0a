<?xml version="1.0" encoding="UTF-8"?>
<Scheme
   LastUpgradeVersion = "1630"
   version = "1.7">
   <BuildAction
      parallelizeBuildables = "YES"
      buildImplicitDependencies = "YES"
      buildArchitectures = "Automatic">
      <BuildActionEntries>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "88ED74E3272FFE6E0088E3EF"
               BuildableName = "Wasfa.app"
               BlueprintName = "Wasfa"
               ReferencedContainer = "container:Wasfa.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
      </BuildActionEntries>
   </BuildAction>
   <TestAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      shouldUseLaunchSchemeArgsEnv = "YES"
      shouldAutocreateTestPlan = "YES">
   </TestAction>
   <LaunchAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      launchStyle = "0"
      useCustomWorkingDirectory = "NO"
      ignoresPersistentStateOnLaunch = "NO"
      debugDocumentVersioning = "YES"
      debugServiceExtension = "internal"
      allowLocationSimulation = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "88ED74E3272FFE6E0088E3EF"
            BuildableName = "Wasfa.app"
            BlueprintName = "Wasfa"
            ReferencedContainer = "container:Wasfa.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
      <EnvironmentVariables>
         <EnvironmentVariable
            key = "TAP_SANDBOX_KEY"
            value = "sk_test_e7HZn30mqsuEPLCN9JbUO8I6"
            isEnabled = "YES">
         </EnvironmentVariable>
         <EnvironmentVariable
            key = "TAP_PRODUCTION_KEY"
            value = "********************************"
            isEnabled = "YES">
         </EnvironmentVariable>
         <EnvironmentVariable
            key = "TAP_MERCHANT_ID"
            value = "M370"
            isEnabled = "YES">
         </EnvironmentVariable>
         <EnvironmentVariable
            key = "TAP_SANDBOX_PUBLIC_KEY"
            value = "pk_test_n6wzypZ01i4JOrsR5oMYcCAx"
            isEnabled = "YES">
         </EnvironmentVariable>
         <EnvironmentVariable
            key = "TAP_PRODUCTION_PUBLIC_KEY"
            value = "pk_live_8waQDiY6Vo0HrKznGNmFy54u"
            isEnabled = "YES">
         </EnvironmentVariable>
      </EnvironmentVariables>
   </LaunchAction>
   <ProfileAction
      buildConfiguration = "Release"
      shouldUseLaunchSchemeArgsEnv = "YES"
      savedToolIdentifier = ""
      useCustomWorkingDirectory = "NO"
      debugDocumentVersioning = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "88ED74E3272FFE6E0088E3EF"
            BuildableName = "Wasfa.app"
            BlueprintName = "Wasfa"
            ReferencedContainer = "container:Wasfa.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
   </ProfileAction>
   <AnalyzeAction
      buildConfiguration = "Debug">
   </AnalyzeAction>
   <ArchiveAction
      buildConfiguration = "Release"
      revealArchiveInOrganizer = "YES">
   </ArchiveAction>
</Scheme>
