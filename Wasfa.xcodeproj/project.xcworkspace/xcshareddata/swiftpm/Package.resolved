{"originHash": "8515e1d04a39a693d624c18187b9621fec9cf6f6df16181c11ceb949297689b2", "pins": [{"identity": "abseil-cpp-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/abseil-cpp-binary.git", "state": {"revision": "bbe8b69694d7873315fd3a4ad41efe043e1c07c5", "version": "1.2024072200.0"}}, {"identity": "alamofire", "kind": "remoteSourceControl", "location": "https://github.com/Alamofire/Alamofire", "state": {"revision": "513364f870f6bfc468f9d2ff0a95caccc10044c5", "version": "5.10.2"}}, {"identity": "app-check", "kind": "remoteSourceControl", "location": "https://github.com/google/app-check.git", "state": {"revision": "61b85103a1aeed8218f17c794687781505fbbef5", "version": "11.2.0"}}, {"identity": "cocoalumberjack", "kind": "remoteSourceControl", "location": "https://github.com/CocoaLumberjack/CocoaLumberjack.git", "state": {"revision": "4b8714a7fb84d42393314ce897127b3939885ec3", "version": "3.8.5"}}, {"identity": "firebase-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/firebase/firebase-ios-sdk.git", "state": {"revision": "d1f7c7e8eaa74d7e44467184dc5f592268247d33", "version": "11.11.0"}}, {"identity": "f<PERSON>lendar", "kind": "remoteSourceControl", "location": "https://github.com/WenchaoD/FSCalendar.git", "state": {"revision": "0fbdec5172fccb90f707472eeaea4ffe095278f6", "version": "2.8.4"}}, {"identity": "googleappmeasurement", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleAppMeasurement.git", "state": {"revision": "dd89fc79a77183830742a16866d87e4e54785734", "version": "11.11.0"}}, {"identity": "googledatatransport", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleDataTransport.git", "state": {"revision": "617af071af9aa1d6a091d59a202910ac482128f9", "version": "10.1.0"}}, {"identity": "googleutilities", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleUtilities.git", "state": {"revision": "53156c7ec267db846e6b64c9f4c4e31ba4cf75eb", "version": "8.0.2"}}, {"identity": "grpc-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/grpc-binary.git", "state": {"revision": "cc0001a0cf963aa40501d9c2b181e7fc9fd8ec71", "version": "1.69.0"}}, {"identity": "gtm-session-fetcher", "kind": "remoteSourceControl", "location": "https://github.com/google/gtm-session-fetcher.git", "state": {"revision": "4d70340d55d7d07cc2fdf8e8125c4c126c1d5f35", "version": "4.4.0"}}, {"identity": "interop-ios-for-google-sdks", "kind": "remoteSourceControl", "location": "https://github.com/google/interop-ios-for-google-sdks.git", "state": {"revision": "040d087ac2267d2ddd4cca36c757d1c6a05fdbfe", "version": "101.0.0"}}, {"identity": "leveldb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/leveldb.git", "state": {"revision": "a0bc79961d7be727d258d33d5a6b2f1023270ba1", "version": "1.22.5"}}, {"identity": "libwebp-xcode", "kind": "remoteSourceControl", "location": "https://github.com/SDWebImage/libwebp-Xcode.git", "state": {"revision": "b2b1d20a90b14d11f6ef4241da6b81c1d3f171e4", "version": "1.3.2"}}, {"identity": "nanopb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/nanopb.git", "state": {"revision": "b7e1104502eca3a213b46303391ca4d3bc8ddec1", "version": "2.30910.0"}}, {"identity": "promises", "kind": "remoteSourceControl", "location": "https://github.com/google/promises.git", "state": {"revision": "540318ecedd63d883069ae7f1ed811a2df00b6ac", "version": "2.4.0"}}, {"identity": "richtext", "kind": "remoteSourceControl", "location": "https://github.com/NuPlay/RichText.git", "state": {"revision": "3910393f3837a21fd2e414b758942ffd2d62aaa2", "version": "2.5.0"}}, {"identity": "sdwebimage", "kind": "remoteSourceControl", "location": "https://github.com/SDWebImage/SDWebImage.git", "state": {"revision": "10d06f6a33bafae8c164fbfd1f03391f6d4692b3", "version": "5.20.0"}}, {"identity": "sdwebimagesvgcoder", "kind": "remoteSourceControl", "location": "https://github.com/SDWebImage/SDWebImageSVGCoder.git", "state": {"revision": "85b5d58ad02c207c496fa34426dc6560d6ae32f0", "version": "1.8.0"}}, {"identity": "sdwebimagesvgkitplugin", "kind": "remoteSourceControl", "location": "https://github.com/SDWebImage/SDWebImageSVGKitPlugin.git", "state": {"revision": "fb16805b772d9a1dfa31a7665f64a557f3b52781", "version": "1.4.0"}}, {"identity": "sdwebimageswiftui", "kind": "remoteSourceControl", "location": "https://github.com/SDWebImage/SDWebImageSwiftUI.git", "state": {"revision": "451c6dfd5ecec2cf626d1d9ca81c2d4a60355172", "version": "3.1.3"}}, {"identity": "sdwebimagewebpcoder", "kind": "remoteSourceControl", "location": "https://github.com/SDWebImage/SDWebImageWebPCoder.git", "state": {"revision": "f534cfe830a7807ecc3d0332127a502426cfa067", "version": "0.14.6"}}, {"identity": "securedefaults", "kind": "remoteSourceControl", "location": "https://github.com/vpeschenkov/SecureDefaults.git", "state": {"revision": "3f9d5d19f7401250791840bff857ed5f6b8c8ddf", "version": "1.2.2"}}, {"identity": "svgkit", "kind": "remoteSourceControl", "location": "https://github.com/SVGKit/SVGKit.git", "state": {"revision": "58152b9f7c85eab239160b36ffdfd364aa43d666", "version": "3.0.0"}}, {"identity": "swift-log", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-log", "state": {"revision": "96a2f8a0fa41e9e09af4585e2724c4e825410b91", "version": "1.6.2"}}, {"identity": "swift-protobuf", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-protobuf.git", "state": {"revision": "d72aed98f8253ec1aa9ea1141e28150f408cf17f", "version": "1.29.0"}}, {"identity": "swiftui-sliders", "kind": "remoteSourceControl", "location": "https://github.com/spacenation/swiftui-sliders", "state": {"revision": "d5a7d856655d5c91f891c2b69d982c30fd5c7bdf", "version": "2.1.0"}}, {"identity": "<PERSON><PERSON><PERSON><PERSON>", "kind": "remoteSourceControl", "location": "https://github.com/SwiftyJSON/SwiftyJSON.git", "state": {"revision": "af76cf3ef710b6ca5f8c05f3a31307d44a3c5828", "version": "5.0.2"}}, {"identity": "swipeactions", "kind": "remoteSourceControl", "location": "https://github.com/aheze/SwipeActions.git", "state": {"revision": "41e6f6dce02d8cfa164f8c5461a41340850ca3ab", "version": "1.1.0"}}], "version": 3}