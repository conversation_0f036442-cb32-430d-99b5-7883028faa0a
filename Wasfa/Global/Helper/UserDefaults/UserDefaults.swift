//
//  UserDefaults.swift
//  Wasfa
//
//  Created by Apple on 26/10/2024.
//

import SecureDefaults
import UIKit

class UserDefaultsSecure: NSObject {
    let defaults = SecureDefaults()
    static let sharedInstance = UserDefaultsSecure()

    override private init() {
        if !defaults.isKeyCreated {
            defaults.password = UUID().uuidString
        }
    }

    static var isLoggedIn: Bool {
        get {
            UserDefaultsSecure.sharedInstance.defaults.bool(forKey: .isLoggedInKey)
        }
        set {
            UserDefaultsSecure.sharedInstance.defaults.set(newValue, forKey: .isLoggedInKey)
        }
    }

    static var deviceID: String {
        guard let uuid = UIDevice.current.identifierForVendor?.uuidString else { return UUID().uuidString }
        return uuid
    }

    func setGeneratedTokenStringValue(value: String?) {
        defaults.set(value, forKey: .tokenKey)
    }

    func getGeneratedToken() -> String? {
        return defaults.string(forKey: .tokenKey)
    }

//    func setGeneratedToken(value: Data) {
//        defaults.set(value, forKey: .tokenDataKey)
//    }
//
//    func getGeneratedToken() -> CreateBaseUrlGenerateTokenResponseData? {
//        if let data = defaults.data(forKey: .tokenDataKey) {
//            do {
//                return try JSONDecoder()
//                    .decode(CreateBaseUrlGenerateTokenResponseData.self, from: data)
//            } catch {
//                print("Unable to Decode")
//            }
//        }
//        return nil
//    }

    func setRegistrationData(value: Data) {
        print(value)
        defaults.set(value, forKey: .registrationDataKey)
    }

//    func getRegistrationData() -> UserRegister? {
//        if let data = defaults.data(forKey: .registrationDataKey) {
//            do {
//                let userRegister = try JSONDecoder().decode(UserRegister.self, from: data)
//                return userRegister
//            } catch {
//                print("Unable to Decode")
//            }
//        }
//        return nil
//    }

    // MARK: set and get user perform on local data

    func setUser(value: Data) {
        defaults.set(value, forKey: .userKey)
    }

//    func getUser() -> UserModel? {
//        if let data = defaults.data(forKey: .userKey) {
//            do {
//
//                let userModel = try JSONDecoder().decode(UserModel.self, from: data)
//                return userModel
//            } catch {
//                print("Unable to Decode")
//            }
//        }
//        return nil
//    }
}

extension String {
    // MARK: user keys

    static let userKey = "user_key"
    static let registrationDataKey = "registered_user_data_key"
    static let tokenDataKey = "generated_token_key"
    static let tokenKey = "generated_token_string_key"
    static let isLoggedInKey = "is_logged_in_key"
}


