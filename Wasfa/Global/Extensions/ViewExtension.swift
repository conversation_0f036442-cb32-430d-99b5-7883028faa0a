//
//  ViewExtension.swift
//  Wasfa
//

import Foundation
import SwiftUI
import UIKit

// Hide default navigation bar from Navigation link screen.
extension View {
    func hideNavigationBar() -> some View {
        self
            .navigationBarTitle("", displayMode: .inline)
            .navigationBarHidden(true)
            .navigationViewStyle(StackNavigationViewStyle())
    }

    @ViewBuilder func visibility(_ visibility: ViewVisibility) -> some View {
        if visibility != .gone {
            if visibility == .visible {
                self
            } else {
                hidden()
            }
        }
    }

    @ViewBuilder func `if`<Content: View>(_ condition: Bool, transform: (Self) -> Content) -> some View {
        if condition {
//            withAnimation(.bouncy) {
            transform(self)
//            }

        } else {
//            withAnimation(.bouncy) {
            self
//            }
        }
    }
    
    
    func hideNavigationBar(_ hide: Bool = true) -> some View {
        toolbar(hide ? .hidden : .visible, for: .tabBar)
            .toolbar(hide ? .hidden : .visible, for: .navigationBar)
            .navigationBarTitle("", displayMode: .inline)
            .navigationBarBackButtonHidden()
    }
    
    
    
}

enum ViewVisibility: CaseIterable {
    case visible, // view is fully visible
         invisible, // view is hidden but takes up space
         gone // view is fully removed from the view hierarchy
}


struct ViewDidLoadModifier: ViewModifier {
    @State private var didLoad = false
    private let action: (() -> Void)?

    init(perform action: (() -> Void)? = nil) {
        self.action = action
    }

    func body(content: Content) -> some View {
        content.onAppear {
            if self.didLoad == false {
                self.didLoad = true
                self.action?()
            }
        }
    }
}

extension View {
    func onLoad(perform action: (() -> Void)? = nil) -> some View {
        modifier(ViewDidLoadModifier(perform: action))
    }
}



@MainActor // Ensure main-thread safety for environment access
struct InjectEnvironmentValuesIntoViewModelModifier<T: SuperViewModel>: ViewModifier {
    @ObservedObject var viewModel: T
    @EnvironmentObject private var appState: AppState
    @Environment(\.routerManager) private var routerManager: RouterManager

    func body(content: Content) -> some View {
        content
            .onLoad {
                viewModel.initEnvironment(appState: appState, routerManager: routerManager)
            }
    }
}

extension View {
    func injectEnvironmentValues<T: SuperViewModel>(_ viewModel: T) -> some View {
        modifier(InjectEnvironmentValuesIntoViewModelModifier(viewModel: viewModel))
    }
}




extension View {
    func disableWithOpacity(_ disable: Bool, opacity: Double = 0.4) -> some View {
        modifier(DisableWithOpacityModifier(disable: disable, opacity: opacity))
    }
}

struct DisableWithOpacityModifier: ViewModifier {
    let disable: Bool
    let opacity: Double
    func body(content: Content) -> some View {
        ZStack(alignment: .bottomTrailing) {
            content
                .disabled(disable)
                .opacity(disable ? opacity : 1.0)
        }
    }
}


extension String {
    var localize:LocalizedStringKey { LocalizedStringKey(self) }
}


extension LocalizedStringKey {
    var stringify: String {
        let mirror = Mirror(reflecting: self)
        for child in mirror.children {
            if let label = child.label, label == "key", let key = child.value as? String {
                return NSLocalizedString(key, comment: "")
            }
        }
        return ""
    }
}




/// A custom view modifier to rotate a view based on the app's language.
struct LanguageBasedRotation: ViewModifier {
    let inverse: Bool
    @EnvironmentObject private var appState: AppState
    func body(content: Content) -> some View {
        content.rotationEffect(inverse ? appState.appLocale == .english ? .degrees(-180) : .degrees(0)   : appState.appLocale == .arabic ? .degrees(-180) : .degrees(0))
    }
}



extension View {
    /// Rotates the view based on the app's language direction.
    /// - Returns: A rotated view based on the app's language.
    func rotateBasedOnLanguage(inverse: Bool = false) -> some View {
        self.modifier(LanguageBasedRotation(inverse: inverse))
    }
}
