//
//  PaginatedViewModel.swift
//  Wasfa
//
//  Created by Apple on 26/11/2024.
//

import SwiftUI

class PaginatedViewModel: SuperViewModel {
    @Published var items: [ProductModel] = []
    @Published var isLoading = false
    @Published var hasMorePages = true
    
    private var currentPage = 1
    private let pageSize = 20
    private var isInitialLoad: Bool = false
    
    @Published var productListingRequest: ProductListingRequest
    init(productListingRequest: ProductListingRequest) {
        self.productListingRequest = productListingRequest
        super.init()
    }
    
    func fetchNextPage() {
        guard !isLoading && hasMorePages else { return }
            
        isLoading = true
        var request: ProductListingRequest = productListingRequest
        request.pageNo = currentPage
        request.perPage = pageSize
        
        onApiCall(api.productsList, parameters: request.toDictionary, withLoadingIndicator: !isInitialLoad) { response in
        
            DispatchQueue.main.async {
                self.isInitialLoad = true
                self.isLoading = false
                
                if let data = response.data {
                    if let products = data.products{self.items.append(contentsOf: products)}
                    
                    self.hasMorePages = self.items.count < data.totalCount
                    if self.hasMorePages {
                        self.currentPage += 1
                    }
                } else {
                    self.hasMorePages = false
                }
            }
        } onFailure: { _ in
            DispatchQueue.main.async {
                self.hasMorePages = false
            }
        }
    }
}
