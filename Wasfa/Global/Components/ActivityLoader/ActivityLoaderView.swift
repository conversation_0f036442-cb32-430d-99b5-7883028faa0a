//
//  ActivityLoaderView.swift
//  Wasfa
//
//  Created by Apple on 27/10/2024.
//

import SwiftUI

// struct ActivityLoaderBinder: View {
//    @Binding var isLoaderShow: Bool
//    var body: some View {
//        if isLoaderShow {
//            ZStack {
//                LottieView(name: "custom_loader", completion: { _ in})
//                    .frame(width: UIScreen.main.bounds.width / 3.5,
//                           height: UIScreen.main.bounds.height / 3.5)
//            }.frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height,
//                    alignment: .center)
//                .background(Color.black.opacity(0.25).edgesIgnoringSafeArea(.all))
//        }
//    }
// }

struct ActivityLoaderView: View {
    @State var isAnimating = false // <1>
    @State private var rotationAngle: Double = 0 // Rotation state variable
    var body: some View {
        ZStack {
//            LottieView(name: "custom_loader", completion: { _ in})
//                .frame(width: UIScreen.main.bounds.width / 3.5,
//                       height: UIScreen.main.bounds.height / 3.5)
            Circle()
                .fill(.white)
                .shadow(color: .gray.opacity(0.3),radius: 2)
                .overlay(alignment: .center) {
//                    ProgressView()
                    Image("img_wasfa_logo_1")
                        .resizable()
                        .scaledToFit()
                        .clipped()
                        .rotationEffect(.degrees(rotationAngle)) // Apply rotation
                        .animation(Animation.linear(duration: 2) // Smooth linear rotation
                                .repeatForever(autoreverses: false),
                            value: rotationAngle
                        )
                }
                .frame(width: 60, height: 60, alignment: .center)
                
                .onAppear {
                    // Trigger rotation when the view appears
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        rotationAngle = 360 // Start rotation slightly after move-in
                    }
                }
//                .offset(y:isAnimating ? 100 : 150)
//                .offset(y:isAnimating ? -50 : 50)
//                .animation(.interpolatingSpring(stiffness: 350, damping: 5, initialVelocity: 10).repeatForever(),value: isAnimating)
//                .onAppear {
//                    self.isAnimating = true // <2>
//                }
        }
        
        .frame(maxWidth: .infinity, maxHeight: .infinity)

//        .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height,
//                alignment: .center)
//            .background(Color.black.opacity(0.25).edgesIgnoringSafeArea(.all))
//        .background(.ultraThinMaterial).edgesIgnoringSafeArea(.all)
    }
}
