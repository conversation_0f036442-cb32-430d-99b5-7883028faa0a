//
//  NetworkImageView.swift
//  Wasfa
//
//  Created by Apple on 28/10/2024.
//

import SDWebImageSwiftUI
import SwiftUI

struct NetworkImageView: View {
    let originalColor: Bool
    let path: String?
    let contentMode: ContentMode
    let onFailure: ((Error) -> Void)?
    let onSuccess: ((PlatformImage, Data?, SDImageCacheType) -> Void)?
    let imageView: Bool

    // Add priority for image loading
    let priority: LoadingPriority

    // Cache configuration
    private static let imageCache = NSCache<NSString, UIImage>()
    private static let maxCacheSize = 50 * 1024 * 1024 // 50MB

    init(path: String?,
         contentMode: ContentMode = .fit,
         originalColor: Bool = true,
         imageView: Bool = false,
         priority: LoadingPriority = .normal,
         onSuccess: ((PlatformImage, Data?, SDImageCacheType) -> Void)? = nil,
         onFailure: ((Error) -> Void)? = nil)
    {
        self.originalColor = originalColor
        self.path = path
        self.imageView = imageView
        self.onSuccess = onSuccess
        self.onFailure = onFailure
        self.contentMode = contentMode
        self.priority = priority

        // Configure cache
        Self.imageCache.totalCostLimit = Self.maxCacheSize
    }

    var body: some View {
        Group {
            if let path = path, !path.isEmpty {
                if path.starts(with: "http") {
                    
                    WebImage(url: URL(string: path),options: [.lowPriority]) { image in
                           image.resizable() // Control layout like SwiftUI.AsyncImage, you must use this modifier or the view will use the image bitmap size
                       } placeholder: {
                        //    Color.gray.opacity(0.3)
                         Image(systemName: "photo").foregroundColor(.white)
                       }
                        .onSuccess { image, data, cacheType in
                            onSuccess?(image, data, cacheType)
                            // Cache the downloaded image
                            if cacheType == .none {
                                Self.imageCache.setObject(image, forKey: path as NSString)
                            }
                            
                            
                        }
                        .onFailure { error in
                            print("Image load failed: \(error.localizedDescription)")
                            onFailure?(error)
                        }
                        .indicator(.activity)
                        .transition(.fade(duration: 0.3))
                        .aspectRatio(contentMode: contentMode)
                        // Configure loading priority
                    
//                        .configure { view in
//                            view.priority = priority == .high ? .high : .normal
//                            view.maxMemoryCost = 10 * 1024 * 1024 // 10MB per image
//                        }
                } else {
                    Image(path)
                        .renderingMode(originalColor ? .original : .template)
                        .resizable()
                        .scaledToFit()
                }
            } else {
                placeholder
            }
        }
    }

    private var placeholder: some View {
        Image(systemName: "photo")
            .resizable()
            .scaledToFit()
            .foregroundStyle(.gray.opacity(0.6))
    }
}

// Loading priority enum
enum LoadingPriority {
    case high
    case normal
}

// Prefetching extension
extension NetworkImageView {
    static func prefetch(urls: [String]) {
        let urls = urls.compactMap { URL(string: $0) }
        SDWebImagePrefetcher.shared.prefetchURLs(urls)
    }

    static func cancelPrefetching() {
        SDWebImagePrefetcher.shared.cancelPrefetching()
    }
}
