//
//  AppConstants.swift
//  Wasfa

import Foundation

struct AppConstants {
    
    struct Server {
//        static let baseURL: String = "https://wasfakw.com/"
        static let baseURL: String = "https://stagging.apixrx.com/apixnew/"
        static let apiPath:String = "api/v2/"
    }
    static let OtpDigits:Int = 4
    static let unAuthenticateErrorString = "Unauthenticated."
    static let unAuthenticateErrorStringSecond = "Invalid credentials1"
    static let serverURL: String = "@{serverURL}"
    static let tabBarHeight:Double = 40
    static let tabBarTopPadding:Double = 35
    static let COUNTDOWN_TIMER_LENGTH = 60
}
