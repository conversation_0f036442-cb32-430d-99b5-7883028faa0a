import SwiftUI

struct ColorConstants {
    static let Black9003d: Color = .init("Black9003d")
    static let Red600: Color = .init("Red600")
    static let Pink300C1: Color = .init("Pink300C1")
    static let Cyan4000a: Color = .init("Cyan4000a")
    static let Cyan8003f: Color = .init("Cyan8003f")
    static let Green900: Color = .init("Green900")
    static let Gray80001: Color = .init("Gray80001")
    static let Blue6009e: Color = .init("Blue6009e")
    static let Black9003f: Color = .init("Black9003f")
    static let WhiteA70093: Color = .init("WhiteA70093")
    static let Black90042: Color = .init("Black90042")
    static let GreenA700: Color = .init("GreenA700")
    static let Black90001: Color = .init("Black90001")
    static let LightGreen600: Color = .init("LightGreen600")
    static let Teal900: Color = .init("Teal900")
    static let Gray20001: Color = .init("Gray20001")
    static let BlueGray900: Color = .init("BlueGray900")
    static let Blue300: Color = .init("Blue300")
    static let DescriptionText: Color = .init("DescriptionText")
    static let BlueLight: Color = .init("Blue.light")
    static let RedA700: Color = .init("RedA700")
    static let Gray600: Color = .init("Gray600")
    static let Gray400: Color = .init("Gray400")
    static let BlueGray100: Color = .init("BlueGray100")
    static let Gray800: Color = .init("Gray800")
    static let Gray500C4: Color = .init("Gray500C4")
    static let Black9000f: Color = .init("Black9000f")
    static let Gray200: Color = .init("Gray200")
    static let Black90054: Color = .init("Black90054")
    static let Pink30054: Color = .init("Pink30054")
    static let Blue60054: Color = .init("Blue60054")
    static let Gray70002: Color = .init("Gray70002")
    static let WhiteA700: Color = .init("WhiteA700")
    static let ProductBG: Color = .init("ProductBG")
    static let Gray70001: Color = .init("Gray70001")
    static let Black9005b: Color = .init("Black9005b")
    static let BlueGray10001: Color = .init("BlueGray10001")
    static let BlueGray10002: Color = .init("BlueGray10002")
    static let Blue6003a: Color = .init("Blue6003a")
    static let LightGreen300: Color = .init("LightGreen300")
    static let Gray50: Color = .init("Gray50")
    static let Black9001e: Color = .init("Black9001e")
    static let Black90021: Color = .init("Black90021")
    static let Teal400: Color = .init("Teal400")
    static let Black90023: Color = .init("Black90023")
    static let Black900: Color = .init("Black900")
    static let Gray50001: Color = .init("Gray50001")
    static let Gray50003: Color = .init("Gray50003")
    static let Gray50002: Color = .init("Gray50002")
    static let Gray50004: Color = .init("Gray50004")
    static let Black900Bf: Color = .init("Black900Bf")
    static let Gray90002: Color = .init("Gray90002")
    static let Gray700: Color = .init("Gray700")
    static let Gray90003: Color = .init("Gray90003")
    static let Gray500: Color = .init("Gray500")
    static let Black9002b: Color = .init("Black9002b")
    static let Gray60001: Color = .init("Gray60001")
    static let Blue60038: Color = .init("Blue60038")
    static let Gray900: Color = .init("Gray900")
    static let Blue600: Color = .init("Blue600")
    static let Gray90001: Color = .init("Gray90001")
    static let Blue6002b: Color = .init("Blue6002b")
    static let Gray100: Color = .init("Gray100")
    static let Gray4009e: Color = .init("Gray4009e")
    static let Teal90026: Color = .init("Teal90026")
    static let Pink30038: Color = .init("Pink30038")
}
