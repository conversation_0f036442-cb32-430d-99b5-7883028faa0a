
import SwiftUI

struct StringConstants {
   
   
    static let kLblAddNewAddress2: LocalizedStringKey = " Add new address"
    static let kLblCreditCard: LocalizedStringKey = "Credit card"
 
    static let kLblKnetCard: LocalizedStringKey = "KNET Card"
    static let kLblNewEmail2: LocalizedStringKey = "New email"
    static let kLblWeightLoss: LocalizedStringKey = "Weight Loss"
    static let kLblStreetName: LocalizedStringKey = "Street Name"
    static let kMsgOutForDelivery: LocalizedStringKey = "Out for delivery "
    static let kLblOrderPlaced: LocalizedStringKey = "Order placed "

    static let kLblResetPassword: LocalizedStringKey = "Reset Password"
    static let kLblTrackNow: LocalizedStringKey = "Track now"
    static let kMsgDonTHaveAnAccount: LocalizedStringKey = "Don’t have an account?"
    static let kLblDateOfBirth: LocalizedStringKey = "Date of birth *"
    static let kLblNewPassword: LocalizedStringKey = "New Password"
    static let kLblOrderSummery: LocalizedStringKey = "Order summery"

    static let kLblLogOut: LocalizedStringKey = "Log out"
    static let kLblLogIn: LocalizedStringKey = "Login"
    static let kLblReadyToShip: LocalizedStringKey = "Ready to ship"
    static let kLblEdit: LocalizedStringKey = "Edit"
   
    static let kLblOrSignWith: LocalizedStringKey = "Or  sign with"
    
    static let kLblSpecialOffers: LocalizedStringKey = "Special Offers"
    
    static let kLblSize: LocalizedStringKey = "Size"
    static let kLblShopNow: LocalizedStringKey = "Shop Now"
    static let kLblOrderHistory: LocalizedStringKey = "Order History"
   
   
 
    static let kLblSchedule: LocalizedStringKey = "Schedule"
    static let kLblLastName: LocalizedStringKey = "Last name *"
    static let kLblGender: LocalizedStringKey = "Gender *"
   
    static let kLblAddAddress2: LocalizedStringKey = "Add address"
    static let kLblBackToLogin: LocalizedStringKey = "Back to Login"
    static let kMsgDonTWorryIt: LocalizedStringKey =
        "Don’t worry ! it occurs. Please enter the \nemail address linked with your account."
    static let kLblCancelOrder: LocalizedStringKey = "Cancel Order"
   
 
    static let kLblBrand: LocalizedStringKey = "Brand  :"
    static let kLblSort: LocalizedStringKey = "Sort"
    static let kLblDeliveryDate: LocalizedStringKey = "Delivery Date"
    static let kLblSize2: LocalizedStringKey = "size : "
    static let kHttpFail: LocalizedStringKey = "HTTP request failed"
    static let kLblOrderId: LocalizedStringKey = "Order ID *"

    static let kLblCompleted: LocalizedStringKey = "Completed"
    static let kLblMassagers: LocalizedStringKey = "Massagers"
    static let kLblScales: LocalizedStringKey = "Scales"
    static let kLblNotes: LocalizedStringKey = "Notes"
    static let kLblPassword: LocalizedStringKey = "Password"
    static let kMsgSorryThereIs: LocalizedStringKey = "Sorry, There is no shipping\naddress "
    static let kLblSendCode: LocalizedStringKey = "Send Code"
    static let kLblNewArrivals: LocalizedStringKey = "New Arrivals"
    static let kLblOldPassword2: LocalizedStringKey = "Old  password"
    static let kInvalidUrl: LocalizedStringKey = "Invalid URL"
    static let kLblApply: LocalizedStringKey = "Apply"
    static let kLblBuilding: LocalizedStringKey = "Building"
    static let kLblAvenue: LocalizedStringKey = "Avenue"

  
    static let kLblDiscount: LocalizedStringKey = "Discount"
    static let kMsgHiWelcomeBack: LocalizedStringKey = "Hi Welcome back, You’ve been missed"
    static let kLblMenu: LocalizedStringKey = "Menu"
    static let kLblCategories: LocalizedStringKey = "Categories"
    static let kLblEmail2: LocalizedStringKey = "Email"
    static let kLblSelectArea: LocalizedStringKey = "Select Area"
    static let kLblChangePassword: LocalizedStringKey = "Change password"

  
    static let kMsgAccountSettings2: LocalizedStringKey = "Account Settings"
    static let kLblContactUs: LocalizedStringKey = "Contact Us"
    static let kUnableToFetch: LocalizedStringKey = "Unable to fetch data"
    static let kLblAddAddress: LocalizedStringKey = "Add address"

    static let kLblSignUp2: LocalizedStringKey = "Sign Up"
    static let kLblNewPassword3: LocalizedStringKey = "New Password"
   
    static let kLblOrderDate: LocalizedStringKey = "Order date"
    static let kLblNewPassword2: LocalizedStringKey = "New password *"
    static let kLblDeliveryTo: LocalizedStringKey = "Delivery to"
    static let kLblBuyNow: LocalizedStringKey = "Buy now"
    static let kMsgYourOrderIsOut: LocalizedStringKey = "Your order is Out for Delivery"
    static let kLblOrderInfo2: LocalizedStringKey = "Order Info"
    static let kLblPrivacyPolicy: LocalizedStringKey = "Privacy Policy"
    static let kMsgDonTHaveAnAccount2: LocalizedStringKey = "Don’t have an account?"
    static let kLblSave: LocalizedStringKey = "Save"
    static let kMsgTermsAndConditions: LocalizedStringKey = "Terms And Conditions"
   
    static let kLblReOrder: LocalizedStringKey = "Re-Order"
    static let kLblTrackOrder2: LocalizedStringKey = "Track Order"
    static let kLblAddToCart: LocalizedStringKey = "Add to cart "
    static let kLblKnet: LocalizedStringKey = "Knet"
    static let kMsgCreateNewPassword: LocalizedStringKey = "Create new password"
    static let kMsgCashOnDelivery: LocalizedStringKey = "Cash on delivery"
  
    
   
    static let kLblTrackOrder: LocalizedStringKey = "Track Order"
   
    static let kLblLastName2: LocalizedStringKey = "Last name"
    static let kLblRefreshing: LocalizedStringKey = "Refreshing "
    static let kLblBestSellers: LocalizedStringKey = "Best Sellers"
    static let kLblWishlist: LocalizedStringKey = "Wishlist"
    static let kLblPhone: LocalizedStringKey = "Phone"
    static let kMsgAlreadyHaveAn: LocalizedStringKey = "Already have an account?  Sign In"
    static let kLblFirstName: LocalizedStringKey = "First name *"
    static let kLblBlock2: LocalizedStringKey = "Block"
    static let kLblOrderHistory2: LocalizedStringKey = "Order History"
   
    static let kMsgConfirmPassword: LocalizedStringKey = "Confirm Password"
    static let kLblSoldOut: LocalizedStringKey = "Sold Out"
    static let kLblConfirmEmail2: LocalizedStringKey = "Confirm email"
  
    static let kLblSignIn: LocalizedStringKey = "Sign In"
    static let kLblBlock: LocalizedStringKey = "Block"

   
  
    static let kLblNeedHelp: LocalizedStringKey = "Need help?"
    static let kMsgExambleGamilCom: LocalizedStringKey = "<EMAIL>"
   

    static let kMsgForgotPassword: LocalizedStringKey = "Forgot password?"
  
    static let kLblBrands: LocalizedStringKey = "Brands"
    static let kLblBuilding2: LocalizedStringKey = "Building"
   
  
    static let kLblConfirmEmail: LocalizedStringKey = "Confirm email *"
    static let kLblOldPassword: LocalizedStringKey = "Old password *"
    static let kLblOldEmail: LocalizedStringKey = "Old email *"
    static let kLblPaymentOption: LocalizedStringKey = "Payment option"
    static let kLblMyAccount: LocalizedStringKey = "My Account"

  
   
   
  
    static let kLblCancelled: LocalizedStringKey = "Cancelled"
    static let kLblAddToWishlist: LocalizedStringKey = "Add to wishlist"
    static let kLblEnterOrderId: LocalizedStringKey = "Enter order ID"
    static let kMsgPasswordChanged: LocalizedStringKey = "Password Changed"
    static let kMsgAccountSettings: LocalizedStringKey = "Account Settings"
   

    static let kLblSubtotal: LocalizedStringKey = "Subtotal"
   
    static let kMsgRecommendedProduct: LocalizedStringKey = "Recommended Product "
    static let kMsg: LocalizedStringKey = "*****************"
  
   
    static let kLblFilter: LocalizedStringKey = "Filter"
    static let kLblFirstName3: LocalizedStringKey = "First Name"
    
 
    static let kMsgShippingAddress: LocalizedStringKey = "Shipping Address"
    
 

    static let kLblOnProgress: LocalizedStringKey = "On progress"
    static let kLblFirstName2: LocalizedStringKey = "First Name"
    static let kMsgDeliveryAddresses: LocalizedStringKey = "Delivery Address"
    static let kLblTotal: LocalizedStringKey = "Total"
    static let kLblAboutUs: LocalizedStringKey = "About Us"
    static let kLblToday: LocalizedStringKey = "Today"
    static let kLblVerify: LocalizedStringKey = "Verify"
    static let kMsgWhatAreYouLooking: LocalizedStringKey = "What are you looking for ? "
  
   
    static let kLblArea: LocalizedStringKey = "Area"
    static let kLblSeeAll: LocalizedStringKey = "See All"
    static let kMsgEnterPromoCode: LocalizedStringKey = "Enter promo code"
    static let kLblDelivery: LocalizedStringKey = "Delivery"

    static let kMsgOtpVerification: LocalizedStringKey = "OTP Verification"
  
    static let kLblName: LocalizedStringKey = "Name"
    static let kLblOrderInfo: LocalizedStringKey = "Order Info"
    static let kLblSeller: LocalizedStringKey = "Seller  :"
    static let kLblMedicalCare: LocalizedStringKey = "Medical Care"
    static let kLblAddNewAddress: LocalizedStringKey = "Add New Address"
    static let kLblName2: LocalizedStringKey = "Name"
 

    static let kLblMyCart: LocalizedStringKey = "My Cart"
    static let kMsgConfirmPassword2: LocalizedStringKey = "Confirm password *"
   
    static let kMsgConfirmPassword3: LocalizedStringKey = "Confirm Password"
    static let kMsgProceedToCheckout: LocalizedStringKey = "Proceed to checkout"
    static let kMsgYourOrderIsReady: LocalizedStringKey = "Your order is ready to ship"
    static let kLblEmail: LocalizedStringKey = "Email"
    static let kLblOrderProceed: LocalizedStringKey = "Order proceed "
 

   
    static let kMsgYourPasswordHas: LocalizedStringKey = "Your password has been changed\r\nsuccessfully"
    static let kLblPaymentMethod: LocalizedStringKey = "Payment method"
 
    static let kLblNewEmail: LocalizedStringKey = "New email *"

    static let kLblCheckout: LocalizedStringKey = "Checkout"
    static let kMsgChooseDelivery: LocalizedStringKey = "Choose delivery date"
    static let kLblFaqS: LocalizedStringKey = "FAQ’s"


    static let kLblAll: LocalizedStringKey = "All"
   
  
    static let kLblStreetName2: LocalizedStringKey = "Street Name"
    static let kMsgWeArePreparing: LocalizedStringKey = "we are preparing Your Order"
   
    static let kLbl96599999999: LocalizedStringKey = "Phone Number"
    static let kLblOrderConfirmed: LocalizedStringKey = "Order confirmed "
    static let kMsgDeliveryAddress: LocalizedStringKey = "Delivery Address"
}

