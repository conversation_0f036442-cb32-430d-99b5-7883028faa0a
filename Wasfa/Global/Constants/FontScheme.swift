import Foundation
import SwiftUI

class FontScheme: NSObject {
    static func kInterRegular(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kInterRegular, size: size)
    }

    static func kInterMedium(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kInterMedium, size: size)
    }

    static func kNunitoLight(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kNunitoLight, size: size)
    }

    static func kNunitoMedium(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kNunitoMedium, size: size)
    }

    static func kNunitoExtraBold(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kNunitoExtraBold, size: size)
    }

    static func kNunitoBold(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kNunitoBold, size: size)
    }

    static func kNunitoSemiBold(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kNunitoSemiBold, size: size)
    }

    static func kNunitoRegular(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kNunitoRegular, size: size)
    }

    static func kPlusJakartaSansRomanBold(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kPlusJakartaSansRomanBold, size: size)
    }

    static func kRobotoRomanRegular(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kRobotoRomanRegular, size: size)
    }
    
    static func kPoppins(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kPoppinsRegular, size: size)
    }

    static func kRobotoRomanMedium(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kRobotoRomanMedium, size: size)
    }

    static func kRobotoRomanLight(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kRobotoRomanLight, size: size)
    }

    static func kProximaNovaMedium(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kProximaNovaMedium, size: size)
    }

    static func fontFromConstant(fontName: String, size: CGFloat) -> Font {
        var result = Font.system(size: size)

        switch fontName {
        case "kInterRegular":
            result = self.kInterRegular(size: size)
        case "kInterMedium":
            result = self.kInterMedium(size: size)
        case "kNunitoLight":
            result = self.kNunitoLight(size: size)
        case "kNunitoMedium":
            result = self.kNunitoMedium(size: size)
        case "kNunitoExtraBold":
            result = self.kNunitoExtraBold(size: size)
        case "kNunitoBold":
            result = self.kNunitoBold(size: size)
        case "kNunitoSemiBold":
            result = self.kNunitoSemiBold(size: size)
        case "kNunitoRegular":
            result = self.kNunitoRegular(size: size)
        case "kPlusJakartaSansRomanBold":
            result = self.kPlusJakartaSansRomanBold(size: size)
        case "kRobotoRomanRegular":
            result = self.kRobotoRomanRegular(size: size)
        case "kRobotoRomanMedium":
            result = self.kRobotoRomanMedium(size: size)
        case "kRobotoRomanLight":
            result = self.kRobotoRomanLight(size: size)
        case "kProximaNovaMedium":
            result = self.kProximaNovaMedium(size: size)
        default:
            result = self.kInterRegular(size: size)
        }
        return result
    }

    enum FontConstant {
        /**
         * Please Add this fonts Manually
         */
        static let kInterRegular: String = "Inter"
        /**
         * Please Add this fonts Manually
         */
        static let kInterMedium: String = "Inter"
        /**
         * Please Add this fonts Manually
         */
        static let kNunitoLight: String = "Nunito"
        /**
         * Please Add this fonts Manually
         */
        static let kNunitoMedium: String = "Nunito"
        /**
         * Please Add this fonts Manually
         */
        static let kNunitoExtraBold: String = "Nunito"
        /**
         * Please Add this fonts Manually
         */
        static let kNunitoBold: String = "Nunito"
        /**
         * Please Add this fonts Manually
         */
        static let kNunitoSemiBold: String = "Nunito"
        /**
         * Please Add this fonts Manually
         */
        static let kNunitoRegular: String = "Nunito"
        /**
         * Please Add this fonts Manually
         */
        static let kPlusJakartaSansRomanBold: String = "PlusJakartaSansRoman"
        /**
         * Please Add this fonts Manually
         */
        static let kRobotoRomanRegular: String = "RobotoRoman"
        
        static let kPoppinsRegular: String = "Poppins"
        /**
         * Please Add this fonts Manually
         */
        static let kRobotoRomanMedium: String = "RobotoRoman"
        /**
         * Please Add this fonts Manually
         */
        static let kRobotoRomanLight: String = "RobotoRoman"
        /**
         * Please Add this fonts Manually
         */
        static let kProximaNovaMedium: String = "ProximaNova"
    }
}
