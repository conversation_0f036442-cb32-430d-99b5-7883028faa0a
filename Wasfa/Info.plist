<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Wasfa Rx</string>
	<key>CFBundlePackageType</key>
	<string>$(PRODUCT_BUNDLE_PACKAGE_TYPE)</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0</string>
	<key>CFBundleVersion</key>
	<string>1</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>UIAppFonts</key>
	<array>
		<string>InterMedium.ttf</string>
		<string>PoppinsLight.ttf</string>
		<string>PoppinsSemiBold.ttf</string>
		<string>PoppinsRegular.ttf</string>
		<string>PoppinsMedium.ttf</string>
		<string>PoppinsBold.ttf</string>
		<string>NunitoBold.ttf</string>
		<string>RobotoRomanMedium.ttf</string>
		<string>InterRegular.ttf</string>
		<string>NunitoSemiBold.ttf</string>
		<string>NunitoLight.ttf</string>
		<string>NunitoExtraBold.ttf</string>
		<string>RobotoRomanLight.ttf</string>
		<string>NunitoMedium.ttf</string>
		<string>PlusJakartaSansRomanBold.ttf</string>
		<string>RobotoRomanRegular.ttf</string>
		<string>NunitoRegular.ttf</string>
	</array>
	<key>UIApplicationSceneManifest</key>
	<dict>
		<key>UIApplicationSupportsMultipleScenes</key>
		<true/>
	</dict>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
		<string>processing</string>
		<string>fetch</string>
	</array>
	<key>UILaunchScreen</key>
	<dict/>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	</array>
    <key>TapSandboxKey</key>
    <string>$(TAP_SANDBOX_KEY)</string>

    <key>TapProductionKey</key>
    <string>$(TAP_PRODUCTION_KEY)</string>

    <key>TapMerchantID</key>
    <string>$(TAP_MERCHANT_ID)</string>

    <key>TapSandboxPublicKey</key>
    <string>$(TAP_SANDBOX_PUBLIC_KEY)</string>

    <key>TapProductionPublicKey</key>
    <string>$(TAP_PRODUCTION_PUBLIC_KEY)</string>
</dict>
</plist>
