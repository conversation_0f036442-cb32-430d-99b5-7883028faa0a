import SwiftUI

struct BrandListingTabContainerView: View {
    @StateObject private var viewModel: BrandListingTabContainerViewModel
    @FocusState private var isFocused: Bool
    init() {
        self._viewModel = StateObject(wrappedValue: BrandListingTabContainerViewModel())
      
    }
    var body: some View {
        Group{
            SuperView(pageState: $viewModel.pageState) {
                VStack(spacing: 0) {
                    searchBar
                    productListSection
                }
            }
        }
        .injectEnvironmentValues(viewModel)
    }
    private var searchBar: some View {
        VStack(spacing: 12.0.relativeHeight) {
            HStack(spacing: 12) {
                Image("img_search_light")
                    .frame(width: 23, height: 24)

                TextField(StringConstants.kMsgWhatAreYouLooking, text: $viewModel.search)
                    .focused($isFocused)
                    .font(FontScheme.kNunitoLight(size: getRelativeHeight(12.0)))
                    .foregroundColor(ColorConstants.Gray50004)
                   
            }
            .padding(.horizontal)
            .frame(width: getRelativeWidth(315.0), height: getRelativeHeight(50.0), alignment: .center)
            .overlay(Capsule().stroke(ColorConstants.Blue6009e, lineWidth: 1))
            .background(Capsule().fill(ColorConstants.Cyan4000a))
            .padding(.horizontal, getRelativeWidth(38.0))
            
        }
        .padding(.top, 6)
        .background(.white)
    }
    private var productListSection: some View {
        MainScrollBody(backButtonWithTitle: "Brands") {
            LazyVStack(pinnedViews: .sectionHeaders) {
                if viewModel.brandList.isEmpty && !viewModel.isInitialLoad {
                    ProgressView()
                        .frame(height: 200)
                } else {
                    BrandListingView(viewModel: viewModel)
                        .padding(.vertical)
                }
            }
            .background(ColorConstants.WhiteA700)
        }
        .scrollDismissesKeyboard(.immediately)
        // .id(viewModel.scrollViewID)
    }
}

//#Preview {
//    NavigationStack {
//        ProductListingTabContainerView(productListingRequest: .init(title: "Device"), isSearchFocused: false, categories: [])
//    }
//}
