import SwiftUI

struct BrandListingView: View {
    @StateObject var viewModel: BrandListingTabContainerViewModel

    private let columns: [GridItem] = [GridItem(), GridItem()]

    var body: some View {
        VStack(alignment: .center) {
            if !self.viewModel.brandList.isEmpty {
                Section {
                    LazyVGrid(columns: self.columns, spacing: 15.relativeHeight) {
                        ForEach(self.viewModel.brandList) { model in
                            ProductBrandCell(model: model, isBigger: true, onSelect: self.viewModel.onBrandSelect)
                                .onAppear { self.viewModel.loadMore(model: model) }
                                .frame(maxHeight: .infinity, alignment: .top)
                        }
                    }
                } footer: {
                    // Loading indicator
                    if self.viewModel.isLoading {
                        ProgressView("Loading...")

                            .padding(.vertical, 10)
                    }

                    // No more items indicator
                    if !self.viewModel.hasMorePages && !self.viewModel.isLoading {
                        Text("\(self.viewModel.brandList.count) of \(self.viewModel.totalCount) brands loaded")
                            .font(.footnote)
                            .foregroundColor(.gray)
                            .padding(.vertical, 10)
                    }
                }

            } else {
                // No product available ContentUnavailableView Placeholder
                ContentUnavailableView("No Brands Available", systemImage: "exclamationmark.triangle", description: Text("Please try again later."))
            }
        }
        .padding(.horizontal, getRelativeWidth(16.0))
        .background(ColorConstants.WhiteA700)
    }
}
