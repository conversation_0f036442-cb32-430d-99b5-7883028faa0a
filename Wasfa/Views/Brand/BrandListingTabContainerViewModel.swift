import Foundation
import SwiftUI

class BrandListingTabContainerViewModel: SuperViewModel {
    @Published var search: String = .init()
  
    @Published var brandList: [Brand] = []
    
    @Published var brandListingRequest: BrandListingRequest = .init()
    
    // variables for Pagination
    @Published var isLoading: Bool = false
    @Published var currentPage: Int = 1
    @Published var hasMorePages: Bool = true
   
    @Published var totalPages: Int = 0
    @Published var totalCount: Int = 0
    @Published var isInitialLoad: Bool = false
    
    private let pageSize = 20 // Adjust based on API
    
    override init() {
        super.init()
        loadBrandList(model: brandListingRequest)
        bindSearchText()
    }
    
    func bindSearchText() {
        // Debounce the search input by 0.5 seconds
        $search
            .debounce(for: .milliseconds(500), scheduler: RunLoop.main)
            .removeDuplicates() // Ignore duplicate searches
            .dropFirst() //
            .sink { [weak self] searchTerm in
                self?.brandListingRequest.name = searchTerm
                self?.resetPagination()
                if let brandListingRequest = self?.brandListingRequest {
                    self?.loadBrandList(model: brandListingRequest, isSearch: true)
                }
            }
            .store(in: &cancellables)
    }
    
    func resetPaginationState(clearData: Bool = true) {
        if clearData {
            brandList.removeAll()
        }
        isInitialLoad = false
        isLoading = false
        currentPage = 1
        hasMorePages = true
        totalPages = 0
        totalCount = 0
    }
    
    func resetPagination() {
        resetPaginationState()
    }
    
    func loadMore(model: Brand) {
        if brandList.last == model && !isLoading && hasMorePages {
            loadBrandList(model: brandListingRequest)
        }
    }
    
    func onBrandSelect(model: Brand) {
        routerManager?.push(to: .productListing(reuestModel: .init(brand:  model.id, title: model.name.capitalized), isSearchFocused: false), appState: appState)
    }
    
    func loadBrandList(model: BrandListingRequest, isSearch: Bool = false) {
        guard canFetchNextPage(isSearch: isSearch) else { return }
            
        isLoading = true
        var paginatedRequest: BrandListingRequest = model
        
        paginatedRequest.pageNo = currentPage
        paginatedRequest.perPage = pageSize
        
        onApiCall(api.brandList, parameters: paginatedRequest.toDictionary, dismissKeyboard: !isSearch, withLoadingIndicator: false) { response in
            self.isInitialLoad = true
            self.isLoading = false
                
            if let data = response.data {
              
                self.brandList.append(contentsOf: data.brands)
                
                
                self.totalPages = data.totalPageCount
                self.totalCount = data.totalCount
                self.hasMorePages = self.currentPage < self.totalPages
                if self.hasMorePages { self.currentPage += 1 }
            } else {
                self.hasMorePages = false
            }
            
        } onFailure: { _ in
            self.hasMorePages = false
        }
    }

    private func canFetchNextPage(isSearch: Bool = false) -> Bool {
        return !isLoading && hasMorePages || isSearch
    }
}

struct BrandListingRequest: Codable {
    var pageNo: Int = 1
    var perPage: Int = 20
    var name: String?
}
