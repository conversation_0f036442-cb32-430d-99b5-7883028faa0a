//  ProductCategoryCell.swift
//  Wasfa
//
//  Created by Apple on 06/02/2025.
//

import SwiftUI

struct ProductCategoryCell: View {
    @Binding var filter: ProductListingRequest?
    let category: CategoryModel.Category
    let categories: [CategoryModel.Category] // All categories list
    let currentLevel: Int // Track depth level
    let maxLevel: Int // New parameter to control max depth
    let onCheckmark: (CategoryModel.Category) -> Void

    @State private var isExpanded: Bool = false
    @State private var selectedSubCategory: CategoryModel.Category?

    private var isSelected: Bool {
        // Check if this category is selected at any level
        if filter?.categoryID == category.categoryID ||
           filter?.subCategoryID == category.categoryID ||
           filter?.superSubCategoryID == category.categoryID {
            return true
        }
        
        // If this is a parent category, check if any of its children or grandchildren are selected
        if let children = category.children {
            // Check if any direct child is selected
            if children.contains(where: { child in 
                filter?.subCategoryID == child.categoryID ||
                filter?.superSubCategoryID == child.categoryID
            }) {
                return true
            }
            
            // Check if any grandchild is selected
            for child in children {
                if let grandChildren = child.children,
                   grandChildren.contains(where: { grandChild in
                       filter?.superSubCategoryID == grandChild.categoryID
                   }) {
                    return true
                }
            }
        }
        
        return false
    }

    private func isAncestor(of category: CategoryModel.Category, for subCategoryID: Int?) -> Bool {
        guard let subCategoryID = subCategoryID else { return false }
        if category.categoryID == subCategoryID {
            return true
        }
        return category.children?.contains(where: { isAncestor(of: $0, for: subCategoryID) }) ?? false
    }

    var body: some View {
        VStack(alignment: .leading) {
            HStack {
                Button(action: {
                    let isSelected = filter?.subCategoryID == category.categoryID || filter?.categoryID == category.categoryID
                    toggleSelection(for: category, isSelected: isSelected)
                }) {
                    Image(systemName: isSelected ? "checkmark.square.fill" : "square")
                        .resizable()
                        .frame(width: 20, height: 20)
                        .foregroundColor(ColorConstants.Blue300)
                }

                Text(category.name)
                    .font(Font.custom("Nunito", size: 16))

                Spacer()

                if let children = category.children, !children.isEmpty, currentLevel < maxLevel {
                    Button(action: { withAnimation { isExpanded.toggle() } }) {
                        Image(systemName: isExpanded ? "minus" : "plus")
                            .foregroundColor(.gray)
                    }
                }
            }
            .padding(.vertical, 5)
            .onAppear {
                isExpanded = isAncestor(of: category, for: filter?.categoryID)
            }

            if isExpanded, let children = category.children, currentLevel < maxLevel {
                LazyVStack(alignment: .leading, spacing: 5) {
                    ForEach(children) { childCategory in
                        ProductCategoryCell(
                            filter: $filter,
                            category: childCategory,
                            categories: categories,
                            currentLevel: currentLevel + 1,
                            maxLevel: maxLevel,
                            onCheckmark: onCheckmark
                        )
                        .padding(.leading, 20)
                    }
                }
            }
        }
    }

    private func toggleSelection(for category: CategoryModel.Category, isSelected: Bool) {
        if currentLevel == 0 {
            handleParentSelection(category, isSelected: isSelected)
        } else {
            handleChildSelection(category, isSelected: isSelected)
        }
        onCheckmark(category)
    }

    private func handleParentSelection(_ category: CategoryModel.Category, isSelected: Bool) {
        if isSelected {
            // Deselect parent and all its descendants
            filter?.categoryID = nil
            filter?.subCategoryID = nil
            filter?.superSubCategoryID = nil
        } else {
            filter?.categoryID = category.categoryID
            filter?.subCategoryID = nil
        }
    }

    private func handleChildSelection(_ category: CategoryModel.Category, isSelected: Bool) {
        if isSelected {
            // Deselect the child category
            if filter?.subCategoryID == category.categoryID {
                filter?.subCategoryID = nil
                filter?.superSubCategoryID = nil
            } else if filter?.superSubCategoryID == category.categoryID {
                filter?.superSubCategoryID = nil
            }
            
            // Clear parent selection if no children are selected
            if filter?.superSubCategoryID == nil && filter?.subCategoryID == nil {
                filter?.categoryID = nil
            }
        } else {
            // Select the child category and its parent hierarchy
            if currentLevel == 1 {
                // Sub category (level 1) selection
                filter?.subCategoryID = category.categoryID
                filter?.superSubCategoryID = nil
                
                // Set parent category
                if let parentID = category.parentID,
                   let parent = categories.first(where: { $0.categoryID == parentID }) {
                    filter?.categoryID = parent.categoryID
                }
            } else if currentLevel == 2 {
                // Super sub category (level 2) selection
                filter?.categoryID = category.superParentID
                filter?.subCategoryID = category.parentID
                filter?.superSubCategoryID = category.categoryID
                
                // Find and set the immediate parent (sub category)
                if let parentID = category.parentID,
                   let parent = categories.first(where: { $0.categoryID == parentID }) {
                    filter?.subCategoryID = parent.categoryID
                    
                    // Find and set the top-level parent (main category)
                    if let grandParentID = parent.parentID,
                       let grandParent = categories.first(where: { $0.categoryID == grandParentID }) {
                        filter?.categoryID = grandParent.categoryID
                    }
                }
            }
        }
    }

    // Helper method to select all ancestor categories
    private func selectAllAncestors(of category: CategoryModel.Category) {
        var currentCategory = category
        var visited: Set<Int> = []
        var ancestors: [CategoryModel.Category] = []

        // First, collect all ancestors in order
        while let parentID = currentCategory.parentID,
              !visited.contains(parentID),
              let parent = categories.first(where: { $0.categoryID == parentID }) {
            visited.insert(parentID)
            ancestors.append(parent)
            currentCategory = parent
        }
        
        // Now set the filter values based on the level of ancestors
        if ancestors.count >= 2 {
            // We have at least both main and sub categories
            filter?.categoryID = ancestors.last?.categoryID
            filter?.subCategoryID = ancestors.first?.categoryID
        } else if ancestors.count == 1 {
            // We only have one parent
            filter?.categoryID = ancestors.first?.categoryID
        }
    }

    // Recursively find the top-level parent category ID for a given category
    private func getTopLevelParentID(of category: CategoryModel.Category) -> Int? {
        var currentCategory = category
        var visited: Set<Int> = []

        while let parentID = currentCategory.parentID,
              !visited.contains(parentID),
              let parent = categories.first(where: { $0.categoryID == parentID })
        {
            visited.insert(parentID)
            currentCategory = parent
        }

        return currentCategory.categoryID
    }
}
