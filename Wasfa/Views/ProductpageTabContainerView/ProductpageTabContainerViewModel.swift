import Foundation
import SwiftUI

enum CategoryLevel: Int {
    case main = 0
    case sub = 1
    case superSub = 2
}

class ProductpageTabContainerViewModel: SuperViewModel {
    @Published var nextScreen: String? = nil
    @Published var search: String = .init()
    @Published var tabOptions: [String] = ["Sort", "Filter"]
    @Published var selectedTabviewIndex: Int = 0
    
    @Published var productList: [ProductModel] = []
    @Published var productListingRequest: ProductListingRequest
    
    @Published var selectedCategoryID: Int?
    
    @Published var selectedSubCategoryID: Int?
    
    @Published var selectedSuperSubCategoryID: Int?
    
    @Published var selectedCategory: CategoryModel.Category?
    
    @Published var selectedSubCategory: CategoryModel.Category?
    
    @Published var selectedSuperSubCategory: CategoryModel.Category?
    
    @Published var categories: [CategoryModel.Category]
    
    @Published var subCategories: [CategoryModel.Category] = []
    
    @Published var superSubCategories: [CategoryModel.Category] = []
    
    @Published var categoryViewModel: CategoryViewModel?
    
    @Published var subCategoryViewModel: CategoryViewModel?
    
//    @Published var scrollViewID: UUID = .init()
    
//    @Published var headerScrollViewID: UUID = .init()
    
    @Published var isHeaderLoading: Bool = false
    
    @Published var isSubHeaderLoading: Bool = false
    
    @Published var offset: CGFloat = 0
    
    @Published var subOffset: CGFloat = 0
    
    @Published var superSubOffset: CGFloat = 0
    
    @Published var isFilterSheetVisible = false
    
    // variables for Pagination
    @Published var isLoading: Bool = false
    @Published var currentPage: Int = 1
    @Published var hasMorePages: Bool = true
   
    @Published var totalPages: Int = 0
    @Published var totalCount: Int = 0
    @Published var isInitialLoad: Bool = false
    
    private let pageSize = 20 // Adjust based on API
    
    init(productListingRequest: ProductListingRequest, categories: [CategoryModel.Category], subCategories: [CategoryModel.Category], superSubCategories: [CategoryModel.Category]) {
        self.productListingRequest = productListingRequest
        self.categories = categories
        self.subCategories = subCategories
        self.superSubCategories = superSubCategories
        super.init()
        initialAssign(model: productListingRequest)
        loadProductList(model: productListingRequest)
        bindSearchText()
    }
    
    func updateFilterSheetVisible(_ value: Bool) {
        isFilterSheetVisible = value
    }
    
    func onApplyFilter(_ value: ProductListingRequest?) {
        updateFilterSheetVisible(false)
        
        if let productListingRequest = value {
            if productListingRequest.categoryID == nil { resetCategory() }
            resetPagination()
            
            self.productListingRequest = productListingRequest
            initialAssign(model: productListingRequest, isFromFilter: true)
            loadProductList(model: productListingRequest)
        }
    }
    
    func bindSearchText() {
        // Debounce the search input by 0.5 seconds
        $search
            .debounce(for: .milliseconds(500), scheduler: RunLoop.main)
            .removeDuplicates() // Ignore duplicate searches
            .dropFirst() //
            .sink { [weak self] searchTerm in
                self?.productListingRequest.name = searchTerm
               
                if let productListingRequest = self?.productListingRequest {
                    self?.resetPagination()
                    self?.loadProductList(model: productListingRequest, isSearch: true)
                }
            }
            .store(in: &cancellables)
    }
    
    private func assignSelectedCategory(from id: Int?) {
        let category = categories.first(where: { $0.categoryID == id })
        Utilities.enQueue(after: .now() + 0.5) {
            withAnimation(.bouncy) {
                self.selectedCategoryID = category?.categoryID
                self.selectedCategory = category
            }
        }
    }
    
    func initialAssign(model: ProductListingRequest, isFromFilter: Bool = false) {
        if categories.isEmpty && !isFromFilter {
            getCategories {
                if let categoryID = model.categoryID {
                    self.assignSelectedCategory(from: categoryID)
                }
            }
        } else {
            if let categoryID = model.categoryID {
                let categories = self.categories
                
                let category = categories.first(where: { $0.categoryID == categoryID })
                
                if let category = category { subCategories = category.children ?? [] }
                
                Utilities.enQueue(after: .now() + 0.5) {
                    withAnimation(.bouncy) {
                        self.selectedCategoryID = category?.categoryID
                        self.selectedCategory = category
                    }
                }
            }
            
            if let subCategoryID = model.subCategoryID {
                let subCategories = self.subCategories
                
                let category = subCategories.first(where: { $0.categoryID == subCategoryID })
                
                if let category = category { superSubCategories = category.children ?? [] }
                
                Utilities.enQueue(after: .now() + 0.6) {
                    withAnimation(.bouncy) {
                        self.selectedSubCategoryID = category?.categoryID
                        self.selectedSubCategory = category
                    }
                }
            }
            
            if let superSubCategoryID = model.superSubCategoryID {
                let superSubCategories = self.superSubCategories
                
                let category = superSubCategories.first(where: { $0.categoryID == superSubCategoryID })
                Utilities.enQueue(after: .now() + 0.9) {
                    withAnimation(.bouncy) {
                        self.selectedSuperSubCategoryID = category?.categoryID
                        self.selectedSuperSubCategory = category
                    }
                }
            }
        }
    }
    
    func loadMoreHeader(model: CategoryModel.Category) {
        if let categoryViewModel = categoryViewModel {
            if categoryViewModel.categoryModelList.last?.id == model.id && !categoryViewModel.isLoading && categoryViewModel.hasMorePages {
                getCategories()
            }
        }
    }
    
    func loadMoreSubHeader(model: CategoryModel.Category) {
        if let subCategoryViewModel = subCategoryViewModel {
            if subCategoryViewModel.categoryModelList.last?.id == model.id && !subCategoryViewModel.isLoading && subCategoryViewModel.hasMorePages {
                getSubCategories()
            }
        }
    }
    
    func getCategories(completion: (() -> Void)? = nil) {
        if categoryViewModel == nil { categoryViewModel = .init() }
        isHeaderLoading = true
        categoryViewModel?.getCategories { data in
            self.categories.append(contentsOf: data)
            completion?()
            self.isHeaderLoading = false
        }
    }
    
    func getSubCategories(completion: (() -> Void)? = nil) {
        if subCategoryViewModel == nil { subCategoryViewModel = .init() }
        isSubHeaderLoading = true
        subCategoryViewModel?.getCategories { data in
            self.subCategories.append(contentsOf: data)
            
            completion?()
            self.isSubHeaderLoading = false
        }
    }
    
    func resetPaginationState(clearData: Bool = true) {
        if clearData {
            productList.removeAll()
        }
        isInitialLoad = false
        isLoading = false
        currentPage = 1
        hasMorePages = true
        totalPages = 0
        totalCount = 0
    }
    
    func resetPagination() {
        resetPaginationState()
    }
    
    func resetScrollView() {
//        scrollViewID = .init()
    }
    
    func resetCategory() {
        selectedCategory = nil
        selectedCategoryID = nil
        selectedSubCategory = nil
        selectedSuperSubCategory = nil
//        subCategories.removeAll()
//        superSubCategories.removeAll()
        resetSubCategory()
    }
    
    func resetSubCategory() {
        subCategoryViewModel = nil
//        subCategories.removeAll()
        selectedSubCategory = nil
    }
    
    func handleCategorySelection(level: CategoryLevel, value: CategoryModel.Category?) {
        var request = productListingRequest

        switch level {
        case .main:
            selectedCategory = (selectedCategory == value) ? nil : value
            selectedSubCategory = nil
            selectedSuperSubCategory = nil

            subCategories = selectedCategory?.children ?? []
//            superSubCategories = []

            request.categoryID = selectedCategory?.categoryID
            request.categoryID = selectedCategory?.categoryID
            request.subCategoryID = nil

        case .sub:
            if selectedSubCategory == value {
                selectedSubCategory = nil
                selectedSuperSubCategory = nil
                request.superSubCategoryID = nil
                request.categoryID = selectedCategory?.categoryID
                request.subCategoryID = nil
            } else {
                selectedSubCategory = value
                selectedSuperSubCategory = nil
                request.subCategoryID = value?.categoryID
                request.categoryID = value?.categoryID
                request.subCategoryID = value?.categoryID
            }

            superSubCategories = selectedSubCategory?.children ?? []

        case .superSub:
            if selectedSuperSubCategory == value {
                selectedSuperSubCategory = nil
                request.superSubCategoryID = nil
                request.categoryID = selectedSubCategory?.categoryID
                request.subCategoryID = selectedSubCategory?.categoryID
            } else {
                selectedSuperSubCategory = value
                request.superSubCategoryID = value?.categoryID
                request.categoryID = value?.categoryID
                request.subCategoryID = value?.categoryID
            }
        }

        resetScrollView()
        resetPagination()
        updateScrollPosition(id: value?.categoryID)

        productListingRequest = request
        
        DispatchQueue.debounce(delay: 0.6, key: "category_selections") {
            self.loadProductList(model: request)
        }
    }
    
    func loadMore(model: ProductModel) {
        if productList.last == model && !isLoading && hasMorePages {
            loadProductList(model: productListingRequest)
        }
    }
    
    func updateScrollPosition(id: Int?) {
        Utilities.enQueue(after: .now() + 0.5) {
            withAnimation(.bouncy) {
                self.selectedCategoryID = id
            }
        }
    }
    
    func loadProductList(model: ProductListingRequest, isSearch: Bool = false) {
        guard canFetchNextPage(isSearch: isSearch) else { return }
            
        isLoading = true
        var paginatedRequest: ProductListingRequest = model
        
        paginatedRequest.categoryID = paginatedRequest.superSubCategoryID ?? paginatedRequest.subCategoryID ?? paginatedRequest.categoryID
        
        paginatedRequest.pageNo = currentPage
        paginatedRequest.perPage = pageSize
        
        onApiCall(api.productsList, parameters: paginatedRequest.toDictionary, dismissKeyboard: !isSearch, withLoadingIndicator: false) { response in
            self.isInitialLoad = true
            self.isLoading = false
                
            if let data = response.data {
                if let products = data.products {
                    self.productList.append(contentsOf: products)
                }
                
                self.totalPages = data.totalPageCount
                self.totalCount = data.totalCount
                self.hasMorePages = self.currentPage < self.totalPages
                if self.hasMorePages { self.currentPage += 1 }
            } else {
                self.hasMorePages = false
            }
            
        } onFailure: { _ in
            self.hasMorePages = false
        }
    }

    private func canFetchNextPage(isSearch: Bool = false) -> Bool {
        return !isLoading && hasMorePages || isSearch
    }
    
    func onSelectProduct(product: ProductModel) {
        routerManager?.push(to: .productDetails(id: product.id), appState: appState)
    }
    
    func onAddToCart(product: Binding<ProductModel>, quantity: Int, onSuccess: @escaping (Bool) -> Void) {
        addToCart(productID: product.wrappedValue.id) { model in
            self.appState?.updateCartQuantity(model?.cartCount)
            onSuccess(model != nil)
        }
    }
    
    func onAddToWishlist(product: ProductModel, isAdd: Bool) {
        addToWishlist(productID: product.id, isAdd: isAdd) { [weak self] model in
            self?.appState?.updateWishlistQuantity(model?.wishListCount)
        }
    }
    
    func addToWishlist(productID: Int, isAdd: Bool, onComplete: @escaping (WishlistAddModel?) -> Void) {
        let parameters: [String: Any] = ["productId": productID]
        
        if isAdd {
            onApiCall(api.addWishlist, parameters: parameters) {
                if $0.success {
                    onComplete($0.data)
                }
            }
        } else {
            onApiCall(api.removeWishlist, parameters: parameters) {
                if $0.success {
                    onComplete($0.data)
                }
            }
        }
    }
    
    func addToCart(productID: Int, onComplete: @escaping (CartAddModel?) -> Void) {
        let parameters: [String: Any] = ["productId": productID, "quantity": 1]
        onApiCall(api.addToCart, parameters: parameters) {
            onComplete($0.data)
        }
    }
}
