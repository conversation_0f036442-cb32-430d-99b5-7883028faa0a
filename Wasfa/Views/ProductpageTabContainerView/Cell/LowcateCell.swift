import SwiftUI

struct LowcateCell: View {
    var body: some View {
        Text(StringConstants.kLblAll)
            .frame(width: getRelativeWidth(41.0), alignment: .center)
            .font(FontScheme.kNunitoLight(size: getRelativeHeight(13.0)))
            .fontWeight(.light)
            .padding(.horizontal, getRelativeWidth(13.0))
            .foregroundColor(ColorConstants.WhiteA700)
            .minimumScaleFactor(0.5)
            .multilineTextAlignment(.center)
            .background(ColorConstants.Blue600)
    }
}

/* struct LowcateCell_Previews: PreviewProvider {

 static var previews: some View {
 			LowcateCell()
 }
 } */
