import SwiftUI

struct ProductListingTabContainerView: View {
    @StateObject private var viewModel: ProductpageTabContainerViewModel
    @Environment(\.presentationMode) private var presentationMode: Binding<PresentationMode>
    @FocusState private var isFocused: Bool

    private let isSearchFocused: Bool

    init(productListingRequest: ProductListingRequest, isSearchFocused: Bool, categories: [CategoryModel.Category], subCategories: [CategoryModel.Category], superSubCategories: [CategoryModel.Category]) {
        self._viewModel = StateObject(wrappedValue: ProductpageTabContainerViewModel(productListingRequest: productListingRequest, categories: categories, subCategories: subCategories, superSubCategories: superSubCategories))
        self.isSearchFocused = isSearchFocused
    }

    var body: some View {
        Group{
            SuperView(pageState: $viewModel.pageState) {
                VStack(spacing: 0) {
                    searchBar
    //                categoryHeaderSection
                    productListSection
                }
               
    //            .fullScreenCover(isPresented:$viewModel.isFilterSheetVisible) {
    //                FilterSortView(filter: viewModel.productListingRequest, categories: viewModel.categories, onApply: viewModel.onApplyFilter)
    //            }
            }
            
            
        }
        .injectEnvironmentValues(viewModel)
        .sheet(isPresented: $viewModel.isFilterSheetVisible) {
            FilterSortView(filter: viewModel.productListingRequest, categories: viewModel.categories, onApply: viewModel.onApplyFilter)
        }
    }
       

    private var searchBar: some View {
        VStack(spacing: 12.0.relativeHeight) {
            HStack(spacing: 12) {
                Image("img_search_light")
                    .frame(width: 23, height: 24)

                TextField(StringConstants.kMsgWhatAreYouLooking, text: $viewModel.search)
                    .focused($isFocused)
                    .font(FontScheme.kNunitoLight(size: getRelativeHeight(12.0)))
                    .foregroundColor(ColorConstants.Gray50004)
                    .onLoad { self.isFocused = isSearchFocused }
            }
            .padding(.horizontal)
            .frame(width: getRelativeWidth(315.0), height: getRelativeHeight(50.0), alignment: .center)
            .overlay(Capsule().stroke(ColorConstants.Blue6009e, lineWidth: 1))
            .background(Capsule().fill(ColorConstants.Cyan4000a))
            .padding(.horizontal, getRelativeWidth(38.0))
            
            
            categoryHeaderSection

            HStack(spacing: 0) {
                sortButton
                Divider()
                    .frame(maxWidth: 1, maxHeight: .infinity)
                    .background(Color(red: 0.11, green: 0.62, blue: 0.85))
                filterButton
            }
            .font(Font.custom("Nunito", size: 14).weight(.light))
            .kerning(0.5)
            .foregroundColor(Color(red: 0.11, green: 0.62, blue: 0.85))
            .frame(maxWidth: .infinity)
            .overlay(RoundedRectangle(cornerRadius: 0).stroke(Color(red: 0.11, green: 0.62, blue: 0.85), lineWidth: 1))
            .frame(height: 45.relativeHeight)
            .padding(.horizontal, -2)
        }
        .padding(.top, 6)
        .background(.white)
        .animation(.bouncy, value: viewModel.categories)
        .animation(.bouncy, value: viewModel.subCategories)
        .animation(.bouncy, value: viewModel.superSubCategories)
    }

    private var sortButton: some View {
        Button(action: {
            viewModel.updateFilterSheetVisible(true)
        }) {
            HStack(spacing: 10) {
                Image(.productSort)
                    .resizable()
                    .frame(width: 24, height: 24)
                Text("Sort")
            }
            .foregroundColor(Color(red: 0.11, green: 0.62, blue: 0.85))
            .padding(.vertical, 8)
        }
        .frame(maxWidth: .infinity)
    }

    private var filterButton: some View {
        Button(action: {
            viewModel.updateFilterSheetVisible(true)
        }) {
            HStack(spacing: 10) {
                Image(.productFilter)
                    .resizable()
                    .frame(width: 24, height: 24)
                Text("Filter")
            }
            .foregroundColor(Color(red: 0.11, green: 0.62, blue: 0.85))
            .padding(.vertical, 8)
        }
        .frame(maxWidth: .infinity)
    }

    private var categoryHeaderSection: some View {
        VStack(spacing: 8.relativeHeight) {
            HorizontalCategoryHeaderView(viewModel: viewModel)
        }
        .fixedSize(horizontal: false, vertical: true)
    }

    private var productListSection: some View {
        MainScrollBody(backButtonWithTitle: viewModel.productListingRequest.categoryID != nil ? viewModel.selectedCategory?.name.localize : viewModel.productListingRequest.title.localize) {
            LazyVStack(pinnedViews: .sectionHeaders) {
                if viewModel.productList.isEmpty && !viewModel.isInitialLoad {
                    ProgressView()
                        .frame(height: 200)
                } else {
                    ProductListingView(viewModel: viewModel)
                        .padding(.vertical)
                }
            }
            .background(ColorConstants.WhiteA700)
        }
        .scrollDismissesKeyboard(.immediately)
        // .id(viewModel.scrollViewID)
    }
}

struct HorizontalCategoryHeaderView: View {
    @StateObject var viewModel: ProductpageTabContainerViewModel

    var body: some View {
        VStack(spacing: 8.relativeHeight) {
            
                categoryHeader(
                    categories: viewModel.categories,
                    selected: viewModel.selectedCategory,
                    isLoading: viewModel.isHeaderLoading,
                    level: .main,
                    coordinateSpace: "scroll.main.category",
                    offsetBinding: $viewModel.offset,
                    selectedID: $viewModel.selectedCategoryID,
                    onLoadMore: viewModel.loadMoreHeader
                )

               
            

            if viewModel.selectedCategory != nil {
                categoryHeader(
                    categories: viewModel.subCategories,
                    selected: viewModel.selectedSubCategory,
                    isLoading: viewModel.isSubHeaderLoading,
                    level: .sub,
                    coordinateSpace: "scroll.sub.category",
                    offsetBinding: $viewModel.subOffset,
                    selectedID: $viewModel.selectedSubCategoryID,
                    onLoadMore: viewModel.loadMoreSubHeader
                )
            }

            if viewModel.selectedSubCategory != nil {
                categoryHeader(
                    categories: viewModel.superSubCategories,
                    selected: viewModel.selectedSuperSubCategory,
                    isLoading: viewModel.isSubHeaderLoading,
                    level: .superSub,
                    coordinateSpace: "scroll.super.sub.category",
                    offsetBinding: $viewModel.superSubOffset,
                    selectedID: $viewModel.selectedSuperSubCategoryID,
                    onLoadMore: viewModel.loadMoreSubHeader
                )
            }
        }
    }

    private func categoryHeader(
        categories: [CategoryModel.Category],
        selected: CategoryModel.Category?,
        isLoading: Bool,
        level: CategoryLevel,
        coordinateSpace: String,
        offsetBinding: Binding<CGFloat>,
        selectedID: Binding<Int?>,
        onLoadMore: @escaping (CategoryModel.Category) -> Void
    ) -> some View {
        ScrollViewReader { _ in
            VStack(spacing: -8) {
                ScrollView(.horizontal) {
                    LazyHStack( alignment: level == .main ? .top : .center,spacing: 9.relativeWidth) {
                        ForEach(categories) { category in
                            let isSelected = selected == category
                            Button {
                                viewModel.handleCategorySelection(level: level, value: category)
                            } label: {
                                if level == .main {
                                    VStack(spacing: -2) {
                                        Text(category.name)
                                            .font(.custom("Nunito-Bold", size: 16))
                                            .foregroundColor(isSelected ? Color(red: 0.11, green: 0.62, blue: 0.85) : Color.gray.opacity(0.5))
                                            .multilineTextAlignment(.center)
                                            .frame(height: getRelativeHeight(34.0))

                                        if isSelected {
                                            Rectangle()
                                                .frame(height: 2)
                                                .foregroundColor(Color(red: 0.11, green: 0.62, blue: 0.85))
                                                .frame(maxWidth: .infinity)
                                        }
                                    }
                                    .padding(.horizontal, getRelativeWidth(12.0))
                                    .padding(.vertical, getRelativeHeight(7.0))
                                } else {
                                    Text(category.name)
                                        .font(FontScheme.kNunitoLight(size: getRelativeHeight(13.0)))
                                        .foregroundColor(isSelected ? .white : Color(red: 0.11, green: 0.62, blue: 0.85))
                                        .padding(.horizontal, getRelativeWidth(20.0))
                                        .padding(.vertical, getRelativeHeight(10.0))
                                        .background(isSelected ? Color(red: 0.11, green: 0.62, blue: 0.85) : Color.clear)
                                        .overlay(
                                            Capsule()
                                                .stroke(Color(red: 0.11, green: 0.62, blue: 0.85), lineWidth: 1)
                                        )
                                        .clipShape(Capsule())
                                }
                            }
                            .id(category.categoryID)
                            .onAppear {onLoadMore(category)}
                        }

                        if isLoading {
                            ProgressView()
                                .padding(.bottom, 1)
                        }
                    }
                    .padding(.top, 6)
                    .background(
                        GeometryReader { geo -> Color in
                            let offset = geo.frame(in: .named(coordinateSpace)).minY
                            DispatchQueue.main.async {
                                offsetBinding.wrappedValue = offset
                            }
                            return Color.clear
                        }
                    )
                }
                .scrollIndicators(.hidden)
                .animation(.bouncy, value: selected?.categoryID)
                .scrollPosition(id: selectedID, anchor: .center)
                .safeAreaPadding(.horizontal)
                .coordinateSpace(name: coordinateSpace)

                if level == .main && !isLoading {
                    Divider()
                        .frame(height: 1)
                        .background(Color.gray.opacity(0.1))
                        
                }
            }
        }
    }
}

//#Preview {
//    NavigationStack {
//        ProductListingTabContainerView(productListingRequest: .init(title: "Device"), isSearchFocused: false, categories: [])
//    }
//}
