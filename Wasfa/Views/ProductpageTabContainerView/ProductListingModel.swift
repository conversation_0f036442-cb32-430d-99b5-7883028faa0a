//
//  ProductListingModel.swift
//  Wasfa
//
//  Created by Apple on 24/11/2024.
//

import Foundation

// MARK: - ProductListingModel

struct ProductListingModel: Codable {
    let totalCount, totalPageCount: Int
    let category: String
    let products: [ProductModel]?
}

struct ProductListingRequest: Encodable, Equatable, Hashable {
    var categoryID, subCategoryID, superSubCategoryID, brand: Int?
    var min,
        max,
        name: String?
    var viewAll: ViewAllType?
    var sortKey: SortKey?
    var pageNo: Int = 1
    var perPage: Int = 20
    let title: String
    
    
    enum CodingKeys: String, CodingKey {
        case categoryID = "category", subCategoryID, superSubCategoryID, brand, min, max, name, viewAll, sortKey, pageNo, perPage, title
    }
}

enum SortKey: String, Encodable, CaseIterable, Identifiable {
    case popularity, priceHighToLow, priceLowToHigh, latest, topRated, byOffer
    
    var title: String {
        switch self {
        case .popularity: return "Popularity"
        case .priceHighToLow: return "Price - high to low"
        case .priceLowToHigh: return "Price - low to high"
        case .latest: return "Latest"
        case .topRated: return "Top - Rated"
        case .byOffer: return "By Offer"
        }
    }
    
    var id: Self { self }
}

enum ViewAllType: String, Encodable {
    case latest, bestSellers, offers

    var title: String {
        switch self {
        case .latest: return "New Arrival"
        case .bestSellers: return "Best Sellers"
        case .offers: return "Special Offers"
        }
    }
}
