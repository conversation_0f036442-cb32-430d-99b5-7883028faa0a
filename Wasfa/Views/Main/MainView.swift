//
//  MainView.swift
//  Wasfa
//
//  Created by Apple on 22/09/2024.
//

import SwiftUI

struct MainView: View {
    @StateObject private var viewModel: MainViewModel = .init()
    @EnvironmentObject private var appState: AppState
    var body: some View {
        Group {
            switch viewModel.appState?.initialScreen {
            case .splash:
                SplashScreenView()
                    .transition(.backslide)
            case .dashboard:
                DashboardView()
                    .transition(.backslide)
            case .authentication:
                Text("Authentication Section")
                    .transition(.backslide)
            case .none:
                EmptyView()
            }
        }
        .toastView(toast: $appState.toast)
        .animation(.easeInOut, value: viewModel.appState?.initialScreen)
        .injectEnvironmentValues(viewModel)
        
    }
}

#Preview {
    NavigationStack {
        MainView().attachAllEnvironmentObjects()
    }
}

extension AnyTransition {
    static var backslide: AnyTransition {
        AnyTransition.asymmetric(
            insertion: .move(edge: .trailing),
            removal: .move(edge: .leading))
    }
}
