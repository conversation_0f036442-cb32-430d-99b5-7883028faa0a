//
//  MainViewModel.swift
//  Wasfa
//
//  Created by Apple on 27/10/2024.
//

import Foundation

class MainViewModel: SuperViewModel {
    override init() {
        super.init()
        onLoad()
    }

    func createGenerateToken(onComplete: @escaping () -> Void) {
        guard UserDefaultsSecure.sharedInstance.getGeneratedToken() == nil else { onComplete(); return }
//        let parameters: [String: Any] = GenerateTokenRequest(deviceID: !AppState.fcmToken.isEmpty ?  AppState.fcmToken : AppState.deviceID).toDictionary
        
        let parameters: [String: Any] = GenerateTokenRequest(deviceID: AppState.fcmToken).toDictionary
        onApiCall(api.generateToken, parameters: parameters, onSuccess: {
            if $0.success {
                UserDefaultsSecure.sharedInstance.setGeneratedTokenStringValue(value: $0.data?.token)
                onComplete()
            }

        })
    }
    
    func refreshUserToken() {}
    
    func onLoad() {
        Utilities.enQueue(after: .now() + 1) { [weak self] in
            let startUpLanguageShown = self?.appState?.startUpLanguageShown ?? false
            if startUpLanguageShown {
                Utilities.enQueue(after: .now() + 4) {
                    self?.createGenerateToken {
                        self?.appState?.initialScreen = .dashboard
                    }
                }
            }
        }
    }
}
