import Foundation
import SwiftUI

class MenuViewModel: SuperViewModel {
    @Published var nextScreen: String? = nil
    @Published var showLogoutAlert = false
    @Published var showLanguagePopup: Bool = false
    func hideMenu() {
        appState?.showSideMenu = false
    }

    func updateLanguagePopup() {
        self.showLanguagePopup.toggle()
    }
    
    // Contact Us
    func handleContactUs() {
        // TODO: Implement contact us functionality
        self.hideMenu()
        self.routerManager?.push(to: .contactUs, appState: appState)
    }
    
    // About Us
    func handleAboutUs() {
        // TODO: Implement about us functionality
        self.hideMenu()
        
        self.routerManager?.push(to: .aboutUs, appState: appState)
    }
    
    func handleLanguage() {
        self.updateLanguagePopup()
    }
    
    // FAQs
    func handleFAQs() {
        // TODO: Implement FAQs functionality
        self.hideMenu()
        
        self.routerManager?.push(to: .faq, appState: appState)
    }
    
    // Terms and Conditions
    func handleTerms() {
        // TODO: Implement terms and conditions functionality
        self.hideMenu()
        
        self.routerManager?.push(to: .termsAndConditions, appState: appState)
    }
    
    // Privacy Policy
    func handlePrivacyPolicy() {
        // TODO: Implement privacy policy functionality
        self.hideMenu()
        
        self.routerManager?.push(to: .privacyPolicy, appState: appState)
    }
    
    func onLogout() {
        if AppState.isLoggedIn {
            self.showLogoutAlert = true
        } else {
            self.hideMenu()
            self.routerManager?.push(to: .signIn, appState: appState)
        }
    }
    
    // Logout
    func handleLogout() {
        self.hideMenu()
        
        self.appState?.onLogout()
        
        // TODO: Implement logout functionality
        // Clear user session
        // Reset app state
        // Navigate to login screen
    }
}
