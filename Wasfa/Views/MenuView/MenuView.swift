import SwiftUI

private enum Constants {
    static let iconSize: CGFloat = 24.0
    static let menuItemHeight: CGFloat = 54.0
    static let headerFontSize: CGFloat = 18.0
    static let itemFontSize: CGFloat = 13.0.relativeFontSize
    static let headerVerticalPadding: CGFloat = 13.0
    static let closeButtonSize: CGFloat = 48.0
    static let topPadding: CGFloat = 30.0
    static let bottomPadding: CGFloat = 10.0
}

// Menu Item Model
struct MenuItem: Identifiable {
    let id = UUID()
    let icon: ImageResource
    let title: LocalizedStringKey
    let action: () -> Void
}

struct MenuItemView: View {
    let item: MenuItem
    
    var body: some View {
        VStack {
            Button(action: item.action) {
                HStack {
                    Image(item.icon)
                        .renderingMode(.template)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .foregroundStyle(ColorConstants.Blue300)
                        .frame(width: getRelativeWidth(Constants.iconSize),
                               height: getRelativeWidth(Constants.iconSize))
                        .accessibilityLabel(item.title)
                    
                    Text(item.title)
                        .font(FontScheme.kPoppins(size: Constants.itemFontSize))
                       
                        .fontWeight(.medium)
                        .foregroundColor(ColorConstants.Black90001)
                        .frame(height: getRelativeHeight(18.0), alignment: .leading)
                        .padding(.leading, getRelativeWidth(13.0))
                    
                    Spacer()
                    
                    Image("img_expand_right_light")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .padding(6)
                        .rotateBasedOnLanguage()
                        .frame(width: getRelativeWidth(Constants.iconSize),
                               height: getRelativeWidth(Constants.iconSize))
                        .accessibilityHidden(true)
                }
            }
        }
        .frame(maxWidth: .infinity)
        .frame(height: getRelativeHeight(Constants.menuItemHeight))
        .overlay(Divider().background(ColorConstants.Black9003f), alignment: .bottom)
        .background(RoundedCorners().fill(Color.clear.opacity(0.7)))
        .contentShape(Rectangle())
        .accessibilityElement(children: .combine)
    }
}

struct MenuView: View {
    @StateObject private var menuViewModel = MenuViewModel()
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject private var appState: AppState
    
    @State private var isDelayed = false
    
    private var menuItems: [MenuItem] {
        [
            MenuItem(icon: .menuContactUs, title: StringConstants.kLblContactUs, action: menuViewModel.handleContactUs),
            
            MenuItem(icon: .menuAboutUs, title: StringConstants.kLblAboutUs, action: menuViewModel.handleAboutUs),
            
            MenuItem(icon: .globe, title: "Language", action: menuViewModel.handleLanguage),
 
            MenuItem(icon: .menuFaq, title: StringConstants.kLblFaqS, action: menuViewModel.handleFAQs),
            
            MenuItem(icon: .menuTermsAndConditions, title: StringConstants.kMsgTermsAndConditions, action: menuViewModel.handleTerms),
     
            MenuItem(icon: .menuPrivacyPolicy, title: StringConstants.kLblPrivacyPolicy, action: menuViewModel.handlePrivacyPolicy),
            
            MenuItem(icon: .menuLogOut, title: AppState.isLoggedIn ? StringConstants.kLblLogOut  : StringConstants.kLblLogIn, action: menuViewModel.onLogout),
        ]
    }
    
    var body: some View {
        ScrollView(.vertical, showsIndicators: false) {
            VStack(spacing: 0) {
                header
                    .padding(.horizontal)
                    .padding(.vertical, getRelativeHeight(Constants.headerVerticalPadding))
                
                VStack(spacing: 0) {
                    ForEach(menuItems) { item in
                        MenuItemView(item: item)
                    }
                }
                .padding(.horizontal)
                
                Spacer(minLength: 20)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(ColorConstants.WhiteA700)
        .padding(.bottom, getRelativeHeight(Constants.bottomPadding))
        .injectEnvironmentValues(menuViewModel)
        .alert("Logout", isPresented: $menuViewModel.showLogoutAlert) {
            Button("Cancel", role: .cancel) {}
            Button("Logout", role: .destructive, action: menuViewModel.handleLogout)
        } message: {
            Text("Are you sure you want to logout?")
        }
        .overlay(alignment: .bottom) {
            Group {
                if menuViewModel.showLanguagePopup {
                    SlideUpAnimationContainerView {} content: {
                        LanguageView(onSubmit: { value in
                            
                            menuViewModel.updateLanguagePopup()
                            
                            if let value = value {
//                                appState.updateSelectedTab(.home)
//                                routerManager.popToAllRoot()
                                appState.updateAppLocale(to: value, reset: true)
//                                menuViewModel.updateLanguage(value.rawValue)
                            } else {
                                
                            }
                        })
                        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .bottom)
                        .transition(.move(edge: .bottom))
                        .ignoresSafeArea(.container, edges: .bottom)
                        //                        .padding(AppConstants.tabBarHeight)
                    }
                    .transition(.move(edge: .bottom).combined(with: .opacity))
                }
            }
            .animation(.easeOut, value: menuViewModel.showLanguagePopup)
        }
    }
    
    private var header: some View {
        ZStack(alignment: .center) {
            Text(StringConstants.kLblMenu)
                .font(FontScheme.kNunitoBold(size: getRelativeHeight(Constants.headerFontSize)))
                .fontWeight(.bold)
                .foregroundColor(ColorConstants.Black90001)
                .accessibilityAddTraits(.isHeader)
            
            Spacer()
            
            Button(action: {
                if !isDelayed {
                    appState.showSideMenu.toggle() // Toggle the Boolean
                    triggerDelay()
                }
                
            }) {
                Image(.menuClose)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: getRelativeWidth(Constants.closeButtonSize),
                           height: getRelativeWidth(Constants.closeButtonSize))
            }
            .frame(maxWidth: .infinity, alignment: .trailing)
            .accessibilityLabel("Close Menu")
        }
    }
    
    // Function to introduce the delay before the next toggle
    private func triggerDelay() {
        isDelayed = true
        Utilities.enQueue(after: .now() + 1.0) { isDelayed = false }
    }
}

#if DEBUG
struct MenuView_Previews: PreviewProvider {
    static var previews: some View {
        MenuView()
            .environmentObject(AppState())
    }
}
#endif
