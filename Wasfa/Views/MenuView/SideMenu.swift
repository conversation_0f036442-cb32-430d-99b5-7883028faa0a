//
//  SideMenu.swift
//  Wasfa
//
//  Created by Apple on 09/02/2025.
//

import SwiftUI

struct SideMenu: View {
    @EnvironmentObject private var appState: AppState
    @State private var isDelayed = false
    var content: AnyView
    var edgeTransition: AnyTransition = .move(edge: .leading)
    var body: some View {
        GeometryReader { proxy in
            let size = proxy.size
            ZStack(alignment: .leading) {
                if appState.showSideMenu {
                    Color.black
                        .opacity(0.3)
                        .ignoresSafeArea()
                        .onTapGesture {
                            if !isDelayed {
                                appState.showSideMenu.toggle() // Toggle the Boolean
                                triggerDelay()
                            }
                        }

                    content
                        .transition(edgeTransition)
                        .background(Color.clear)
                        .clipShape(UnevenRoundedRectangle(cornerRadii: RectangleCornerRadii(topLeading: 0, bottomLeading: 0, bottomTrailing: 15.relativeFontSize, topTrailing: 15.0.relativeFontSize)))
                        .ignoresSafeArea(.container, edges: .bottom)
//                        .frame(maxWidth: size.width * 0.80)
                        .background(ColorConstants.WhiteA700)
                }
            }
            .frame(maxWidth: .infinity, maxHeight: size.height, alignment: .leading)
            .animation(.bouncy, value: appState.showSideMenu)
        }
    }

    // Function to introduce the delay before the next toggle
    private func triggerDelay() {
        isDelayed = true
        Utilities.enQueue(after: .now() + 1.0) {isDelayed = false}
    }
}

#Preview {
    SideMenu(content: AnyView(EmptyView()))
        .attachAllEnvironmentObjects()
}
