import SwiftUI

struct DeviceListCell: View {
    let model: CategoryModel.Category
    let isLast: Bool
    let categoryList: [CategoryModel.Category]
    let scrollViewProxy: ScrollViewProxy
    @State var expanded: Bool = false
    
    @Environment(\.routerManager) var routerManager: RouterManager
    @EnvironmentObject private var appState: AppState
    
    var body: some View {
        VStack(spacing: 26.0.relativeHeight) {
            ZStack(alignment: .center) {
                NetworkImageView(path: model.banner, contentMode: .fit)
                    .frame(width: getRelativeWidth(356.0), height: getRelativeHeight(88.0),
                           alignment: .center)
                    
                    .background(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                               bottomRight: 13.0).fill(.white))
                    .clipped()
                HStack {
                    Text(model.name)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.WhiteA700)
                        .multilineTextAlignment(.leading)
                        .padding(.leading, 22.0.relativeWidth)
                        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .leading)
                        .onTapGesture {
                            routerManager.push(to: .productListing(reuestModel: .init(categoryID: model.categoryID, title: model.name), categories: categoryList, subCategories: model.children ?? []), appState: appState)
                        }
                    Button(action: {
                        withAnimation(.bouncy) {
                            expanded.toggle()
                            
                            if isLast, expanded {
                                scrollToBottom(scrollViewProxy)
                            }
                        }
                    }, label: {
                        VStack { Image(.categoryAddButton)
                            .frame(width: getRelativeWidth(22.0), height: getRelativeWidth(24.0),
                                   alignment: .center)
                            .rotationEffect(Angle(degrees: expanded ? 360 : 0))
                        }
                        .frame(maxWidth: 50, maxHeight: .infinity)
                       
                    })
                }
                .padding(.trailing, 22.0.relativeWidth)
                .frame(width: getRelativeWidth(356.0), height: getRelativeHeight(88.0),
                       alignment: .leading)
                .background(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                           bottomRight: 13.0)
                        .fill(ColorConstants.Black90042))
                .shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 1)
                .clipShape(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                          bottomRight: 13.0))
            }
            .frame(width: getRelativeWidth(356.0), alignment: .leading)
            .clipped()
            LazyVStack(spacing: 6.0.relativeHeight) {
                ForEach(model.children ?? []) {
                    ExpandedListView(selectedParentCategory: model, parentCategory: categoryList, subCategory: model.children ?? [], model: $0, isLast: isLast, scrollViewProxy: scrollViewProxy)
                }
            }
           
            .visibility(expanded ? .visible : .gone)
        }
        .padding(.bottom, expanded ? 16 : 0)
        .background(
            RoundedCorners(topLeft: 0, topRight: 0, bottomLeft: 24, bottomRight: 24)
                .fill(Color(red: 0.95, green: 0.95, blue: 0.95))
        )
    }
    
    // Function to scroll to the bottom
    private func scrollToBottom(_ scrollProxy: ScrollViewProxy? = nil) {
        // Scroll to the item with the "bottom" ID
        
        Utilities.enQueue(after: .now() + 0.1) {
            withAnimation(.bouncy) {
                scrollProxy?.scrollTo("bottom", anchor: .bottom)
            }
        }
    }
}

// struct DevicelistCell_Previews: PreviewProvider {
//    static var previews: some View {
//        DeviceListCell()
//    }
// }

struct ExpandedListView: View {
    let selectedParentCategory: CategoryModel.Category
    let parentCategory: [CategoryModel.Category]
    let subCategory: [CategoryModel.Category]
    let model: CategoryModel.Category
    let isLast: Bool
    let scrollViewProxy: ScrollViewProxy
    @Environment(\.routerManager) var routerManager: RouterManager
    @EnvironmentObject private var appState: AppState
    @State private var expanded: Bool = false
    
    var body: some View {
        VStack(spacing: 26.0.relativeHeight) {
            HStack {
                Text(model.name)
                    .font(Font.custom("Nunito", size: 14).weight(.bold))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .onTapGesture {
                        routerManager.push(to: .productListing(reuestModel: .init(categoryID: selectedParentCategory.categoryID, subCategoryID: model.categoryID, title: model.name), categories: parentCategory, subCategories: subCategory, superSubCategories: model.children ?? []), appState: appState)
                    }
                
                Button {
                    withAnimation(.bouncy) {
                        if let children = model.children, !children.isEmpty {
                            expanded.toggle()
                            
                            if isLast {
                                scrollToBottom(scrollViewProxy)
                            }
                        }
                    }
                } label: {
                    Image(.imgExpandRightLight)
                        .frame(width: 24, height: 24)
                        .rotationEffect(Angle(degrees: expanded ? 90 : 0))
                }
            }
            .padding(.horizontal)
            .frame(height: 47)
//            .background(Color(red: 0.94, green: 0.93, blue: 0.93))
            
            LazyVStack(spacing: 6.0.relativeHeight) {
                ForEach(model.children ?? []) { category in
                     
                    Button {
                        routerManager.push(to: .productListing(reuestModel: .init(categoryID: selectedParentCategory.categoryID, subCategoryID: model.categoryID, superSubCategoryID: category.categoryID, title: category.name), categories: parentCategory, subCategories: subCategory, superSubCategories: model.children ?? []), appState: appState)
                    } label: {
                        HStack {
                            Text(category.name)
                                .font(Font.custom("Nunito", size: 14).weight(.bold))
                                .foregroundColor(.black)
                                .frame(maxWidth: .infinity, alignment: .leading)
                        }
                        .padding(.horizontal)
                        .frame(height: 47)
                        .background(ColorConstants.WhiteA700)
                        .padding(.horizontal)
                    }
                }
            }
            .visibility(expanded ? .visible : .gone)
        }
    }
    
    // Function to scroll to the bottom
    private func scrollToBottom(_ scrollProxy: ScrollViewProxy? = nil) {
        // Scroll to the item with the "bottom" ID
        
        Utilities.enQueue(after: .now() + 1) {
            withAnimation {
                scrollProxy?.scrollTo("bottom", anchor: .bottom)
            }
        }
    }
}
