import SwiftUI

struct CategoryView: View {
    @StateObject var viewModel = CategoryViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    var body: some View {
        MainScrollBody(backButtonWithTitle: StringConstants.kLblCategories, hideBackButton: true) {
            ScrollViewReader { scrollProxy in

                SuperView(pageState: $viewModel.pageState) {
                    Section {
                        LazyVStack {
                            ForEach(viewModel.categoryModelList) { model in

                                DeviceListCell(model: model, isLast: viewModel.categoryModelList.last?.id == model.id, categoryList: viewModel.categoryModelList, scrollViewProxy: scrollProxy, expanded: model.isExpanded)
                                    .id(model.categoryID)
                                    .onAppear { self.viewModel.loadMore(model: model) }
                            }
                        }
                        // Add an empty view with a known ID at the end
                        .id("bottom")
                        .padding(.horizontal, getRelativeWidth(16.0))
                        .background(ColorConstants.WhiteA700)
                        .padding(.top, getRelativeHeight(30.0))
                        .padding(.bottom, getRelativeHeight(10.0))
                    } footer: {
                        // Loading indicator
                        if viewModel.isLoading {
                            ProgressView("Loading...")
                                .padding(.vertical, 10)
                        }

                        // No more items indicator
                        if !viewModel.hasMorePages && !viewModel.isLoading {
                            Text("\(viewModel.categoryModelList.count) of \(viewModel.totalCount) category loaded")
                                .font(.footnote)
                                .foregroundColor(.gray)
                                .padding(.vertical, 10)
                        }
                    }
                }
                .onLoad {
                    viewModel.scrollToID = scrollProxy.scrollTo
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .onDisappear(perform: viewModel.onDisappear)
                .onAppear(perform: viewModel.updateSelectedCategoryID)
            }
        }
        .onDisappear(perform: viewModel.onDisappear)
        .background(ColorConstants.WhiteA700)
        .injectEnvironmentValues(viewModel)
    }
}

#Preview {
    NavigationStack {
        CategoryView().attachAllEnvironmentObjects()
    }
}
