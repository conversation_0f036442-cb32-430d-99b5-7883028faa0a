import Foundation
import SwiftUI

@MainActor
class CategoryViewModel: SuperViewModel {
    // MARK: - Published Properties

    @Published private(set) var categoryModelList: [CategoryModel.Category] = []
    
    // MARK: - Pagination Properties

    @Published private(set) var isLoading: Bool = false
    @Published private(set) var totalPages: Int = 0
    @Published private(set) var totalCount: Int = 0
    @Published private(set) var hasMorePages: Bool = true
    
    private var currentPage: Int = 1
    private let pageSize: Int = 20
    private var isInitialLoad: Bool = false
    
    // MARK: - Callbacks

   
    
    // MARK: - Initialization

    override init() {
        super.init()
    }
    
    
    var scrollToID: ((Int?, UnitPoint?) -> Void)?
    func scrollToItem(_ id: Int?) {
        withAnimation(.bouncy) {
            scrollToID?(id, .top)
        }
    }
    
    func resetPageValues() {
        totalPages = 0
        totalCount = 0
        currentPage = 1
        hasMorePages = true
        categoryModelList.removeAll()
        isLoading = false
        isInitialLoad = false
    }
    
    func assignCategoryID() {
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            self.scrollToItem(self.appState?.selectedCategoryID)
        }
    }
    
    public func updateSelectedCategoryID() {
        resetPageValues()
        getCategories { _ in
            self.assignCategoryID()
        }
    }
    
    public func onDisappear() {
        appState?.updateSelectedCategoryID(nil)
    }
    
    func loadMore(model: CategoryModel.Category) {
        if categoryModelList.last?.id == model.id && !isLoading && hasMorePages {
            getCategories()
        }
    }
    
    func getCategories(completion: (([CategoryModel.Category]) -> Void)? = nil) {
        guard !isLoading && hasMorePages else { return }
        isLoading = true
        
        var parameters: [String: Any] = [:]
        parameters["pageNo"] = currentPage
        parameters["perPage"] = pageSize
        
        onApiCall(api.categoryList, parameters: parameters, withLoadingIndicator: !isInitialLoad) { response in
            
            self.isInitialLoad = true
            self.isLoading = false
            
            if let data = response.data {
                let list = data.categories
               
                self.categoryModelList.append(contentsOf: list)
                
                if let selectedID = self.appState?.selectedCategoryID,
                   let index = self.categoryModelList.firstIndex(where: { $0.categoryID == selectedID })
                {
                    self.categoryModelList[index].isExpanded = true
                }
                
                self.totalPages = data.totalPageCount
                self.totalCount = data.totalCount
                self.hasMorePages = self.currentPage < self.totalPages
                if self.hasMorePages { self.currentPage += 1 }
                
            } else {
                self.hasMorePages = false
            }
            
            completion?(response.data?.categories ?? [])
        }
        onFailure: { _ in
            self.hasMorePages = false
            completion?([])
        }
    }
}
