//import SwiftUI
//
//struct Productcard4Cell: View {
//    var body: some View {
//        VStack {
//            ZStack(alignment: .center) {
//                Image("img_613xc1bj1dl_1_103x111")
//                    .resizable()
//                    .frame(width: getRelativeWidth(109.0), height: getRelativeHeight(103.0),
//                           alignment: .leading)
//                    .scaledToFit()
//                    .cornerRadius(11.0)
//                    .padding(.leading, getRelativeWidth(15.0))
//                    .padding(.trailing, getRelativeWidth(25.0))
//                HStack {
//                    Button(action: {}, label: {
//                        Image("img_favorite_gray_500")
//                    })
//                    .frame(width: getRelativeWidth(22.0), height: getRelativeWidth(24.0),
//                           alignment: .center)
//                    .padding(.bottom, getRelativeHeight(28.0))
//                    VStack {
//                        VStack {
//                            Text(StringConstants.kLbl50Off)
//                                .font(FontScheme.kNunitoLight(size: getRelativeHeight(9.0)))
//                                .fontWeight(.light)
//                                .foregroundColor(ColorConstants.WhiteA700)
//                                .minimumScaleFactor(0.5)
//                                .multilineTextAlignment(.leading)
//                                .frame(width: getRelativeWidth(36.0),
//                                       height: getRelativeHeight(13.0), alignment: .leading)
//                        }
//                        .frame(width: getRelativeWidth(45.0), height: getRelativeHeight(13.0),
//                               alignment: .leading)
//                        .background(RoundedCorners(topLeft: 6.0, topRight: 6.0, bottomLeft: 6.0,
//                                                   bottomRight: 6.0)
//                                .fill(ColorConstants.Teal400))
//                        ZStack(alignment: .center) {
//                            ZStack {}
//                                .hideNavigationBar()
//                                .frame(width: getRelativeWidth(45.0),
//                                       height: getRelativeHeight(12.0), alignment: .leading)
//                                .background(RoundedCorners(topLeft: 6.0, topRight: 6.0,
//                                                           bottomLeft: 6.0, bottomRight: 6.0)
//                                        .fill(ColorConstants.Red600))
//                                .padding(.bottom, getRelativeHeight(18.98))
//                            Text(StringConstants.kLblSoldOut)
//                                .font(FontScheme.kNunitoLight(size: getRelativeHeight(9.0)))
//                                .fontWeight(.light)
//                                .foregroundColor(ColorConstants.WhiteA700)
//                                .minimumScaleFactor(0.5)
//                                .multilineTextAlignment(.center)
//                                .frame(width: getRelativeWidth(16.0),
//                                       height: getRelativeHeight(31.0), alignment: .center)
//                                .padding(.horizontal, getRelativeWidth(14.31))
//                        }
//                        .hideNavigationBar()
//                        .frame(width: getRelativeWidth(45.0), height: getRelativeHeight(31.0),
//                               alignment: .leading)
//                        .padding(.top, getRelativeHeight(4.0))
//                    }
//                    .frame(width: getRelativeWidth(45.0), height: getRelativeHeight(49.0),
//                           alignment: .leading)
//                    .padding(.leading, getRelativeWidth(67.0))
//                }
//                .frame(width: getRelativeWidth(136.0), height: getRelativeHeight(52.0),
//                       alignment: .leading)
//                .padding(.bottom, getRelativeHeight(57.0))
//                .padding(.horizontal, getRelativeWidth(6.0))
//            }
//            .hideNavigationBar()
//            .frame(width: getRelativeWidth(149.0), height: getRelativeHeight(114.0),
//                   alignment: .leading)
//            .background(RoundedCorners(topLeft: 11.0, topRight: 11.0).fill(ColorConstants.Gray50))
//            Text(StringConstants.kMsgPPrideTripleOmega)
//                .font(FontScheme.kNunitoLight(size: getRelativeHeight(11.0)))
//                .fontWeight(.light)
//                .foregroundColor(ColorConstants.Black90001)
//                .minimumScaleFactor(0.5)
//                .multilineTextAlignment(.center)
//                .frame(width: getRelativeWidth(127.0), height: getRelativeHeight(29.0),
//                       alignment: .center)
//                .padding(.top, getRelativeHeight(4.0))
//            Text(StringConstants.kLblKd28840)
//                .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
//                .fontWeight(.bold)
//                .foregroundColor(ColorConstants.Black90001)
//                .minimumScaleFactor(0.5)
//                .multilineTextAlignment(.leading)
//                .frame(width: getRelativeWidth(58.0), height: getRelativeHeight(17.0),
//                       alignment: .leading)
//                .padding(.top, getRelativeHeight(21.0))
//            Button(action: {}, label: {
//                Image("img_group_737")
//            })
//            .frame(width: getRelativeWidth(26.0), height: getRelativeWidth(28.0),
//                   alignment: .center)
//            .background(RoundedCorners(topLeft: 5.0, topRight: 5.0, bottomLeft: 5.0,
//                                       bottomRight: 5.0)
//                    .fill(ColorConstants.Blue300))
//            .shadow(color: ColorConstants.Black90054, radius: 2, x: 0, y: 1)
//        }
//        .frame(width: getRelativeWidth(161.0), alignment: .leading)
//        .background(RoundedCorners(topLeft: 11.0, topRight: 11.0, bottomLeft: 11.0,
//                                   bottomRight: 11.0)
//                .fill(ColorConstants.WhiteA700))
//        .shadow(color: ColorConstants.Black90023, radius: 5, x: 0, y: 0)
//        .hideNavigationBar()
//    }
//}
//
///* struct Productcard4Cell_Previews: PreviewProvider {
//
// static var previews: some View {
// 			Productcard4Cell()
// }
// } */
