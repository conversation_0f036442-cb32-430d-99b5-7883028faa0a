import SwiftUI


struct ProductDetailView: View {
    @StateObject var viewModel: ProductDetailViewModel
    @Environment(\.routerManager) private var routerManager: RouterManager
    @EnvironmentObject private var appState: AppState
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
  
    @State private var userReview: String = ""

    @State private var showReviewPopup = false

    init(productID: Int) {
        self._viewModel = StateObject(wrappedValue: ProductDetailViewModel(productID: productID))
    }
    
    var body: some View {
        SuperView(pageState: self.$viewModel.pageState) {
            MainScrollBody {
                VStack(spacing: 12.relativeHeight) {
                    self.productImageView
                    
                    self.productInfoView
                    
                    self.priceSectionView
                    
                    self.sellerAndBrandView
                    
                    self.productDescription

                    self.recommendedProductsView
                    
                    self.reviewsSection
                }
                .padding(.horizontal, 12.relativeWidth)
                .padding(.vertical, 16.relativeHeight)
            }
            .safeAreaInset(edge: .bottom) {
                self.actionButtons
            }
        }
        .background(ColorConstants.WhiteA700)
        .injectEnvironmentValues(self.viewModel)
        .overlay(self.reviewPopup)
    }
    
    private var productImageView: some View {
        VStack(spacing: 12.relativeHeight) {
            if let productDetailsModel = viewModel.productDetailsModel {
                PageView(productDetailsModel.photos, selection: self.$viewModel.currentPhotoIndex.animation()) { photo in
                    NetworkImageView(path: photo.path)
                        .scaledToFit()
                        .frame(width: getRelativeWidth(350), height: getRelativeHeight(322))
                        .clipped()
                }
                
                PageIndicator(numPages: productDetailsModel.photos.count, currentPage: self.$viewModel.currentPhotoIndex, selectedColor: ColorConstants.Blue300, unSelectedColor: ColorConstants.Pink30054, spacing: 8.0)
                    .frame(maxWidth: .infinity)
                    .visibility(productDetailsModel.photos.count > 1 ? .visible : .gone)
            }
        }.overlay(alignment: .topTrailing, content: {
            self.addToWishlistView
        })
        .frame(width: getRelativeWidth(350), height: getRelativeHeight(322))
        .background(ColorConstants.WhiteA700)
        .padding(.horizontal, getRelativeWidth(12.0))
    }
    
    private var productInfoView: some View {
        VStack(alignment: .leading) {
            self.ratingView
            
            if let productDetailsModel = viewModel.productDetailsModel {
                Text(productDetailsModel.name)
                    .font(Font.custom("Poppins", size: 20.relativeFontSize).weight(.medium))
                    .foregroundColor(ColorConstants.Black90001)
                    .multilineTextAlignment(.leading)
                    .frame(maxWidth: .infinity, alignment: .leading)
            }
        }
        .padding(.top, getRelativeHeight(17))
        .padding(.horizontal, 12.relativeWidth)
    }
    
    private var ratingView: some View {
        HStack(spacing: 8) {
            Image(.reviewStar)
                .renderingMode(.template)
                .resizable()
                .frame(width: 18, height: 18)
                .foregroundColor(Color(hex: "#FFC000"))
                .scaledToFit()
            if let productDetailsModel = viewModel.productDetailsModel {
                HStack {
                    Text(productDetailsModel.rating.toString(withDecimalPlaces: 1))
                        .font(Font.custom("Plus Jakarta Sans", size: 16).weight(.bold))
                        .foregroundColor(ColorConstants.Black900)
                       
                    Text("(\(productDetailsModel.reviewCount) Reviews)")
                        .font(Font.custom("Plus Jakarta Sans", size: 12).weight(.regular))
                        .foregroundColor(ColorConstants.Gray600)
                        
                }.kerning(0.08)
            }
        }
        .padding(.vertical, 6)
//        .frame(width: 77, height: 46)
//        .background(RoundedRectangle(cornerRadius: 6).fill(ColorConstants.GreenA700))
    }
    
    private var priceSectionView: some View {
        HStack {
            if let productDetailsModel = viewModel.productDetailsModel {
                HStack(spacing: 4.relativeWidth) {
                    Text(productDetailsModel.unitPrice)
                        .font(Font.custom("Poppins", size: 24.relativeFontSize).weight(.bold))
                        .foregroundColor(ColorConstants.Blue300)
                    
                    if let strokedPrice = productDetailsModel.strokedPrice {
                        Text(strokedPrice)
                            .strikethrough(true)
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(12)))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.Gray50002)
                            .visibility(productDetailsModel.hasDiscount ? .visible : .gone)
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
            }
            
            self.sizeAndQuantityView
        }
        .padding(.top, getRelativeHeight(12))
        .padding(.horizontal, 12.relativeWidth)
    }
    
    @State private var isWishlistAdded: Bool = false
    private var addToWishlistView: some View {
        HStack {
            if let productDetailsModel = viewModel.productDetailsModel {
                Image(self.isWishlistAdded ? "wishlist.fill" : "img_favorite_light_blue_600")
                    .resizable()
                    .scaledToFit()
                    .frame(width: 20, height: 20)
                
                    //            Text(StringConstants.kLblAddToWishlist)
                    //                .font(FontScheme.kNunitoLight(size: getRelativeHeight(14)))
                    //                .foregroundColor(ColorConstants.Blue600)
                    //                .padding(.leading, 5)
                
                    //        .animation(.bouncy, value: isWishlistAdded)
                    .frame(height: 20)
                    .onTapGesture {
                        self.viewModel.addToWishlist(productID: productDetailsModel.id, isAdd: !self.isWishlistAdded) {
                            if $0 != nil { self.isWishlistAdded.toggle() }
                        }
                    }
                    .onAppear {
                        self.isWishlistAdded = productDetailsModel.wishListStatus
                    }
            }
        }
    }
    
    private var sellerAndBrandView: some View {
        VStack(spacing: 8) {
            Text("Brand & Seller")
                .font(Font.custom("Poppins", size: 16.relativeFontSize).weight(.semibold))
                .foregroundColor(.init(hex: "#090F47"))
                .frame(maxWidth: .infinity, alignment: .leading)

            if let productDetailsModel = viewModel.productDetailsModel {
                self.sellerInfoRow(title: StringConstants.kLblBrand, value: productDetailsModel.brand.name)
                self.sellerInfoRow(title: StringConstants.kLblSeller, value: productDetailsModel.seller)
            }
        }
        .padding(.vertical, 14)
        .padding(.horizontal, 12)
    }
    
    private func sellerInfoRow(title: LocalizedStringKey, value: String) -> some View {
        HStack(spacing: 32) {
            Text(title)
                .font(FontScheme.kNunitoBold(size: 14))
                .foregroundColor(ColorConstants.Gray600)
            
            Text(value)
                .font(FontScheme.kNunitoLight(size: 14))
                .foregroundColor(ColorConstants.Black90001)
                .frame(maxWidth: .infinity, alignment: .leading)
        }
    }
    
    private var productDescription: some View {
        Group {
            if let productDetailsModel = viewModel.productDetailsModel, let html = productDetailsModel.description {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Product Details")
                        .font(Font.custom("Poppins", size: 16.relativeFontSize).weight(.semibold))
                        .foregroundColor(.init(hex: "#090F47"))

                    CustomRichText(html: html, fontFamily: "Poppins", fontSrc: "PoppinsLight.ttf", sizeAdjust: "90", fontColor: ColorConstants.DescriptionText, lineHeight: 110.0.relativeFontSize)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        
                }.padding(.horizontal, getRelativeWidth(12))
            }
        }
    }
    
    private var sizeAndQuantityView: some View {
        HStack {
//            sizePickerView
//            Spacer()
            self.quantityPickerView
        }
//        .padding(.top, getRelativeHeight(26))
        .padding(.horizontal, getRelativeWidth(16))
    }
    
//    @State private var sizes = ["S", "M", "L", "XL"]
//    @State private var selectedSize: String = "M" // Default selection
//    
//    private var sizePickerView: some View {
//        HStack {
//            // Menu for dropdown size selection
//            Menu {
//                ForEach(self.sizes, id: \.self) { size in
//                    Button(action: {
//                        self.selectedSize = size // Update selected size when tapped
//                    }) {
//                        Text(size)
//                    }
//                }
//            } label: {
//                Text(StringConstants.kLblSize)
//                    .font(FontScheme.kNunitoBold(size: 14))
//                    .foregroundColor(ColorConstants.Black90001)
//               
//                Text(self.selectedSize)
//                    .font(FontScheme.kNunitoBold(size: 14))
//                    .foregroundColor(ColorConstants.Blue300)
//                    .frame(maxWidth: .infinity, alignment: .trailing)
//            }
//        }
//        .padding(.horizontal)
//        .frame(width: 161, height: 42)
//        .background(RoundedRectangle(cornerRadius: 21).fill(ColorConstants.WhiteA700))
//        .overlay(RoundedRectangle(cornerRadius: 21).stroke(ColorConstants.Gray60001, lineWidth: 1))
//    }
    
    private var quantityPickerView: some View {
        Group {
            if let productDetailsModel = viewModel.productDetailsModel {
                HStack(spacing: 15.relativeWidth) {
                    Button { self.viewModel.decrement } label: {
                        Image(systemName: "minus")
                            .fontWeight(.bold)
                            .foregroundStyle(ColorConstants.WhiteA700)
                            .frame(width: 30, height: 30)
                            .background(.primary)
                            .clipShape(.rect(cornerRadius: 8))
                    }
                    
                    Text(String(format: "%02d", self.viewModel.quantity))
                        .font(Font.custom("Poppins", size: 18).weight(.medium))
                        .foregroundColor(ColorConstants.Black90001)
                        .contentTransition(.numericText())
                        .animation(.bouncy, value: self.viewModel.quantity)
//                        .frame(width: 10)
                    
                    Button { self.viewModel.increment } label: {
                        Image(systemName: "plus")
                            .fontWeight(.bold)
                            .foregroundStyle(ColorConstants.WhiteA700)
                            .frame(width: 30, height: 30)
                            .background(.primary)
                            .clipShape(.rect(cornerRadius: 8))
                    }
                }
                .disableWithOpacity(productDetailsModel.currentStock < 1)
                .frame(maxWidth: .infinity, alignment: .trailing)
            }
        }
    }
    
    private var actionButtons: some View {
        HStack(spacing: 21.relativeWidth) {
            if let productDetailsModel = viewModel.productDetailsModel {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Total")
                        .font(Font.custom("Poppins", size: 15.12.relativeFontSize).weight(.medium))
                        .foregroundColor(ColorConstants.Gray600)

                    let price = productDetailsModel.calculablePrice * Double(self.viewModel.quantity)
                    
                    Text("KD \(price.toString(withDecimalPlaces: 3))")
                        .font(Font.custom("Poppins", size: 19.43).weight(.bold))
                        .foregroundColor(ColorConstants.Black90001)
                        .contentTransition(.numericText())
                        .animation(.bouncy, value: price)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(.leading, 16)
                
                self.actionButton(title: StringConstants.kLblAddToCart, color: ColorConstants.Blue300, action: self.viewModel.onAddToCart)
                    .disableWithOpacity(productDetailsModel.currentStock < 1)
//                actionButton(title: StringConstants.kLblBuyNow, color: ColorConstants.Blue600, action: viewModel.onBuyNow)
//                    .disableWithOpacity(productDetailsModel.currentStock < 1)
            }
        }
        .padding(.vertical, 16.relativeHeight)
        .padding(.horizontal, getRelativeWidth(16))
        .frame(maxWidth: .infinity)
        .background(ColorConstants.WhiteA700.shadow(color: ColorConstants.Black9003f, radius: 2, x: 0, y: 0))
    }
    
    @ViewBuilder
    private func actionButton(title: LocalizedStringKey, color: Color, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            Text(title)
                .font(FontScheme.kNunitoBold(size: 16))
                .fontWeight(.bold)
                .foregroundColor(ColorConstants.WhiteA700)
                .frame(width: 167, height: 50)
                .background(RoundedRectangle(cornerRadius: 14).fill(color))
                .shadow(color: ColorConstants.Black9003f, radius: 4)
        }
    }
    
    private var recommendedProductsView: some View {
        Group {
            if let relatedProducts = viewModel.productDetailsModel?.relatedProducts, !relatedProducts.isEmpty {
                VStack {
                    HStack {
                        Text(StringConstants.kMsgRecommendedProduct)
                            .font(FontScheme.kNunitoExtraBold(size: 16))
                            .fontWeight(.heavy)
                            .foregroundColor(ColorConstants.Black90001)
                        
                        Spacer()
                        
                        Text(StringConstants.kLblSeeAll)
                            .font(FontScheme.kNunitoLight(size: 12))
                            .foregroundColor(ColorConstants.Pink300C1)
                    }
                    .padding(.trailing, 10)
                   
                    HorizontalScrollView(data: relatedProducts) { model in
                        ProductCardCell(model: model, onSelect: self.viewModel.onSelectProduct, onAddToCart: self.viewModel.onAddToCartRelatedProducts, onAddToWishlist: self.viewModel.onAddToWishlist)
                    }
                    .padding(.top, 20)
                }
                
                .padding(.top, 41)
            }
        }
    }
    
    private var reviewsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            if let productDetailsModel = viewModel.productDetailsModel {
                HStack(spacing: 8) {
                    Text("Reviews")
                    
                        .font(Font.custom("Poppins", size: 16.relativeFontSize).weight(.semibold))
                        .foregroundColor(.init(hex: "#090F47"))
                        .padding(.trailing)
                    
                    Text(productDetailsModel.rating.toString(withDecimalPlaces: 1))
                        .font(Font.custom("Poppins", size: 12.relativeFontSize).weight(.regular))
                        .foregroundColor(.init(hex: "#A7B0B5"))
                    
                    StarRatingView(rating: .constant(productDetailsModel.rating.toInt))
                    
                    Text("(\(productDetailsModel.reviewCount))")
                        .font(Font.custom("Poppins", size: 12.relativeFontSize).weight(.regular))
                        .foregroundColor(.init(hex: "#A7B0B5"))
                }
                
                Button {
                    if !productDetailsModel.addReviewStatus {
                        self.viewModel.updatePageState(.failure(error: "Please buy the product to write a review"))
                    } else {
                        if let user = AppState.user,
                           let name = user.name,
                           let email = user.email,
                           !name.isEmpty, !email.isEmpty
                        {
                            self.showReviewPopup = true
                            
                        } else {
                            self.routerManager.push(to: .accountSettings, appState: self.appState)
                        }
                    }
                } label: {
                    TextField("Type your review here", text: self.$userReview, axis: .vertical)
                        .multilineTextAlignment(.leading)
                        .lineLimit(1 ... 4)
                        .font(Font.custom("Poppins", size: 14).weight(.regular))
                        .foregroundColor(ColorConstants.Gray500)
                        .padding(.vertical, 8)
                        .overlay(alignment: .bottom) {
                            Rectangle()
                                .frame(height: 1)
                                .foregroundColor(ColorConstants.Gray400)
                        }
                        .disabled(true)
                }

                ForEach(productDetailsModel.reviews) { review in
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            HStack(spacing: 4) {
                                Image(.reviewStar)
                                    .renderingMode(.template)
                                    .resizable()
                                    .foregroundColor(Color(hex: "#FFC000"))
                                    .frame(width: 14, height: 14)
                                
                                let rating = review.rating
                                
                                Text(rating.toString(withDecimalPlaces: 1))
                                    .font(Font.custom("Poppins", size: 13.relativeFontSize).weight(.regular))
                                    .foregroundColor(.init(hex: "#A7B0B5"))
                            }
                            
                            Spacer()
                            Text(review.date)
                                .font(Font.custom("Poppins", size: 14.relativeFontSize).weight(.regular))
                                .foregroundColor(.init(hex: "#A7B0B5"))
                        }
                        
                        HStack(alignment: .top, spacing: 12) {
                            NetworkImageView(path: review.avatar)
                                .frame(width: 50, height: 50)
                                .clipShape(Circle())
                                .overlay(Circle().stroke(ColorConstants.Gray200, lineWidth: 1))
                            
                            VStack(alignment: .leading, spacing: 4) {
                                HStack {
                                    Text(review.name)
                                        .font(Font.custom("Poppins", size: 12.relativeFontSize).weight(.semibold))
                                        .foregroundColor(.init(hex: "#1C3146"))
//                                    Spacer()
//                                    Text(review.email)
//                                        .font(Font.custom("Poppins", size: 12.relativeFontSize).weight(.semibold))
//                                        .foregroundColor(.init(hex: "#1C3146"))
                                }
                                
                                Text(review.comment)
                                    .font(Font.custom("Poppins", size: 13.relativeFontSize).weight(.regular))
                                    .foregroundColor(.init(hex: "#465C67"))
                            }
                        }
                        Divider()
                    }
                }
            }
        }
        .padding(.horizontal, 12.relativeWidth)
        .padding(.top, 24)
    }
    
    private var reviewPopup: some View {
        Group {
            if self.showReviewPopup {
                ReviewPopupView(isVisible: self.$showReviewPopup, onSubmit: self.viewModel.submitReview)
            }
        }.animation(.easeInOut, value: self.showReviewPopup)
    }
}

enum ReviewFocusField: Hashable {
    case comment
}

struct ReviewPopupView: View {
    @Binding var isVisible: Bool
    @State private var isStepOne: Bool = true
    @State private var rating: Int = 0
    @State private var comment: String = ""
    var onSubmit: (Int, String) -> Void
    

    
    @FocusState private var focusedField: ReviewFocusField?
    
    var body: some View {
        ZStack(alignment: .bottom) {
            Color.black.opacity(0.4)
                .ignoresSafeArea()
                .onTapGesture {
                    withAnimation {
                        self.isVisible = false
                    }
                }
            
            VStack(spacing: 16) {
                // Header
                HStack {
                    Text("Write A Review")
                        .font(Font.custom("Poppins", size: 18).weight(.semibold))
                        .foregroundColor(ColorConstants.Black90001)
                        .frame(maxWidth: .infinity, alignment: .center)
                    
                    Button(action: {
                        withAnimation {
                            self.isVisible = false
                        }
                    }) {
                        Image(systemName: "xmark")
                            .foregroundColor(ColorConstants.Black90001)
                            .font(.system(size: 18, weight: .regular))
                    }
                }
                .padding(.horizontal, 16)
                .padding(.top, 16)
                
                Divider()
                    .background(ColorConstants.Gray400)
                
                Group {
                    if self.isStepOne {
                        // Step 1: Rating
                        VStack(spacing: 24) {
                            VStack(spacing: 32) {
                                Text("How would you rate it?")
                                    .font(Font.custom("Poppins", size: 16).weight(.semibold))
                                    .foregroundColor(ColorConstants.Blue300)
                                
                                StarRatingView(rating: self.$rating)
                                    .scaleEffect(1.8)
                            }
                            .frame(maxHeight: 120, alignment: .center)
                            
                            Button(action: {
                                self.isStepOne = false
                            }) {
                                Text("Next")
                                    .font(Font.custom("Poppins", size: 16).weight(.semibold))
                                    .foregroundColor(ColorConstants.WhiteA700)
                                    .frame(maxWidth: .infinity)
                                    .padding()
                                    .background(Capsule().fill(ColorConstants.Blue300))
                                    .frame(maxWidth: UIScreen.main.bounds.width * 0.5)
                            }
                            .padding(.horizontal, 16)
                        }
                        .transition(.backslide.combined(with: .opacity))
                    } else {
                        // Step 2: Review Text
                        VStack(spacing: 24) {
                            VStack(alignment: .leading) {
                                Text("Write your review")
                                    .font(Font.custom("Poppins", size: 16).weight(.semibold))
                                    .foregroundColor(ColorConstants.Blue300)
                                    .padding(.horizontal)
                                TextField("What did you like or dislike?", text: self.$comment, axis: .vertical)
                                    .autoFocus(.comment, equals: $focusedField,deadline: .now() + 0.5)
                                    .multilineTextAlignment(.leading)
                                    .lineLimit(4)
                                   
                                    .font(Font.custom("Poppins", size: 14).weight(.regular))
                                    .foregroundColor(ColorConstants.Gray500)
                                    .padding()
                                    .frame(height: 120, alignment: .topLeading)
                                    .background(RoundedRectangle(cornerRadius: 8).stroke(ColorConstants.Blue300, lineWidth: 1))
                                    .padding(.horizontal, 16)
                            }
//                            .frame(maxHeight: .infinity)
                            
                            HStack(spacing: 16) {
                                Button(action: {
                                    self.isStepOne = true
                                }) {
                                    Text("Back")
                                        .font(Font.custom("Poppins", size: 16).weight(.semibold))
                                        .foregroundColor(ColorConstants.Blue300)
                                        .frame(maxWidth: .infinity)
                                        .padding()
                                        .background(Capsule().stroke(ColorConstants.Blue300, lineWidth: 1))
                                }
                                
                                Button(action: {
                                    self.isVisible = false
                                    self.onSubmit(self.rating, self.comment)
                                }) {
                                    Text("Submit")
                                        .font(Font.custom("Poppins", size: 16).weight(.semibold))
                                        .foregroundColor(ColorConstants.WhiteA700)
                                        .frame(maxWidth: .infinity)
                                        .padding()
                                        .background(Capsule().fill(ColorConstants.Blue300))
                                }
                                .disableWithOpacity(self.comment.isEmpty)
                            }
                            .padding(.horizontal, 16)
                        }
                        .transition(.slide.combined(with: .opacity))
                    }
                }
//                .frame(maxHeight: .infinity)
            }
            .padding(.bottom, 16)
//            .frame(height: UIScreen.main.bounds.height * 0.35, alignment: .center)
            .background(RoundedRectangle(cornerRadius: 12).fill(ColorConstants.WhiteA700))
            .frame(maxWidth: .infinity)
            .transition(.move(edge: .bottom).combined(with: .opacity))
            
        }.animation(.easeInOut, value: self.isStepOne)
    }
}

struct StarRatingView: View {
    @Binding var rating: Int

    var body: some View {
        HStack(spacing: 2) {
            ForEach(1 ... 5, id: \.self) { index in
                Image(.reviewStar)
                    .renderingMode(.template)
                    .resizable()
                    .foregroundStyle(index <= self.rating ? Color(hex: "#FFC000") : Color(hex: "#CFD8DD"))
                    .frame(width: 16, height: 16)
                    .onTapGesture {
                        self.rating = index
                    }
            }
        }
    }
}


#Preview {
    NavigationStack {
        ProductDetailView(productID: 111).attachAllEnvironmentObjects()
    }
}

extension Double {
    /// Converts the Double to a String with the specified number of decimal places.
    func toString(withDecimalPlaces places: Int) -> String {
        return String(format: "%.\(places)f", self)
    }
}

extension View {
    func autoFocus<T: Hashable>(_ tag: T, equals selection: FocusState<T?>.Binding, deadline: DispatchTime = .now() + 0.2) -> some View {
        self
            .focused(selection, equals: tag)
            .onAppear {
                DispatchQueue.main.asyncAfter(deadline: deadline) {selection.wrappedValue = tag}
            }
    }
}

extension Double {
    /// Converts the Double to a truncated Int.
    var toInt: Int { Int(self) }

    /// Converts the Double to a rounded Int.
    var roundedInt: Int { Int(self.rounded()) }
}
