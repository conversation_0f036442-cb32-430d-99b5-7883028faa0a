//
//  ProductDetailsModel.swift
//  Wasfa
//
//  Created by Apple on 02/11/2024.
//

import Foundation

// This file was generated from JSON Schema using quicktype, do not modify it directly.
// To parse the JSON, add this file to your project and do:
//
//   let productDetailsModel = try? JSONDecoder().decode(ProductDetailsModel.self, from: jsonData)

import Foundation

// MARK: - ProductDetailsModel
struct ProductDetailsModel: Codable {
    let id: Int
    let name, seller: String
    let photos: [Photo]
    let thumbnailImage: String
    let tags: [String]
    let choiceOptions, colors: [String]?
    let hasDiscount: Bool
    let discount, strikedPrice, unitPrice: String
    let calculablePrice: Double
    let currencySymbol: String
    let currentStock: Int
    let unit: String
    let rating: Double
    let description, shortDescription, videoLink: String?
    let brand: Brand
    let relatedProducts: [ProductModel]
    let reviews: [Review]
    let reviewCount: Int
    let shareURL: String
    let stockVisibility, wishListStatus, cartAddedStatus, addReviewStatus: Bool
    let cartQty: Int

    enum CodingKeys: String, CodingKey {
        case id, name, seller, photos, thumbnailImage, tags, choiceOptions, colors, hasDiscount, discount, strikedPrice, unitPrice, calculablePrice, currencySymbol, currentStock, unit, rating, description, shortDescription, videoLink, brand, relatedProducts, reviews, reviewCount
        case shareURL = "shareUrl"
        case stockVisibility, wishListStatus, cartAddedStatus, addReviewStatus, cartQty
    }
    
    var strokedPrice: String? {
        unitPrice == strikedPrice ? nil : strikedPrice
    }
    
    
}

struct Review: Codable, Identifiable {
    let id: UUID = UUID()
    let email: String
    let rating: Double
    let date, comment, name: String
    let avatar: String
}


// MARK: - Brand
struct Brand: Codable, Identifiable, Equatable, Hashable {
    let id: Int
    let name: String
    let logo: String
}

// MARK: - Photo
struct Photo: Codable, Identifiable {
    let id: UUID = UUID()
    let variant: String
    let path: String
}

//// MARK: - RelatedProduct
//struct RelatedProduct: Codable {
//    let id: Int
//    let name: String
//    let thumbnailImage: String
//    let hasDiscount: Bool
//    let discount, unitPrice, strikedPrice: String
//    let description: String?
//    let shortDescription: String?
//    let tags: [String]
//    let rating: Int
//    let wishListStatus: Bool
//    let currentStock: Int
//    let buyOptionStatus: Bool
//    let buyOfferText: String
//    let cartAddedStatus: Bool
//    let cartQty: Int
//}
