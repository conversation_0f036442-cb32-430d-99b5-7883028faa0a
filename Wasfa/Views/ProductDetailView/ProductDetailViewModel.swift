import Foundation
import SwiftUI

class ProductDetailViewModel: SuperViewModel {
    @Published var productDetailsModel: ProductDetailsModel?
    @Published var currentPhotoIndex: Int? = 0
    
    @Published var quantity: Int = 1

    var increment: Void {
        if productDetailsModel?.currentStock ?? 1 > quantity { quantity += 1 }
    }

    var decrement: Void {
        if quantity > 1 { quantity -= 1 }
    }
    
    let productID: Int
    init(productID: Int) {
        self.productID = productID
        super.init()
        getProductDetails(id: productID)
    }
    
    func getProductDetails(id: Int) {
        onApiCall(api.productDetails, parameters: ["productId": productID]) {
            self.productDetailsModel = $0.data?.first
        }
    }
    
    func onSelectProduct(product: ProductModel) {
        routerManager?.push(to: .productDetails(id: product.id), appState: appState)
    }
    
    func submitReview(_ rating: Int, _ comment: String) {
        guard let productDetailsModel else { return }
        guard let user = AppState.user else { return }
        let parameters: [String: Any] = ["productId": productDetailsModel.id, "rating": rating, "comment": comment, "name": user.name, "email": user.email]
        
        onApiCall(api.addReview, parameters: parameters) {
            if $0.success { self.getProductDetails(id: self.productID) }
        }
    }
    
    func onAddToCart() {
        guard let productDetailsModel else { return }
        addToCart(productID: productDetailsModel.id) { data in
            self.appState?.updateCartQuantity(data?.cartCount)
        }
    }
    
    func onBuyNow() {
        guard let productDetailsModel else { return }
        addToCart(productID: productDetailsModel.id) { data in
            self.appState?.updateCartQuantity(data?.cartCount)
            self.routerManager?.push(to: .cart(), appState: self.appState)
        }
    }
    
    func addToCart(productID: Int, quantity: Int? = nil, onComplete: @escaping (CartAddModel?) -> Void) {
        let parameters: [String: Any] = ["productId": productID, "quantity": quantity ?? self.quantity]
        onApiCall(api.addToCart, parameters: parameters) {
            if let data = $0.data {
                onComplete(data)
            }
        }
    }
    
    func onAddToCartRelatedProducts(product: Binding<ProductModel>, quantity: Int, onSuccess: @escaping (Bool) -> Void) {
        addToCart(productID: product.wrappedValue.id, quantity: 1) { data in
            self.appState?.updateCartQuantity(data?.cartCount)
        }
    }
    
    func onAddToWishlist(product: ProductModel, isAdd: Bool) {
        addToWishlist(productID: product.id, isAdd: isAdd) { [weak self] model in
            self?.appState?.updateWishlistQuantity(model?.wishListCount)
        }
    }
    
    func addToWishlist(productID: Int, isAdd: Bool, onComplete: @escaping (WishlistAddModel?) -> Void) {
        let parameters: [String: Any] = ["productId": productID]
        
        if isAdd {
            onApiCall(api.addWishlist, parameters: parameters) {
                if $0.success {
                    onComplete($0.data)
                    self.appState?.updateWishlistQuantity($0.data?.wishListCount)
                }
            }
        } else {
            onApiCall(api.removeWishlist, parameters: parameters) {
                if $0.success {
                    onComplete($0.data)
                    self.appState?.updateWishlistQuantity($0.data?.wishListCount)
                }
            }
        }
    }
}
