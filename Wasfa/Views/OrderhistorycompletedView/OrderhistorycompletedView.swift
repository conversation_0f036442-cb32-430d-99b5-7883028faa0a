//import SwiftUI
//
//struct OrderhistorycompletedView: View {
//    @StateObject var orderhistorycompletedViewModel = OrderhistorycompletedViewModel()
//    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
//    var body: some View {
//        VStack {
//            VStack {
//                VStack(spacing: 0) {
//                    ScrollView(.vertical, showsIndicators: false) {
//                        LazyVStack {
//                            ForEach(0 ... 4, id: \.self) { index in
//                                Userprofile1Cell()
//                            }
//                        }
//                    }
//                }
//                .frame(width: getRelativeWidth(355.0), alignment: .center)
//                .padding(.top, getRelativeHeight(21.0))
//                .padding(.horizontal, getRelativeWidth(17.0))
//            }
//            .frame(width: UIScreen.main.bounds.width, alignment: .center)
//            .background(ColorConstants.WhiteA700)
//            .padding(.top, getRelativeHeight(30.0))
//            .padding(.bottom, getRelativeHeight(10.0))
//        }
//        .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height)
//        .background(ColorConstants.WhiteA700)
//        .ignoresSafeArea()
//        .hideNavigationBar()
//    }
//}
//
//struct OrderhistorycompletedView_Previews: PreviewProvider {
//    static var previews: some View {
//        OrderhistorycompletedView()
//    }
//}
