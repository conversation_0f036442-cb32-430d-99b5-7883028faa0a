import SwiftUI

struct PasswordSucessView: View {
    @StateObject var passwordSucessViewModel = PasswordSucessViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    var body: some View {
        VStack {
            VStack {
                Image("img_success")
                    .resizable()
                    .frame(width: getRelativeWidth(152.0), height: getRelativeWidth(152.0),
                           alignment: .center)
                    .scaledToFit()
                    .clipped()
                Text(StringConstants.kMsgPasswordChanged)
                    .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(24.0)))
                    .fontWeight(.heavy)
                    .foregroundColor(ColorConstants.Black90001)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(width: getRelativeWidth(227.0), height: getRelativeHeight(33.0),
                           alignment: .topLeading)
                    .padding(.top, getRelativeHeight(22.0))
                Text(StringConstants.kMsgYourPasswordHas)
                    .font(FontScheme.kRobotoRomanRegular(size: getRelativeHeight(14.0)))
                    .fontWeight(.regular)
                    .foregroundColor(ColorConstants.Black90001)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.center)
                    .frame(width: getRelativeWidth(225.0), height: getRelativeHeight(37.0),
                           alignment: .center)
                    .padding(.top, getRelativeHeight(18.0))
                Button(action: {}, label: {
                    HStack(spacing: 0) {
                        Text(StringConstants.kLblBackToLogin)
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
                            .fontWeight(.bold)
                            .padding(.horizontal, getRelativeWidth(30.0))
                            .padding(.vertical, getRelativeHeight(14.0))
                            .foregroundColor(ColorConstants.WhiteA700)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.center)
                            .frame(width: getRelativeWidth(348.0), height: getRelativeHeight(54.0),
                                   alignment: .center)
                            .background(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0,
                                                       bottomRight: 8.0)
                                    .fill(ColorConstants.Blue300))
                            .shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 0)
                            .padding(.vertical, getRelativeHeight(25.0))
                    }
                })
                .frame(width: getRelativeWidth(348.0), height: getRelativeHeight(54.0),
                       alignment: .center)
                .background(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0,
                                           bottomRight: 8.0)
                        .fill(ColorConstants.Blue300))
                .shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 0)
                .padding(.vertical, getRelativeHeight(25.0))
            }
            .frame(width: UIScreen.main.bounds.width, alignment: .topLeading)
            .background(ColorConstants.WhiteA700)
            .padding(.top, getRelativeHeight(30.0))
            .padding(.bottom, getRelativeHeight(10.0))
        }
        .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height)
        .background(ColorConstants.WhiteA700)
        .ignoresSafeArea()
        .hideNavigationBar()
    }
}

struct PasswordSucessView_Previews: PreviewProvider {
    static var previews: some View {
        PasswordSucessView()
    }
}
