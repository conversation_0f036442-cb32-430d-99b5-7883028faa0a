//
//  SuperViewModel.swift
//  Wasfa
//
//  Created by Apple on 27/10/2024.
//

import Alamofire
import Foundation
import SwiftUI

@MainActor
class SuperViewModel: ObservableObject {
    var cancellables: Cancellables = []
    let api: RepositoriesAPIProtocol = RepositoriesAPI.shared
    
    @Published var pageState: PageState = .stable
    @Published private(set) var appState: AppState?
    @Published private(set) var routerManager: RouterManager?
   
    init() {
        print("SuperViewModel class is initialized")
        
        if let token = UserDefaultsSecure.sharedInstance.getGeneratedToken() {
            print("token:", token, terminator: "\n\n\n")
        }
    }
    
    deinit {
        print("SuperViewModel class is deinitialized")
        
        cancellables.forEach { $0.cancel() }
    }
    
    func initEnvironment(appState: AppState? = nil, routerManager: RouterManager? = nil) {
        self.appState = appState
        self.routerManager = routerManager
    }
    
    func isUnAuthenticateErrorOccurred(_ error: String) -> Bool {
        error.contains(AppConstants.unAuthenticateErrorString) ||
            error.contains(AppConstants.unAuthenticateErrorStringSecond)
    }
    
    func updatePageState(_ state: PageState) { pageState = state }
    
    func handleApiCallFailure(_ error: String) { updatePageState(.failure(error: error)) }
    
    func onApiCallFailure(error: String, customLoadingBinding: Binding<Bool>?) {
        handleApiCallFailure(error)
        if let customLoadingBinding = customLoadingBinding { customLoadingBinding.wrappedValue = false }
    }
    
    func onApiCallSuccess<T>(response: ApiBaseModel<T>, customLoadingBinding: Binding<Bool>?) {
//        if response.data is [Any] {
//            let dataArray = response.data as! [Any]
//
//            if dataArray.isEmpty {
//                updatePageState(.noData)
//            }else{
//                updatePageState(.stable)
//            }
//
//        } else {
//            updatePageState(.stable)
//        }
        
        updatePageState(.stable)
        if let customLoadingBinding = customLoadingBinding { customLoadingBinding.wrappedValue = false }
    }
    
    func onApiCall<T, R>(_ execution: (_ request: R, _ completionHandler: @escaping (Result<CommonApiResponse<T>, AFError>) -> Void) -> Void, parameters: R, dismissKeyboard: Bool = true, withStateChange: Bool = true, withLoadingIndicator: Bool = true, customLoadingBinding: Binding<Bool>? = nil, onSuccess: @escaping (CommonApiResponse<T>) -> Void, onFailure: TypeCallback<String>? = nil) {
        updatePageState(.loading(withStateChange && withLoadingIndicator))
        
        if let customLoadingBinding = customLoadingBinding { customLoadingBinding.wrappedValue = true }
        
        if dismissKeyboard { Utilities.dismissKeyboard() }
        execution(parameters) { result in
            switch result {
            case let .success(result):
                if result.error {
                    onFailure?(result.message)
                    if withStateChange {
                        self.onApiCallFailure(error: result.message, customLoadingBinding: customLoadingBinding)
                    }
                    return
                }
                if withStateChange {
                    self.onApiCallSuccess(response: result, customLoadingBinding: customLoadingBinding)
                }
                onSuccess(result)
            case let .failure(error):
                onFailure?(error.localizedDescription)
                if withStateChange {
                    self.onApiCallFailure(error: error.localizedDescription, customLoadingBinding: customLoadingBinding)
                }
            }
        }
    }
}

extension SuperViewModel {
    func handleError(_ error: Error) {
        DispatchQueue.main.async {
            self.pageState = .failure(error: error.localizedDescription)
        }
    }
}
