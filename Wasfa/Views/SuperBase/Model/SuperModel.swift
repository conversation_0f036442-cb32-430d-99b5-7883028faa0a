//
//  SuperModel.swift
//  Wasfa
//
//  Created by Apple on 27/10/2024.
//


import SwiftUI



struct VoidStruct: Codable {}

protocol SuperApiResponse<T>: Codable {
    associatedtype T: Codable
    var error:Bool { get }
    var status: Int? { get }
    var message: String { get }
    var data: T { get }
}
struct ApiBaseModel<T: Codable>: SuperApiResponse {
    
    var error:Bool
    
    var status: Int?

    var message: String

    var data: T?

    var success: Bool { !error }
}



enum AlertType {
    case alert, choiceAlert
}

struct AlertConfig: Equatable {
    static func == (lhs: AlertConfig, rhs: AlertConfig) -> Bool {
        return lhs.title == rhs.title &&
            lhs.text == rhs.text &&
            lhs.alertType == rhs.alertType &&
            lhs.cancelButtonText == rhs.cancelButtonText &&
            lhs.okButtonText == rhs.okButtonText
    }

    let title, text: String
    let cancelButtonText, okButtonText: String
    let alertType: AlertType
    let onCancel: VoidCallback?
    let onOk: VoidCallback?

    init(title: String, text: String, cancelButtonText: String = "Cancel", okButtonText: String = "Ok", alertType: AlertType = .alert, onCancel: VoidCallback? = nil, onOk: VoidCallback? = nil) {
        self.title = title
        self.text = text
        self.cancelButtonText = cancelButtonText
        self.okButtonText = okButtonText
        self.alertType = alertType
        self.onCancel = onCancel
        self.onOk = onOk
    }
}


enum PageState: Equatable {
    static func == (lhs: PageState, rhs: PageState) -> Bool {
        switch (lhs, rhs) {
        case (.stable, .stable),
             (.loading, .loading),
             (.success, .success),
           
             (.failure, .failure),
             (.message, .message),
             (.custom, .custom):
            return true
        default:
            return false
        }
    }

    case stable, loading(_ load: Bool = true), success(message: String, onDone: (() -> Void)? = nil), failure(error: String), message(config: AlertConfig), custom(view: any View)
}
