//
//  MainScrollBody.swift
//  Wasfa
//
//  Created by Apple on 20/09/2024.
//

import SwiftUI

struct MainScrollBody<Content, TrailingContent>: View where Content: View, TrailingContent: View {
    let content: Content
    let trailingContent: TrailingContent

    let backButtonWithTitle: LocalizedStringKey?
    let isHome: Bool
    let hideBackButton: Bool
    let enableScrollView: Bool
    let hideNavigationBar: Bool
    let invertColor: Bool

    @Environment(\.routerManager) private var routerManager: RouterManager
    @EnvironmentObject private var appState: AppState
    @Environment(\.dismiss) private var dismiss

//    @State private var invertColor:Bool

    init(invertColor: Bool = true, backButtonWithTitle: LocalizedStringKey? = nil, isHome: Bool = false, hideBackButton: Bool = false, hideNavigationBar: Bool = false, enableScroll: Bool = true, @ViewBuilder trailingContent: () -> TrailingContent, @ViewBuilder content: () -> Content) {
        self.enableScrollView = enableScroll
        self.isHome = isHome
        self.hideNavigationBar = hideNavigationBar
        self.hideBackButton = hideBackButton
        self.trailingContent = trailingContent()
        self.content = content()
        self.backButtonWithTitle = backButtonWithTitle
        self.invertColor = invertColor
    }

    var body: some View {
        if self.enableScrollView {
            ScrollView(showsIndicators: false) {
//                GeometryReader { geo in
                self.content
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .if(!self.hideNavigationBar, transform: {
                        $0.logoNavigationBar(invertColor: self.invertColor, trailingContent: self.trailingContent, appState: _appState, router: _routerManager, onDismiss: self.dismiss, backButtonWithTitle: self.backButtonWithTitle, hideBackButton: self.hideBackButton, isHome: self.isHome)
                    })
//                        .toolbarBackground(ColorConstants.bgGradient,for: .navigationBar)
                    .hideNavigationBar(false)
            }

        } else {
            self.content
                .if(!self.hideNavigationBar, transform: {
                    $0.logoNavigationBar(invertColor: false, trailingContent: self.trailingContent, appState: _appState, router: _routerManager, onDismiss: self.dismiss, backButtonWithTitle: self.backButtonWithTitle, hideBackButton: self.hideBackButton, isHome: self.isHome)
                })
//                .toolbarBackground(ColorConstants.bgGradient,for: .navigationBar)
                .hideNavigationBar(false)
        }
    }
}

extension MainScrollBody where TrailingContent == EmptyView {
    init(invertColor: Bool = true, backButtonWithTitle: LocalizedStringKey? = nil, isHome: Bool = false, hideBackButton: Bool = false, hideNavigationBar: Bool = false, enableScroll: Bool = true, @ViewBuilder content: () -> Content) {
        self.init(invertColor: invertColor, backButtonWithTitle: backButtonWithTitle, isHome: isHome, hideBackButton: hideBackButton, hideNavigationBar: hideNavigationBar, enableScroll: enableScroll, trailingContent: { EmptyView() }, content: content)
    }
}

extension View {
    func logoNavigationBar<Content: View>(
        invertColor: Bool,
        trailingContent: Content,
        appState: EnvironmentObject<AppState>,
        router: Environment<RouterManager>,
        onDismiss: DismissAction? = nil,
        backButtonWithTitle: LocalizedStringKey?,
        hideBackButton: Bool,
        isHome: Bool = false
    ) -> some View {
        toolbar {
            let routesType: RoutesType = mapRouterWithTab(appState: appState)
            let isCurrentRootEmpty: Bool = checkCurrentRouteEmpty(appState: appState, router: router)

            // Leading Item (Side Menu or Back Button)
            ToolbarItem(placement: .topBarLeading) {
                if isHome {
                    self.sideMenuButton(invertColor: invertColor, appState: appState.wrappedValue)
                } else {
                    self.backButton(
                        isCurrentRootEmpty: isCurrentRootEmpty,
                        backButtonWithTitle: backButtonWithTitle,
                        invertColor: invertColor,
                        router: router,
                        routesType: routesType
                    )
                }
            }

            // Logo (Principal)
            if isHome {
                ToolbarItem(placement: .principal) {
                    self.logoView
                }
            }

            // Trailing Items (Search, Notifications, Cart, Custom Content)
            ToolbarItem(placement: .topBarTrailing) {
                self.trailingIcons(invertColor: invertColor, isHome: isHome, appState: appState, router: router)
            }

            // Trailing Content (Custom injected content)
            ToolbarItem(placement: .topBarTrailing) {
                trailingContent
                    .foregroundColor(invertColor ? ColorConstants.Black900 : ColorConstants.WhiteA700)
            }
        }
    }

    // MARK: - Helper Views
    private func sideMenuButton(invertColor: Bool, appState: AppState) -> some View {
        Button {
            appState.showSideMenu.toggle()
        } label: {
            Image(.imgMenu)
                .renderingMode(.template)
                .resizable()
                .scaledToFit()
                .frame(width: 27, height: 27)
                .foregroundColor(invertColor ? ColorConstants.Black900 : ColorConstants.WhiteA700)
        }
        .transition(.slide)
    }

    private func backButton(
        isCurrentRootEmpty: Bool,
        backButtonWithTitle: LocalizedStringKey?,
        invertColor: Bool,
        router: Environment<RouterManager>,
        routesType: RoutesType
    ) -> some View {
        HStack(spacing: getRelativeWidth(15.0)) {
            if !isCurrentRootEmpty {
                Button(action: {
                    withAnimation {
                        router.wrappedValue.goBack(where: routesType)
                    }
                }, label: {
                    Image("img_vector_9")
                        .renderingMode(.template)
                        .resizable()
                        .scaledToFit()
                        .rotateBasedOnLanguage()
                        .frame(width: 18, height: 18)
                })
                
            }

            if let backButtonWithTitle = backButtonWithTitle {
                Text(backButtonWithTitle)
                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(16)))
                    .fontWeight(.bold)
                    .multilineTextAlignment(.leading)
                    .frame(height: getRelativeHeight(22), alignment: .topLeading)
            }
        }
        .foregroundColor(invertColor ? ColorConstants.Black900 : ColorConstants.WhiteA700)
    }

    private var logoView: some View {
        Image(.imgWasfaLogo1)
            .resizable()
            .aspectRatio(contentMode: .fit)
            .frame(width: 77, height: 42)
            .clipped()
    }

    private func trailingIcons(invertColor: Bool, isHome: Bool = false, appState: EnvironmentObject<AppState>, router: Environment<RouterManager>) -> some View {
        HStack(spacing: 16) {
           
            let isCurrentHaveProductList: Bool = checkCurrentRouteContainString(appState: appState, router: router, route: "productListing")
            
            self.iconButton(imageName: .imgSearchLight, invertColor: invertColor, type: .search) {
                router.wrappedValue.push(to: .productListing(reuestModel: .init(title: ""), isSearchFocused: true), appState: appState.wrappedValue)
            }
                .visibility(isHome || isCurrentHaveProductList ? .gone : .visible)
            
           
            
            

            let isCurrentHaveNotification: Bool = checkCurrentRouteContain(appState: appState, router: router, route: .notification)
            let isCurrentHaveCart: Bool = checkCurrentRouteContain(appState: appState, router: router, route: .cart(false))
            let isCurrentHaveRxCart: Bool = checkCurrentRouteContain(appState: appState, router: router, route: .cart(true))

            if !isCurrentHaveNotification {
                self.iconButton(imageName: .imgBellPinLight, invertColor: invertColor, type: .notification) {
                    router.wrappedValue.push(to: .notification, appState: appState.wrappedValue)
                }
                .overlay(
                    // Badge overlay
                    ZStack {
                        let notificationCount: Int = appState.wrappedValue.notificationCount

                        if notificationCount > 0 {
                            Text("\(notificationCount > 9 ? "9+" : "\(notificationCount)")")
                                .font(Font.custom("Inter", size: 11))
                                .multilineTextAlignment(.center)
                                .foregroundColor(.white)
                                .contentTransition(.numericText())
                                .animation(.bouncy, value: notificationCount)
                                .padding(4)
                                .background(ColorConstants.Blue300)
                                .clipShape(.circle)
                        }
                    }
                    .offset(x: 12, y: -12)
                )
            }
            
            if !isCurrentHaveRxCart {
                self.iconButton(imageName: .rxCart, invertColor: invertColor, type: .rxCart) {
                    router.wrappedValue.push(to: .cart(true), appState: appState.wrappedValue)
                }
//                .overlay(
//                    // Badge overlay
//                    ZStack {
//                        let cartQuantity: Int = appState.wrappedValue.cartCount
//
//                        if cartQuantity > 0 {
//                            Text("\(cartQuantity)")
//                                .font(Font.custom("Inter", size: 11))
//                                .multilineTextAlignment(.center)
//                                .foregroundColor(.white)
//                                .contentTransition(.numericText())
//                                .animation(.bouncy, value: cartQuantity)
//                                .padding(5)
//                                .background(ColorConstants.Blue300)
//                                .clipShape(.circle)
//                        }
//                    }
//                    .offset(x: 12, y: -12)
//                )
            }

            if !isCurrentHaveCart {
                self.iconButton(imageName: .navigationCart, invertColor: invertColor, type: .cart) {
                    router.wrappedValue.push(to: .cart(), appState: appState.wrappedValue)
                }
                .overlay(
                    // Badge overlay
                    ZStack {
                        let cartQuantity: Int = appState.wrappedValue.cartCount

                        if cartQuantity > 0 {
                            Text("\(cartQuantity)")
                                .font(Font.custom("Inter", size: 11))
                                .multilineTextAlignment(.center)
                                .foregroundColor(.white)
                                .contentTransition(.numericText())
                                .animation(.bouncy, value: cartQuantity)
                                .padding(5)
                                .background(ColorConstants.Blue300)
                                .clipShape(.circle)
                        }
                    }
                    .offset(x: 12, y: -12)
                )
            }
        }
    }

    private func iconButton(imageName: ImageResource, invertColor: Bool, type: NavigationTrailingButtonType, onAction: @escaping () -> Void) -> some View {
        // Computed property that returns CGSize based on the switch case
        var frameSize: CGSize {
            switch type {
            case .search, .notification:
                 CGSize(width: 27, height: 27)
            case .cart:
                 CGSize(width: 25, height: 25)
            case .rxCart:
                 CGSize(width: 25, height: 25)
            }
        }

        return Button(action: onAction) {
            Image(imageName)
                .resizable()
                .scaledToFit()
                .frame(width: frameSize.width, height: frameSize.height)
                .foregroundColor(invertColor ? ColorConstants.Black900 : ColorConstants.WhiteA700)
        }
    }
}

enum NavigationTrailingButtonType {
    case search, notification, cart, rxCart
}

func checkCurrentRouteEmpty(appState: EnvironmentObject<AppState>, router: Environment<RouterManager>) -> Bool {
    switch appState.wrappedValue.selectedTab {
    case .home:
        return router.wrappedValue.homeRouteList.isEmpty
    case .category:
        return router.wrappedValue.categoryRouteList.isEmpty
    case .wishlist:
        return router.wrappedValue.wishlistRouteList.isEmpty
    case .myAccount:
        return router.wrappedValue.myAccountRouteList.isEmpty
    }
}

func checkCurrentRouteContain(appState: EnvironmentObject<AppState>, router: Environment<RouterManager>, route: Route) -> Bool {
    switch appState.wrappedValue.selectedTab {
    case .home:
        return router.wrappedValue.homeRouteList.last == route
    case .category:
        return router.wrappedValue.categoryRouteList.last == route
    case .wishlist:
        return router.wrappedValue.wishlistRouteList.last == route
    case .myAccount:
        return router.wrappedValue.myAccountRouteList.last == route
    }
}

func checkCurrentRouteContainString(appState: EnvironmentObject<AppState>, router: Environment<RouterManager>, route: String) -> Bool {
    switch appState.wrappedValue.selectedTab {
    case .home:
        return router.wrappedValue.homeRouteList.last?.rawValue == route
    case .category:
        return router.wrappedValue.categoryRouteList.last?.rawValue == route
    case .wishlist:
        return router.wrappedValue.wishlistRouteList.last?.rawValue == route
    case .myAccount:
        return router.wrappedValue.myAccountRouteList.last?.rawValue == route
    }
}

func mapRouterWithTab(appState: EnvironmentObject<AppState>) -> RoutesType {
    switch appState.wrappedValue.selectedTab {
    case .home:
        return .homeRoute
    case .category:
        return .categoryRoute
    case .wishlist:
        return .wishlistRoute
    case .myAccount:
        return .myAccountRoute
    }
}

enum ScrollOffsetNamespace {
    static let namespace = "scrollView"
}

struct ScrollOffsetPreferenceKey: PreferenceKey {
    static var defaultValue: CGPoint = .zero
    static func reduce(value: inout CGPoint, nextValue: () -> CGPoint) {}
}

struct ScrollViewOffsetTracker: View {
    var body: some View {
        GeometryReader { geo in
            Color.clear
                .preference(
                    key: ScrollOffsetPreferenceKey.self,
                    value: geo.frame(in: .named(ScrollOffsetNamespace.namespace)).origin
                )
        }
        .frame(height: 0)
    }
}

private extension ScrollView {
    func withOffsetTracking(action: @escaping (_ offset: CGPoint) -> Void) -> some View {
        self.coordinateSpace(name: ScrollOffsetNamespace.namespace)
            .onPreferenceChange(ScrollOffsetPreferenceKey.self, perform: action)
    }
}

public struct ScrollViewWithOffset<Content: View>: View {
    public init(
        _ axes: Axis.Set = .vertical,
        showsIndicators: Bool = true,
        onScroll: ScrollAction? = nil,
        @ViewBuilder content: @escaping () -> Content
    ) {
        self.axes = axes
        self.showsIndicators = showsIndicators
        self.onScroll = onScroll ?? { _ in }
        self.content = content
    }

    private let axes: Axis.Set
    private let showsIndicators: Bool
    private let onScroll: ScrollAction
    private let content: () -> Content

    public typealias ScrollAction = (_ offset: CGPoint) -> Void

    public var body: some View {
        ScrollView(self.axes, showsIndicators: self.showsIndicators) {
            ZStack(alignment: .top) {
                ScrollViewOffsetTracker()
                self.content()
            }
        }
        .withOffsetTracking(action: self.onScroll)
    }
}
