//
//  SuperView.swift
//  Wasfa
//
//  Created by Apple on 27/10/2024.
//

import SwiftUI

struct SuperView<Content: View>: View {
    let content: Content
    @Binding private var pageState: PageState
    @State private var loading: Bool = false
    init(pageState: Binding<PageState>, @ViewBuilder content: () -> Content) {
        self.content = content()
        self._pageState = pageState
    }

    @ViewBuilder
    var body: some View {
        content
            .overlay {
                switch pageState {
                case .stable, .success:
                    EmptyView()
                case .loading(let isLoading):
                    if isLoading {
//                        VStack {}
//                            .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height,
//                                   alignment: .center)
//                            .background(Color.black.opacity(0.25).edgesIgnoringSafeArea(.all))
                        VStack {}
                            .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height,
                                   alignment: .center)
                            .background(.ultraThinMaterial)
                            .edgesIgnoringSafeArea(.all)
                    }
                default:
                    VStack {}
                        .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height,
                               alignment: .center)
                        .background(.ultraThinMaterial)
                        .edgesIgnoringSafeArea(.all)
                }
            }
            .hideNavigationBar(pageState == .custom(view: EmptyView()))
            .overlay(alignment: .center) {
                Group{
                    switch pageState {
                    case .loading(let loading):
                        if loading {
                            ActivityLoaderView()
                                .transition(.scale)
                               
                               
                        }
                    case .message(let config):
                        AlertView(pageState: $pageState, config: config)
                            .transition(.slide)
                    case .failure(let error):
                        AlertView(pageState: $pageState, config: .init(title: "Error!", text: error))
                            .padding(.horizontal)
                            .transition(.slide)
                    case .custom(let view):
                        AnyView(view)
                            .transition(.move(edge: .leading))
                    default:
                        EmptyView()
                    }
                }
                .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height,
                       alignment: .center)
                .animation(.bouncy, value: pageState)
                
            }
    }
}
