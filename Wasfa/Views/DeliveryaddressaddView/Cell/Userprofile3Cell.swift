//import SwiftUI
//
//struct Userprofile3Cell: View {
//    var body: some View {
//        VStack(alignment: .leading, spacing: 0) {
//            HStack {
//                RadioGroup(items: [StringConstants.kLblJithunRaj2.stringify], selectedId: .constant(""),
//                           selectedColor: ColorConstants.Blue600)
//                    .frame(width: getRelativeWidth(101.0), height: getRelativeHeight(26.0),
//                           alignment: .topLeading)
//                    .overlay(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
//                                            bottomRight: 13.0)
//                            .stroke(ColorConstants.Blue600,
//                                    lineWidth: 1))
//                    .background(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
//                                               bottomRight: 13.0)
//                            .fill(ColorConstants.WhiteA700))
//                Text(StringConstants.kLblEdit)
//                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
//                    .fontWeight(.bold)
//                    .foregroundColor(ColorConstants.Blue300)
//                    .minimumScaleFactor(0.5)
//                    .multilineTextAlignment(.leading)
//                    .frame(width: getRelativeWidth(22.0), height: getRelativeHeight(17.0),
//                           alignment: .leading)
//                    .padding(.leading, getRelativeWidth(201.0))
//            }
//            .frame(width: getRelativeWidth(327.0), height: getRelativeHeight(26.0),
//                   alignment: .leading)
//            .padding(.trailing)
//            Text(StringConstants.kLbl9946785622)
//                .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
//                .fontWeight(.bold)
//                .foregroundColor(ColorConstants.Gray90002)
//                .minimumScaleFactor(0.5)
//                .multilineTextAlignment(.leading)
//                .frame(width: getRelativeWidth(75.0), height: getRelativeHeight(17.0),
//                       alignment: .leading)
//                .padding(.top, getRelativeHeight(5.0))
//                .padding(.leading, getRelativeWidth(41.0))
//            HStack {
//                Image("img_pin_fill_teal_900")
//                    .resizable()
//                    .frame(width: getRelativeWidth(17.0), height: getRelativeWidth(19.0),
//                           alignment: .leading)
//                    .scaledToFit()
//                Text(StringConstants.kMsg4thFloorAlZumorrodah)
//                    .font(FontScheme.kRobotoRomanRegular(size: getRelativeHeight(11.0)))
//                    .fontWeight(.regular)
//                    .foregroundColor(ColorConstants.Black90001)
//                    .minimumScaleFactor(0.5)
//                    .multilineTextAlignment(.leading)
//                    .frame(width: getRelativeWidth(217.0), height: getRelativeHeight(32.0),
//                           alignment: .leading)
//                    .padding(.leading, getRelativeWidth(6.0))
//            }
//            .frame(width: getRelativeWidth(242.0), height: getRelativeHeight(32.0),
//                   alignment: .leading)
//            .padding(.vertical, getRelativeHeight(11.0))
//            .padding(.leading, getRelativeWidth(35.0))
//            .padding(.trailing, getRelativeWidth(55.0))
//        }
//        .frame(width: getRelativeWidth(353.0), alignment: .leading)
//        .overlay(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0, bottomRight: 8.0)
//            .stroke(ColorConstants.Blue600,
//                    lineWidth: 1))
//        .background(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0, bottomRight: 8.0)
//            .fill(ColorConstants.WhiteA700))
//        .hideNavigationBar()
//    }
//}
//
///* struct Userprofile3Cell_Previews: PreviewProvider {
//
// static var previews: some View {
// 			Userprofile3Cell()
// }
// } */
