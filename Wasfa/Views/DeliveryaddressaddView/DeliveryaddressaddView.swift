//import SwiftUI
//
//struct DeliveryaddressaddView: View {
//    @StateObject var deliveryaddressaddViewModel = DeliveryaddressaddViewModel()
//    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
//    var body: some View {
//        VStack {
//            VStack {
//                HStack {
//                    HStack {
//                        Button(action: {}, label: {
//                            Image("img_vector_9")
//                        })
//                        .frame(width: getRelativeWidth(24.0), height: getRelativeWidth(24.0),
//                               alignment: .center)
//                        .padding(.bottom, getRelativeHeight(4.0))
//                        Text(StringConstants.kMsgDeliveryAddress)
//                            .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(16.0)))
//                            .fontWeight(.heavy)
//                            .foregroundColor(ColorConstants.Black90001)
//                            .minimumScaleFactor(0.5)
//                            .multilineTextAlignment(.leading)
//                            .frame(width: getRelativeWidth(131.0), height: getRelativeHeight(22.0),
//                                   alignment: .topLeading)
//                            .padding(.leading, getRelativeWidth(20.0))
//                        Image("img_search_light")
//                            .resizable()
//                            .frame(width: getRelativeWidth(27.0), height: getRelativeWidth(27.0),
//                                   alignment: .center)
//                            .scaledToFit()
//                            .clipped()
//                            .padding(.leading, getRelativeWidth(34.0))
//                        Image("img_bell_pin_light")
//                            .resizable()
//                            .frame(width: getRelativeWidth(27.0), height: getRelativeWidth(27.0),
//                                   alignment: .center)
//                            .scaledToFit()
//                            .clipped()
//                            .padding(.leading, getRelativeWidth(33.0))
//                        ZStack(alignment: .topLeading) {
//                            Image("img_vector")
//                                .resizable()
//                                .frame(width: getRelativeWidth(18.0),
//                                       height: getRelativeHeight(15.0), alignment: .center)
//                                .scaledToFit()
//                                .clipped()
//                                .padding(.top, getRelativeHeight(6.85))
//                                .padding(.trailing, getRelativeWidth(11.0))
//                            Image("img_vector_gray_900_03")
//                                .resizable()
//                                .frame(width: getRelativeWidth(9.0), height: getRelativeHeight(4.0),
//                                       alignment: .center)
//                                .scaledToFit()
//                                .clipped()
//                                .padding(.bottom, getRelativeHeight(15.88))
//                                .padding(.trailing, getRelativeWidth(15.06))
//                            Image("img_vector_gray_900_03_1x1")
//                                .resizable()
//                                .frame(width: getRelativeWidth(1.0), height: getRelativeWidth(1.0),
//                                       alignment: .center)
//                                .scaledToFit()
//                                .clipped()
//                                .padding(.top, getRelativeHeight(11.31))
//                                .padding(.trailing, getRelativeWidth(21.65))
//                            HStack {
//                                Image("img_vector_gray_900_03_1x1")
//                                    .resizable()
//                                    .frame(width: getRelativeWidth(1.0),
//                                           height: getRelativeWidth(1.0), alignment: .center)
//                                    .scaledToFit()
//                                    .clipped()
//                                    .padding(.vertical, getRelativeHeight(11.0))
//                                ZStack(alignment: .topTrailing) {
//                                    ZStack {}
//                                        .hideNavigationBar()
//                                        .frame(width: getRelativeWidth(16.0),
//                                               height: getRelativeWidth(16.0), alignment: .center)
//                                        .background(RoundedCorners(topLeft: 8.0, topRight: 8.0,
//                                                                   bottomLeft: 8.0,
//                                                                   bottomRight: 8.0)
//                                                .fill(ColorConstants.Blue300))
//                                    Text("2")
//                                        .font(FontScheme
//                                            .kInterRegular(size: getRelativeHeight(11.0)))
//                                        .fontWeight(.regular)
//                                        .foregroundColor(ColorConstants.WhiteA700)
//                                        .minimumScaleFactor(0.5)
//                                        .multilineTextAlignment(.leading)
//                                        .frame(width: getRelativeWidth(6.0),
//                                               height: getRelativeHeight(14.0),
//                                               alignment: .topLeading)
//                                        .padding(.leading, getRelativeWidth(5.33))
//                                }
//                                .hideNavigationBar()
//                                .frame(width: getRelativeWidth(16.0),
//                                       height: getRelativeWidth(16.0), alignment: .center)
//                            }
//                            .frame(width: getRelativeWidth(16.0), height: getRelativeWidth(16.0),
//                                   alignment: .topTrailing)
//                            .padding(.bottom, getRelativeHeight(6.0))
//                            .padding(.leading, getRelativeWidth(12.42))
//                        }
//                        .hideNavigationBar()
//                        .frame(width: getRelativeWidth(29.0), height: getRelativeHeight(22.0),
//                               alignment: .center)
//                        .padding(.leading, getRelativeWidth(29.0))
//                    }
//                    .frame(width: getRelativeWidth(355.0), height: getRelativeHeight(28.0),
//                           alignment: .leading)
//                    .padding(.leading, getRelativeWidth(19.0))
//                    .padding(.trailing, getRelativeWidth(16.0))
//                }
//                .frame(width: UIScreen.main.bounds.width - 20, height: getRelativeHeight(73.0),
//                       alignment: .leading)
//                .background(ColorConstants.WhiteA700)
//                .shadow(color: ColorConstants.Black90021, radius: 14, x: 0, y: 6)
//                ZStack(alignment: .center) {
//                    VStack {
//                        HStack {
//                            Button(action: {}, label: {
//                                Image("img_add_white_a700")
//                            })
//                            .frame(width: getRelativeWidth(24.0), height: getRelativeWidth(24.0),
//                                   alignment: .center)
//                            Text(StringConstants.kLblAddNewAddress2)
//                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
//                                .fontWeight(.bold)
//                                .foregroundColor(ColorConstants.WhiteA700)
//                                .minimumScaleFactor(0.5)
//                                .multilineTextAlignment(.leading)
//                                .frame(width: getRelativeWidth(153.0),
//                                       height: getRelativeHeight(25.0), alignment: .topLeading)
//                                .padding(.leading, getRelativeWidth(6.0))
//                        }
//                        .frame(width: getRelativeWidth(348.0), height: getRelativeHeight(54.0),
//                               alignment: .center)
//                        .background(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0,
//                                                   bottomRight: 8.0)
//                                .fill(ColorConstants.Blue300))
//                        .shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 0)
//                        .padding(.horizontal, getRelativeWidth(4.0))
//                        VStack(spacing: 0) {
//                            ScrollView(.vertical, showsIndicators: false) {
//                                LazyVStack {
//                                    ForEach(0 ... 4, id: \.self) { index in
//                                        Userprofile3Cell()
//                                    }
//                                }
//                            }
//                        }
//                        .frame(width: getRelativeWidth(355.0), alignment: .center)
//                        .padding(.top, getRelativeHeight(25.0))
//                    }
//                    .frame(width: getRelativeWidth(355.0), height: getRelativeHeight(808.0),
//                           alignment: .center)
//                    .padding(.bottom, getRelativeHeight(185.0))
//                    .padding(.horizontal, getRelativeWidth(18.0))
//                    VStack {
//                        VStack {
//                            Group {
//                                HStack {
//                                    Text(StringConstants.kLblAddNewAddress)
//                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
//                                        .fontWeight(.bold)
//                                        .foregroundColor(ColorConstants.Black90001)
//                                        .minimumScaleFactor(0.5)
//                                        .multilineTextAlignment(.leading)
//                                        .frame(width: getRelativeWidth(140.0),
//                                               height: getRelativeHeight(22.0),
//                                               alignment: .topLeading)
//                                    Button(action: {}, label: {
//                                        Image("img_close_round_light")
//                                    })
//                                    .frame(width: getRelativeWidth(24.0),
//                                           height: getRelativeWidth(24.0), alignment: .center)
//                                    .padding(.leading, getRelativeWidth(160.0))
//                                }
//                                .frame(width: getRelativeWidth(324.0),
//                                       height: getRelativeHeight(24.0), alignment: .center)
//                                .padding(.top, getRelativeHeight(4.0))
//                                .padding(.leading, getRelativeWidth(10.0))
//                                .padding(.trailing, getRelativeWidth(15.0))
//                                Divider()
//                                    .frame(width: getRelativeWidth(349.0),
//                                           height: getRelativeHeight(1.0), alignment: .center)
//                                    .background(ColorConstants.Cyan8003f)
//                                    .padding(.top, getRelativeHeight(16.0))
//                                Text(StringConstants.kLblName)
//                                    .font(FontScheme
//                                        .kRobotoRomanMedium(size: getRelativeHeight(14.0)))
//                                    .fontWeight(.medium)
//                                    .foregroundColor(ColorConstants.Black90001)
//                                    .minimumScaleFactor(0.5)
//                                    .multilineTextAlignment(.leading)
//                                    .frame(width: getRelativeWidth(50.0),
//                                           height: getRelativeHeight(17.0), alignment: .topLeading)
//                                    .padding(.top, getRelativeHeight(12.0))
//                                    .padding(.leading, getRelativeWidth(10.0))
//                                HStack {
//                                    TextField(StringConstants.kLblName2,
//                                              text: $deliveryaddressaddViewModel.nameText)
//                                        .font(FontScheme
//                                            .kRobotoRomanMedium(size: getRelativeHeight(12.0)))
//                                        .foregroundColor(ColorConstants.Gray500C4)
//                                        .padding()
//                                }
//                                .frame(width: getRelativeWidth(335.0),
//                                       height: getRelativeHeight(53.0), alignment: .center)
//                                .overlay(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                        bottomLeft: 15.0, bottomRight: 15.0)
//                                        .stroke(ColorConstants.Black90001,
//                                                lineWidth: 1))
//                                .background(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                           bottomLeft: 15.0, bottomRight: 15.0)
//                                        .fill(ColorConstants.WhiteA700))
//                                .padding(.top, getRelativeHeight(5.0))
//                                .padding(.horizontal, getRelativeWidth(6.0))
//                                Text(StringConstants.kLblEmail)
//                                    .font(FontScheme
//                                        .kRobotoRomanMedium(size: getRelativeHeight(14.0)))
//                                    .fontWeight(.medium)
//                                    .foregroundColor(ColorConstants.Black90001)
//                                    .minimumScaleFactor(0.5)
//                                    .multilineTextAlignment(.leading)
//                                    .frame(width: getRelativeWidth(48.0),
//                                           height: getRelativeHeight(17.0), alignment: .topLeading)
//                                    .padding(.top, getRelativeHeight(16.0))
//                                    .padding(.leading, getRelativeWidth(10.0))
//                                HStack {
//                                    TextField(StringConstants.kMsgExambleGamilCom,
//                                              text: $deliveryaddressaddViewModel.emailText)
//                                        .font(FontScheme
//                                            .kRobotoRomanMedium(size: getRelativeHeight(12.0)))
//                                        .foregroundColor(ColorConstants.Gray500C4)
//                                        .padding()
//                                }
//                                .frame(width: getRelativeWidth(335.0),
//                                       height: getRelativeHeight(53.0), alignment: .center)
//                                .overlay(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                        bottomLeft: 15.0, bottomRight: 15.0)
//                                        .stroke(ColorConstants.Black90001,
//                                                lineWidth: 1))
//                                .background(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                           bottomLeft: 15.0, bottomRight: 15.0)
//                                        .fill(ColorConstants.WhiteA700))
//                                .padding(.top, getRelativeHeight(5.0))
//                                .padding(.horizontal, getRelativeWidth(6.0))
//                                Text(StringConstants.kLblPhone)
//                                    .font(FontScheme
//                                        .kRobotoRomanMedium(size: getRelativeHeight(14.0)))
//                                    .fontWeight(.medium)
//                                    .foregroundColor(ColorConstants.Black90001)
//                                    .minimumScaleFactor(0.5)
//                                    .multilineTextAlignment(.leading)
//                                    .frame(width: getRelativeWidth(53.0),
//                                           height: getRelativeHeight(17.0), alignment: .topLeading)
//                                    .padding(.top, getRelativeHeight(16.0))
//                                    .padding(.leading, getRelativeWidth(10.0))
//                                HStack {
//                                    TextField(StringConstants.kLbl96599999999,
//                                              text: $deliveryaddressaddViewModel.phoneText)
//                                        .font(FontScheme
//                                            .kRobotoRomanMedium(size: getRelativeHeight(12.0)))
//                                        .foregroundColor(ColorConstants.Gray500C4)
//                                        .padding()
//                                }
//                                .frame(width: getRelativeWidth(335.0),
//                                       height: getRelativeHeight(53.0), alignment: .center)
//                                .overlay(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                        bottomLeft: 15.0, bottomRight: 15.0)
//                                        .stroke(ColorConstants.Black90001,
//                                                lineWidth: 1))
//                                .background(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                           bottomLeft: 15.0, bottomRight: 15.0)
//                                        .fill(ColorConstants.WhiteA700))
//                                .padding(.top, getRelativeHeight(5.0))
//                                .padding(.horizontal, getRelativeWidth(6.0))
//                            }
//                            Group {
//                                Text(StringConstants.kLblArea)
//                                    .font(FontScheme
//                                        .kRobotoRomanMedium(size: getRelativeHeight(14.0)))
//                                    .fontWeight(.medium)
//                                    .foregroundColor(ColorConstants.Black90001)
//                                    .minimumScaleFactor(0.5)
//                                    .multilineTextAlignment(.leading)
//                                    .frame(width: getRelativeWidth(42.0),
//                                           height: getRelativeHeight(17.0), alignment: .topLeading)
//                                    .padding(.top, getRelativeHeight(17.0))
//                                    .padding(.leading, getRelativeWidth(10.0))
//                                Picker(StringConstants.kLblSelectArea,
//                                       selection: $deliveryaddressaddViewModel.selectareaPicker1) {
//                                    ForEach(deliveryaddressaddViewModel.selectareaPicker1Values,
//                                            id: \.self) { value in
//                                        Text(value)
//                                    }
//                                }
//                                .foregroundColor(ColorConstants.Gray500C4)
//                                .font(.system(size: getRelativeHeight(12)))
//                                .pickerStyle(MenuPickerStyle())
//                                Text(StringConstants.kLblBlock)
//                                    .font(FontScheme
//                                        .kRobotoRomanMedium(size: getRelativeHeight(14.0)))
//                                    .fontWeight(.medium)
//                                    .foregroundColor(ColorConstants.Black90001)
//                                    .minimumScaleFactor(0.5)
//                                    .multilineTextAlignment(.leading)
//                                    .frame(width: getRelativeWidth(48.0),
//                                           height: getRelativeHeight(17.0), alignment: .topLeading)
//                                    .padding(.top, getRelativeHeight(22.0))
//                                    .padding(.leading, getRelativeWidth(10.0))
//                                HStack {
//                                    TextField(StringConstants.kLblBlock2,
//                                              text: $deliveryaddressaddViewModel.blockoneText)
//                                        .font(FontScheme
//                                            .kRobotoRomanMedium(size: getRelativeHeight(12.0)))
//                                        .foregroundColor(ColorConstants.Gray500C4)
//                                        .padding()
//                                }
//                                .frame(width: getRelativeWidth(335.0),
//                                       height: getRelativeHeight(53.0), alignment: .center)
//                                .overlay(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                        bottomLeft: 15.0, bottomRight: 15.0)
//                                        .stroke(ColorConstants.Black90001,
//                                                lineWidth: 1))
//                                .background(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                           bottomLeft: 15.0, bottomRight: 15.0)
//                                        .fill(ColorConstants.WhiteA700))
//                                .padding(.top, getRelativeHeight(5.0))
//                                .padding(.horizontal, getRelativeWidth(6.0))
//                                Text(StringConstants.kLblStreetName)
//                                    .font(FontScheme
//                                        .kRobotoRomanMedium(size: getRelativeHeight(14.0)))
//                                    .fontWeight(.medium)
//                                    .foregroundColor(ColorConstants.Black90001)
//                                    .minimumScaleFactor(0.5)
//                                    .multilineTextAlignment(.leading)
//                                    .frame(width: getRelativeWidth(94.0),
//                                           height: getRelativeHeight(17.0), alignment: .topLeading)
//                                    .padding(.top, getRelativeHeight(17.0))
//                                    .padding(.leading, getRelativeWidth(10.0))
//                                HStack {
//                                    TextField(StringConstants.kLblStreetName2,
//                                              text: $deliveryaddressaddViewModel.nameText1)
//                                        .font(FontScheme
//                                            .kRobotoRomanMedium(size: getRelativeHeight(12.0)))
//                                        .foregroundColor(ColorConstants.Gray500C4)
//                                        .padding()
//                                }
//                                .frame(width: getRelativeWidth(335.0),
//                                       height: getRelativeHeight(53.0), alignment: .center)
//                                .overlay(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                        bottomLeft: 15.0, bottomRight: 15.0)
//                                        .stroke(ColorConstants.Black90001,
//                                                lineWidth: 1))
//                                .background(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                           bottomLeft: 15.0, bottomRight: 15.0)
//                                        .fill(ColorConstants.WhiteA700))
//                                .padding(.top, getRelativeHeight(5.0))
//                                .padding(.horizontal, getRelativeWidth(6.0))
//                                Text(StringConstants.kLblBuilding)
//                                    .font(FontScheme
//                                        .kRobotoRomanMedium(size: getRelativeHeight(14.0)))
//                                    .fontWeight(.medium)
//                                    .foregroundColor(ColorConstants.Black90001)
//                                    .minimumScaleFactor(0.5)
//                                    .multilineTextAlignment(.leading)
//                                    .frame(width: getRelativeWidth(65.0),
//                                           height: getRelativeHeight(17.0), alignment: .topLeading)
//                                    .padding(.top, getRelativeHeight(19.0))
//                                    .padding(.leading, getRelativeWidth(10.0))
//                                HStack {
//                                    TextField(StringConstants.kLblBuilding2,
//                                              text: $deliveryaddressaddViewModel.buildingoneText)
//                                        .font(FontScheme
//                                            .kRobotoRomanMedium(size: getRelativeHeight(12.0)))
//                                        .foregroundColor(ColorConstants.Gray500C4)
//                                        .padding()
//                                }
//                                .frame(width: getRelativeWidth(335.0),
//                                       height: getRelativeHeight(53.0), alignment: .center)
//                                .overlay(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                        bottomLeft: 15.0, bottomRight: 15.0)
//                                        .stroke(ColorConstants.Black90001,
//                                                lineWidth: 1))
//                                .background(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                           bottomLeft: 15.0, bottomRight: 15.0)
//                                        .fill(ColorConstants.WhiteA700))
//                                .padding(.top, getRelativeHeight(4.0))
//                                .padding(.horizontal, getRelativeWidth(6.0))
//                            }
//                            Group {
//                                Text(StringConstants.kLblAvenue)
//                                    .font(FontScheme
//                                        .kRobotoRomanMedium(size: getRelativeHeight(14.0)))
//                                    .fontWeight(.medium)
//                                    .foregroundColor(ColorConstants.Black90001)
//                                    .minimumScaleFactor(0.5)
//                                    .multilineTextAlignment(.leading)
//                                    .frame(width: getRelativeWidth(49.0),
//                                           height: getRelativeHeight(17.0), alignment: .topLeading)
//                                    .padding(.top, getRelativeHeight(15.0))
//                                    .padding(.leading, getRelativeWidth(10.0))
//                                HStack {
//                                    TextField(StringConstants.kLblAvenue,
//                                              text: $deliveryaddressaddViewModel.avenueoneText)
//                                        .font(FontScheme
//                                            .kRobotoRomanMedium(size: getRelativeHeight(12.0)))
//                                        .foregroundColor(ColorConstants.Gray500C4)
//                                        .padding()
//                                }
//                                .frame(width: getRelativeWidth(335.0),
//                                       height: getRelativeHeight(53.0), alignment: .center)
//                                .overlay(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                        bottomLeft: 15.0, bottomRight: 15.0)
//                                        .stroke(ColorConstants.Black90001,
//                                                lineWidth: 1))
//                                .background(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                           bottomLeft: 15.0, bottomRight: 15.0)
//                                        .fill(ColorConstants.WhiteA700))
//                                .padding(.top, getRelativeHeight(5.0))
//                                .padding(.horizontal, getRelativeWidth(6.0))
//                                VStack(alignment: .leading, spacing: 0) {
//                                    Text(StringConstants.kLblNotes)
//                                        .font(FontScheme
//                                            .kRobotoRomanMedium(size: getRelativeHeight(14.0)))
//                                        .fontWeight(.medium)
//                                        .foregroundColor(ColorConstants.Black90001)
//                                        .minimumScaleFactor(0.5)
//                                        .multilineTextAlignment(.leading)
//                                        .frame(width: getRelativeWidth(39.0),
//                                               height: getRelativeHeight(17.0),
//                                               alignment: .topLeading)
//                                        .padding(.leading, getRelativeWidth(4.0))
//                                    HStack {
//                                        TextField(StringConstants.kLblNotes,
//                                                  text: $deliveryaddressaddViewModel.notesoneText)
//                                            .font(FontScheme
//                                                .kRobotoRomanMedium(size: getRelativeHeight(12.0)))
//                                            .foregroundColor(ColorConstants.Gray500C4)
//                                            .padding()
//                                    }
//                                    .frame(width: getRelativeWidth(335.0),
//                                           height: getRelativeHeight(88.0), alignment: .leading)
//                                    .overlay(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                            bottomLeft: 15.0, bottomRight: 15.0)
//                                            .stroke(ColorConstants.Black90001,
//                                                    lineWidth: 1))
//                                    .background(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                               bottomLeft: 15.0, bottomRight: 15.0)
//                                            .fill(ColorConstants.WhiteA700))
//                                    .padding(.top, getRelativeHeight(5.0))
//                                }
//                                .frame(width: getRelativeWidth(349.0),
//                                       height: getRelativeHeight(110.0), alignment: .center)
//                                .padding(.top, getRelativeHeight(16.0))
//                                Button(action: {}, label: {
//                                    HStack(spacing: 0) {
//                                        Text(StringConstants.kLblAddAddress2)
//                                            .font(FontScheme
//                                                .kNunitoBold(size: getRelativeHeight(18.0)))
//                                            .fontWeight(.bold)
//                                            .padding(.horizontal, getRelativeWidth(30.0))
//                                            .padding(.vertical, getRelativeHeight(11.0))
//                                            .foregroundColor(ColorConstants.WhiteA700)
//                                            .minimumScaleFactor(0.5)
//                                            .multilineTextAlignment(.leading)
//                                            .frame(width: getRelativeWidth(306.0),
//                                                   height: getRelativeHeight(47.0),
//                                                   alignment: .topLeading)
//                                            .background(RoundedCorners(topLeft: 8.0, topRight: 8.0,
//                                                                       bottomLeft: 8.0,
//                                                                       bottomRight: 8.0)
//                                                    .fill(ColorConstants.Blue300))
//                                            .shadow(color: ColorConstants.Black9003f, radius: 4,
//                                                    x: 0, y: 0)
//                                            .padding(.top, getRelativeHeight(15.0))
//                                            .padding(.horizontal, getRelativeWidth(23.0))
//                                    }
//                                })
//                                .frame(width: getRelativeWidth(306.0),
//                                       height: getRelativeHeight(47.0), alignment: .topLeading)
//                                .background(RoundedCorners(topLeft: 8.0, topRight: 8.0,
//                                                           bottomLeft: 8.0, bottomRight: 8.0)
//                                        .fill(ColorConstants.Blue300))
//                                .shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 0)
//                                .padding(.top, getRelativeHeight(15.0))
//                                .padding(.horizontal, getRelativeWidth(23.0))
//                            }
//                        }
//                        .frame(width: getRelativeWidth(356.0), height: UIScreen.main.bounds.height,
//                               alignment: .topLeading)
//                        .background(RoundedCorners(topLeft: 28.0, topRight: 28.0, bottomLeft: 28.0,
//                                                   bottomRight: 28.0)
//                                .fill(ColorConstants.WhiteA700))
//                        .shadow(color: ColorConstants.Black9003f, radius: 7, x: 0, y: -7)
//                        .padding(.trailing, getRelativeWidth(6.0))
//                    }
//                    .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height,
//                           alignment: .topLeading)
//                    .background(ColorConstants.Black9005b)
//                }
//                .hideNavigationBar()
//                .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height,
//                       alignment: .topLeading)
//            }
//            .frame(width: UIScreen.main.bounds.width, alignment: .topLeading)
//            .background(ColorConstants.WhiteA700)
//            .padding(.top, getRelativeHeight(30.0))
//            .padding(.bottom, getRelativeHeight(10.0))
//        }
//        .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height)
//        .background(ColorConstants.WhiteA700)
//        .ignoresSafeArea()
//        .hideNavigationBar()
//    }
//}
//
//struct DeliveryaddressaddView_Previews: PreviewProvider {
//    static var previews: some View {
//        DeliveryaddressaddView()
//    }
//}
