import Foundation
import SwiftUI

class DeliveryaddressaddViewModel: ObservableObject {
    @Published var nextScreen: String? = nil
    @Published var nameText: String = ""
    @Published var emailText: String = ""
    @Published var phoneText: String = ""
    @Published var selectareaPicker1: String = "Option 1"
    @Published var selectareaPicker1Values: [String] = ["Option 1", "Option 2", "Option 3"]
    @Published var blockoneText: String = ""
    @Published var nameText1: String = ""
    @Published var buildingoneText: String = ""
    @Published var avenueoneText: String = ""
    @Published var notesoneText: String = ""
}
