import Foundation
import SwiftUI

class AddaddressViewModel: ObservableObject {
    @Published var nextScreen: String? = nil
    @Published var knetRadio: String = ""
    @Published var creditcardRadio: String = ""
    @Published var cashonRadio: String = ""
    @Published var nameText: String = ""
    @Published var emailText: String = ""
    @Published var _99valueoneText: String = ""
    @Published var selectareaPicker1: String = "Option 1"
    @Published var selectareaPicker1Values: [String] = ["Option 1", "Option 2", "Option 3"]
    @Published var blockvalueoneText: String = ""
    @Published var nameText1: String = ""
    @Published var buildingvalueText: String = ""
    @Published var avenuevalueoneText: String = ""
    @Published var notesvalueoneText: String = ""
}
