//import SwiftUI
//
//struct AddaddressView: View {
//    @StateObject var addaddressViewModel = AddaddressViewModel()
//    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
//    var body: some View {
//        VStack {
//            VStack {
//                HStack {
//                    HStack {
//                        Button(action: {}, label: {
//                            Image("img_vector_9")
//                        })
//                        .frame(width: getRelativeWidth(24.0), height: getRelativeWidth(24.0),
//                               alignment: .center)
//                        .padding(.bottom, getRelativeHeight(4.0))
//                        Text(StringConstants.kLblCheckout)
//                            .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(16.0)))
//                            .fontWeight(.heavy)
//                            .foregroundColor(ColorConstants.Black90001)
//                            .minimumScaleFactor(0.5)
//                            .multilineTextAlignment(.leading)
//                            .frame(width: getRelativeWidth(71.0), height: getRelativeHeight(22.0),
//                                   alignment: .topLeading)
//                            .padding(.bottom, getRelativeHeight(4.0))
//                            .padding(.leading, getRelativeWidth(20.0))
//                        Image("img_search_light")
//                            .resizable()
//                            .frame(width: getRelativeWidth(27.0), height: getRelativeWidth(27.0),
//                                   alignment: .center)
//                            .scaledToFit()
//                            .clipped()
//                            .padding(.leading, getRelativeWidth(93.0))
//                        Image("img_bell_pin_light")
//                            .resizable()
//                            .frame(width: getRelativeWidth(27.0), height: getRelativeWidth(27.0),
//                                   alignment: .center)
//                            .scaledToFit()
//                            .clipped()
//                            .padding(.leading, getRelativeWidth(33.0))
//                        ZStack(alignment: .topLeading) {
//                            Image("img_vector")
//                                .resizable()
//                                .frame(width: getRelativeWidth(18.0),
//                                       height: getRelativeHeight(15.0), alignment: .center)
//                                .scaledToFit()
//                                .clipped()
//                                .padding(.top, getRelativeHeight(6.85))
//                                .padding(.trailing, getRelativeWidth(11.0))
//                            Image("img_vector_gray_900_03")
//                                .resizable()
//                                .frame(width: getRelativeWidth(9.0), height: getRelativeHeight(4.0),
//                                       alignment: .center)
//                                .scaledToFit()
//                                .clipped()
//                                .padding(.bottom, getRelativeHeight(15.88))
//                                .padding(.trailing, getRelativeWidth(15.06))
//                            Image("img_vector_gray_900_03_1x1")
//                                .resizable()
//                                .frame(width: getRelativeWidth(1.0), height: getRelativeWidth(1.0),
//                                       alignment: .center)
//                                .scaledToFit()
//                                .clipped()
//                                .padding(.top, getRelativeHeight(11.31))
//                                .padding(.trailing, getRelativeWidth(21.65))
//                            HStack {
//                                Image("img_vector_gray_900_03_1x1")
//                                    .resizable()
//                                    .frame(width: getRelativeWidth(1.0),
//                                           height: getRelativeWidth(1.0), alignment: .center)
//                                    .scaledToFit()
//                                    .clipped()
//                                    .padding(.vertical, getRelativeHeight(11.0))
//                                ZStack(alignment: .topTrailing) {
//                                    ZStack {}
//                                        .hideNavigationBar()
//                                        .frame(width: getRelativeWidth(16.0),
//                                               height: getRelativeWidth(16.0), alignment: .center)
//                                        .background(RoundedCorners(topLeft: 8.0, topRight: 8.0,
//                                                                   bottomLeft: 8.0,
//                                                                   bottomRight: 8.0)
//                                                .fill(ColorConstants.Blue300))
//                                    Text("2")
//                                        .font(FontScheme
//                                            .kInterRegular(size: getRelativeHeight(11.0)))
//                                        .fontWeight(.regular)
//                                        .foregroundColor(ColorConstants.WhiteA700)
//                                        .minimumScaleFactor(0.5)
//                                        .multilineTextAlignment(.leading)
//                                        .frame(width: getRelativeWidth(6.0),
//                                               height: getRelativeHeight(14.0),
//                                               alignment: .topLeading)
//                                        .padding(.leading, getRelativeWidth(5.33))
//                                }
//                                .hideNavigationBar()
//                                .frame(width: getRelativeWidth(16.0),
//                                       height: getRelativeWidth(16.0), alignment: .center)
//                            }
//                            .frame(width: getRelativeWidth(16.0), height: getRelativeWidth(16.0),
//                                   alignment: .topTrailing)
//                            .padding(.bottom, getRelativeHeight(6.0))
//                            .padding(.leading, getRelativeWidth(12.42))
//                        }
//                        .hideNavigationBar()
//                        .frame(width: getRelativeWidth(29.0), height: getRelativeHeight(22.0),
//                               alignment: .center)
//                        .padding(.leading, getRelativeWidth(29.0))
//                    }
//                    .frame(width: getRelativeWidth(355.0), height: getRelativeHeight(28.0),
//                           alignment: .leading)
//                    .padding(.leading, getRelativeWidth(19.0))
//                    .padding(.trailing, getRelativeWidth(16.0))
//                }
//                .frame(width: UIScreen.main.bounds.width - 20, height: getRelativeHeight(73.0),
//                       alignment: .leading)
//                .background(ColorConstants.WhiteA700)
//                .shadow(color: ColorConstants.Black90021, radius: 14, x: 0, y: 6)
//                ScrollView(.vertical, showsIndicators: false) {
//                    ZStack(alignment: .center) {
//                        VStack(alignment: .leading, spacing: 0) {
//                            Text(StringConstants.kLblDelivery)
//                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
//                                .fontWeight(.bold)
//                                .foregroundColor(ColorConstants.Black90001)
//                                .minimumScaleFactor(0.5)
//                                .multilineTextAlignment(.leading)
//                                .frame(width: getRelativeWidth(66.0),
//                                       height: getRelativeHeight(22.0), alignment: .topLeading)
//                            HStack {
//                                Button(action: {}, label: {
//                                    Image("img_group_148")
//                                })
//                                .frame(width: getRelativeWidth(37.0),
//                                       height: getRelativeWidth(37.0), alignment: .center)
//                                .background(RoundedCorners(topLeft: 4.0, topRight: 4.0,
//                                                           bottomLeft: 4.0, bottomRight: 4.0)
//                                        .fill(ColorConstants.WhiteA700))
//                                .shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 0)
//                                VStack(alignment: .leading, spacing: 0) {
//                                    Text(StringConstants.kLblDeliveryDate)
//                                        .font(FontScheme
//                                            .kNunitoMedium(size: getRelativeHeight(14.0)))
//                                        .fontWeight(.medium)
//                                        .foregroundColor(ColorConstants.Black90001)
//                                        .minimumScaleFactor(0.5)
//                                        .multilineTextAlignment(.leading)
//                                        .frame(width: getRelativeWidth(91.0),
//                                               height: getRelativeHeight(20.0),
//                                               alignment: .topLeading)
//                                    Text(StringConstants.kLblMay052024)
//                                        .font(FontScheme
//                                            .kNunitoMedium(size: getRelativeHeight(12.0)))
//                                        .fontWeight(.medium)
//                                        .foregroundColor(ColorConstants.Blue600)
//                                        .minimumScaleFactor(0.5)
//                                        .multilineTextAlignment(.leading)
//                                        .frame(width: getRelativeWidth(84.0),
//                                               height: getRelativeHeight(17.0),
//                                               alignment: .topLeading)
//                                }
//                                .frame(width: getRelativeWidth(91.0),
//                                       height: getRelativeHeight(38.0), alignment: .bottom)
//                                .padding(.leading, getRelativeWidth(8.0))
//                                Button(action: {}, label: {
//                                    HStack(spacing: 0) {
//                                        Text(StringConstants.kLblSchedule)
//                                            .font(FontScheme
//                                                .kNunitoBold(size: getRelativeHeight(12.0)))
//                                            .fontWeight(.bold)
//                                            .padding(.horizontal, getRelativeWidth(8.0))
//                                            .padding(.vertical, getRelativeHeight(6.0))
//                                            .foregroundColor(ColorConstants.WhiteA700)
//                                            .minimumScaleFactor(0.5)
//                                            .multilineTextAlignment(.center)
//                                            .frame(width: getRelativeWidth(71.0),
//                                                   height: getRelativeHeight(30.0),
//                                                   alignment: .center)
//                                            .background(RoundedCorners(topLeft: 8.0, topRight: 8.0,
//                                                                       bottomLeft: 8.0,
//                                                                       bottomRight: 8.0)
//                                                    .fill(ColorConstants.Blue600))
//                                            .padding(.top, getRelativeHeight(4.0))
//                                            .padding(.bottom, getRelativeHeight(5.0))
//                                            .padding(.leading, getRelativeWidth(126.0))
//                                    }
//                                })
//                                .frame(width: getRelativeWidth(71.0),
//                                       height: getRelativeHeight(30.0), alignment: .center)
//                                .background(RoundedCorners(topLeft: 8.0, topRight: 8.0,
//                                                           bottomLeft: 8.0, bottomRight: 8.0)
//                                        .fill(ColorConstants.Blue600))
//                                .padding(.top, getRelativeHeight(4.0))
//                                .padding(.bottom, getRelativeHeight(5.0))
//                                .padding(.leading, getRelativeWidth(126.0))
//                            }
//                            .frame(width: getRelativeWidth(351.0), height: getRelativeHeight(59.0),
//                                   alignment: .leading)
//                            .overlay(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0,
//                                                    bottomRight: 8.0)
//                                    .stroke(ColorConstants.Black90001,
//                                            lineWidth: 1))
//                            .background(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0,
//                                                       bottomRight: 8.0)
//                                    .fill(ColorConstants.WhiteA700))
//                            .padding(.top, getRelativeHeight(19.0))
//                            HStack {
//                                ZStack {
//                                    Image("img_pin_fill")
//                                        .resizable()
//                                        .frame(width: getRelativeWidth(37.0),
//                                               height: getRelativeWidth(37.0), alignment: .center)
//                                        .scaledToFit()
//                                        .clipped()
//                                }
//                                .hideNavigationBar()
//                                .frame(width: getRelativeWidth(37.0),
//                                       height: getRelativeWidth(37.0), alignment: .top)
//                                .background(RoundedCorners(topLeft: 4.0, topRight: 4.0,
//                                                           bottomLeft: 4.0, bottomRight: 4.0)
//                                        .fill(ColorConstants.WhiteA700))
//                                .shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 0)
//                                .padding(.bottom, getRelativeHeight(17.0))
//                                VStack(alignment: .leading, spacing: 0) {
//                                    Text(StringConstants.kMsgShippingAddress)
//                                        .font(FontScheme
//                                            .kNunitoMedium(size: getRelativeHeight(14.0)))
//                                        .fontWeight(.medium)
//                                        .foregroundColor(ColorConstants.Black90001)
//                                        .minimumScaleFactor(0.5)
//                                        .multilineTextAlignment(.leading)
//                                        .frame(width: getRelativeWidth(118.0),
//                                               height: getRelativeHeight(20.0),
//                                               alignment: .topLeading)
//                                    Text(StringConstants.kMsgSorryThereIs)
//                                        .font(FontScheme
//                                            .kNunitoMedium(size: getRelativeHeight(11.0)))
//                                        .fontWeight(.medium)
//                                        .foregroundColor(ColorConstants.Gray50001)
//                                        .minimumScaleFactor(0.5)
//                                        .multilineTextAlignment(.leading)
//                                        .frame(width: getRelativeWidth(145.0),
//                                               height: getRelativeHeight(32.0),
//                                               alignment: .topLeading)
//                                }
//                                .frame(width: getRelativeWidth(145.0),
//                                       height: getRelativeHeight(53.0), alignment: .center)
//                                .padding(.leading, getRelativeWidth(8.0))
//                                Button(action: {}, label: {
//                                    HStack(spacing: 0) {
//                                        Text(StringConstants.kLblAddAddress)
//                                            .font(FontScheme
//                                                .kNunitoBold(size: getRelativeHeight(12.0)))
//                                            .fontWeight(.bold)
//                                            .padding(.horizontal, getRelativeWidth(8.0))
//                                            .padding(.vertical, getRelativeHeight(6.0))
//                                            .foregroundColor(ColorConstants.WhiteA700)
//                                            .minimumScaleFactor(0.5)
//                                            .multilineTextAlignment(.center)
//                                            .frame(width: getRelativeWidth(96.0),
//                                                   height: getRelativeHeight(30.0),
//                                                   alignment: .center)
//                                            .background(RoundedCorners(topLeft: 8.0, topRight: 8.0,
//                                                                       bottomLeft: 8.0,
//                                                                       bottomRight: 8.0)
//                                                    .fill(ColorConstants.Blue600))
//                                            .padding(.vertical, getRelativeHeight(12.0))
//                                            .padding(.leading, getRelativeWidth(48.0))
//                                    }
//                                })
//                                .frame(width: getRelativeWidth(96.0),
//                                       height: getRelativeHeight(30.0), alignment: .center)
//                                .background(RoundedCorners(topLeft: 8.0, topRight: 8.0,
//                                                           bottomLeft: 8.0, bottomRight: 8.0)
//                                        .fill(ColorConstants.Blue600))
//                                .padding(.vertical, getRelativeHeight(12.0))
//                                .padding(.leading, getRelativeWidth(48.0))
//                            }
//                            .frame(width: getRelativeWidth(351.0), height: getRelativeHeight(76.0),
//                                   alignment: .leading)
//                            .overlay(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0,
//                                                    bottomRight: 8.0)
//                                    .stroke(ColorConstants.Black90001,
//                                            lineWidth: 1))
//                            .background(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0,
//                                                       bottomRight: 8.0)
//                                    .fill(ColorConstants.WhiteA700))
//                            .padding(.top, getRelativeHeight(11.0))
//                            Text(StringConstants.kLblPaymentMethod)
//                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
//                                .fontWeight(.bold)
//                                .foregroundColor(ColorConstants.Black90001)
//                                .minimumScaleFactor(0.5)
//                                .multilineTextAlignment(.leading)
//                                .frame(width: getRelativeWidth(134.0),
//                                       height: getRelativeHeight(22.0), alignment: .topLeading)
//                                .padding(.top, getRelativeHeight(36.0))
//                            HStack {
//                                RadioGroup(items: [StringConstants.kLblKnet.stringify],
//                                           selectedId: $addaddressViewModel.knetRadio,
//                                           selectedColor: ColorConstants.Black90001)
//                                    .frame(width: getRelativeWidth(76.0),
//                                           height: getRelativeHeight(26.0), alignment: .center)
//                                    .overlay(RoundedCorners(topLeft: 13.0, topRight: 13.0,
//                                                            bottomLeft: 13.0, bottomRight: 13.0)
//                                            .stroke(ColorConstants.Black90001,
//                                                    lineWidth: 1))
//                                    .background(RoundedCorners(topLeft: 13.0, topRight: 13.0,
//                                                               bottomLeft: 13.0, bottomRight: 13.0)
//                                            .fill(ColorConstants.WhiteA700))
//                                Image("img_image_4")
//                                    .resizable()
//                                    .frame(width: getRelativeWidth(40.0),
//                                           height: getRelativeHeight(30.0), alignment: .center)
//                                    .scaledToFit()
//                                    .clipped()
//                                    .padding(.leading, getRelativeWidth(213.0))
//                            }
//                            .frame(width: getRelativeWidth(349.0), height: getRelativeHeight(46.0),
//                                   alignment: .leading)
//                            .overlay(RoundedCorners(topLeft: 6.0, topRight: 6.0, bottomLeft: 6.0,
//                                                    bottomRight: 6.0)
//                                    .stroke(ColorConstants.Black90001,
//                                            lineWidth: 1))
//                            .background(RoundedCorners(topLeft: 6.0, topRight: 6.0, bottomLeft: 6.0,
//                                                       bottomRight: 6.0)
//                                    .fill(ColorConstants.WhiteA700))
//                            .padding(.top, getRelativeHeight(14.0))
//                            HStack {
//                                RadioGroup(items: [StringConstants.kLblCreditCard.stringify],
//                                           selectedId: $addaddressViewModel.creditcardRadio,
//                                           selectedColor: ColorConstants.Black90001)
//                                    .frame(width: getRelativeWidth(113.0),
//                                           height: getRelativeHeight(26.0), alignment: .center)
//                                    .overlay(RoundedCorners()
//                                        .stroke(ColorConstants.Black90001, lineWidth: 1))
//                                    .background(RoundedCorners().fill(ColorConstants.WhiteA700))
//                                Image("img_image_2")
//                                    .resizable()
//                                    .frame(width: getRelativeWidth(74.0),
//                                           height: getRelativeHeight(27.0), alignment: .center)
//                                    .scaledToFit()
//                                    .clipped()
//                                    .padding(.leading, getRelativeWidth(142.0))
//                            }
//                            .frame(width: getRelativeWidth(349.0), height: getRelativeHeight(46.0),
//                                   alignment: .leading)
//                            .overlay(RoundedCorners(topLeft: 6.0, topRight: 6.0, bottomLeft: 6.0,
//                                                    bottomRight: 6.0)
//                                    .stroke(ColorConstants.Black90001,
//                                            lineWidth: 1))
//                            .background(RoundedCorners(topLeft: 6.0, topRight: 6.0, bottomLeft: 6.0,
//                                                       bottomRight: 6.0)
//                                    .fill(ColorConstants.WhiteA700))
//                            .padding(.top, getRelativeHeight(10.0))
//                            HStack {
//                                RadioGroup(items: [StringConstants.kMsgCashOnDelivery.stringify],
//                                           selectedId: $addaddressViewModel.cashonRadio,
//                                           selectedColor: ColorConstants.Black90001)
//                                    .frame(width: getRelativeWidth(151.0),
//                                           height: getRelativeHeight(26.0), alignment: .center)
//                                    .overlay(RoundedCorners()
//                                        .stroke(ColorConstants.Black90001, lineWidth: 1))
//                                    .background(RoundedCorners().fill(ColorConstants.WhiteA700))
//                                Image("img_image_3")
//                                    .resizable()
//                                    .frame(width: getRelativeWidth(47.0),
//                                           height: getRelativeHeight(27.0), alignment: .center)
//                                    .scaledToFit()
//                                    .clipped()
//                                    .padding(.leading, getRelativeWidth(131.0))
//                            }
//                            .frame(width: getRelativeWidth(349.0), height: getRelativeHeight(46.0),
//                                   alignment: .leading)
//                            .overlay(RoundedCorners(topLeft: 6.0, topRight: 6.0, bottomLeft: 6.0,
//                                                    bottomRight: 6.0)
//                                    .stroke(ColorConstants.Black90001,
//                                            lineWidth: 1))
//                            .background(RoundedCorners(topLeft: 6.0, topRight: 6.0, bottomLeft: 6.0,
//                                                       bottomRight: 6.0)
//                                    .fill(ColorConstants.WhiteA700))
//                            .padding(.top, getRelativeHeight(10.0))
//                        }
//                        .frame(width: getRelativeWidth(352.0), height: getRelativeHeight(418.0),
//                               alignment: .center)
//                        .padding(.bottom, getRelativeHeight(580.27))
//                        .padding(.horizontal, getRelativeWidth(18.0))
//                        VStack {
//                            VStack {
//                                Group {
//                                    HStack {
//                                        Text(StringConstants.kLblAddNewAddress)
//                                            .font(FontScheme
//                                                .kNunitoBold(size: getRelativeHeight(16.0)))
//                                            .fontWeight(.bold)
//                                            .foregroundColor(ColorConstants.Black90001)
//                                            .minimumScaleFactor(0.5)
//                                            .multilineTextAlignment(.leading)
//                                            .frame(width: getRelativeWidth(140.0),
//                                                   height: getRelativeHeight(22.0),
//                                                   alignment: .topLeading)
//                                        Button(action: {}, label: {
//                                            Image("img_close_round_light")
//                                        })
//                                        .frame(width: getRelativeWidth(24.0),
//                                               height: getRelativeWidth(24.0), alignment: .center)
//                                        .padding(.leading, getRelativeWidth(160.0))
//                                    }
//                                    .frame(width: getRelativeWidth(324.0),
//                                           height: getRelativeHeight(24.0), alignment: .center)
//                                    .padding(.top, getRelativeHeight(4.0))
//                                    .padding(.leading, getRelativeWidth(10.0))
//                                    .padding(.trailing, getRelativeWidth(15.0))
//                                    Divider()
//                                        .frame(width: getRelativeWidth(349.0),
//                                               height: getRelativeHeight(1.0), alignment: .center)
//                                        .background(ColorConstants.Cyan8003f)
//                                        .padding(.top, getRelativeHeight(16.0))
//                                    Text(StringConstants.kLblName)
//                                        .font(FontScheme
//                                            .kNunitoMedium(size: getRelativeHeight(14.0)))
//                                        .fontWeight(.medium)
//                                        .foregroundColor(ColorConstants.Black90001)
//                                        .minimumScaleFactor(0.5)
//                                        .multilineTextAlignment(.leading)
//                                        .frame(width: getRelativeWidth(50.0),
//                                               height: getRelativeHeight(20.0),
//                                               alignment: .topLeading)
//                                        .padding(.top, getRelativeHeight(10.0))
//                                        .padding(.leading, getRelativeWidth(10.0))
//                                    HStack {
//                                        TextField(StringConstants.kLblName2,
//                                                  text: $addaddressViewModel.nameText)
//                                            .font(FontScheme
//                                                .kNunitoMedium(size: getRelativeHeight(12.0)))
//                                            .foregroundColor(ColorConstants.Gray500C4)
//                                            .padding()
//                                    }
//                                    .frame(width: getRelativeWidth(335.0),
//                                           height: getRelativeHeight(53.0), alignment: .center)
//                                    .overlay(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                            bottomLeft: 15.0, bottomRight: 15.0)
//                                            .stroke(ColorConstants.Black90001,
//                                                    lineWidth: 1))
//                                    .background(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                               bottomLeft: 15.0, bottomRight: 15.0)
//                                            .fill(ColorConstants.WhiteA700))
//                                    .padding(.top, getRelativeHeight(4.0))
//                                    .padding(.horizontal, getRelativeWidth(6.0))
//                                    Text(StringConstants.kLblEmail)
//                                        .font(FontScheme
//                                            .kNunitoMedium(size: getRelativeHeight(14.0)))
//                                        .fontWeight(.medium)
//                                        .foregroundColor(ColorConstants.Black90001)
//                                        .minimumScaleFactor(0.5)
//                                        .multilineTextAlignment(.leading)
//                                        .frame(width: getRelativeWidth(48.0),
//                                               height: getRelativeHeight(20.0),
//                                               alignment: .topLeading)
//                                        .padding(.top, getRelativeHeight(14.0))
//                                        .padding(.leading, getRelativeWidth(10.0))
//                                    HStack {
//                                        TextField(StringConstants.kMsgExambleGamilCom,
//                                                  text: $addaddressViewModel.emailText)
//                                            .font(FontScheme
//                                                .kNunitoMedium(size: getRelativeHeight(12.0)))
//                                            .foregroundColor(ColorConstants.Gray500C4)
//                                            .padding()
//                                    }
//                                    .frame(width: getRelativeWidth(335.0),
//                                           height: getRelativeHeight(53.0), alignment: .center)
//                                    .overlay(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                            bottomLeft: 15.0, bottomRight: 15.0)
//                                            .stroke(ColorConstants.Black90001,
//                                                    lineWidth: 1))
//                                    .background(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                               bottomLeft: 15.0, bottomRight: 15.0)
//                                            .fill(ColorConstants.WhiteA700))
//                                    .padding(.top, getRelativeHeight(4.0))
//                                    .padding(.horizontal, getRelativeWidth(6.0))
//                                    Text(StringConstants.kLblPhone)
//                                        .font(FontScheme
//                                            .kNunitoMedium(size: getRelativeHeight(14.0)))
//                                        .fontWeight(.medium)
//                                        .foregroundColor(ColorConstants.Black90001)
//                                        .minimumScaleFactor(0.5)
//                                        .multilineTextAlignment(.leading)
//                                        .frame(width: getRelativeWidth(53.0),
//                                               height: getRelativeHeight(20.0),
//                                               alignment: .topLeading)
//                                        .padding(.top, getRelativeHeight(14.0))
//                                        .padding(.leading, getRelativeWidth(10.0))
//                                    HStack {
//                                        TextField(StringConstants.kLbl96599999999,
//                                                  text: $addaddressViewModel._99valueoneText)
//                                            .font(FontScheme
//                                                .kNunitoMedium(size: getRelativeHeight(12.0)))
//                                            .foregroundColor(ColorConstants.Gray500C4)
//                                            .padding()
//                                    }
//                                    .frame(width: getRelativeWidth(335.0),
//                                           height: getRelativeHeight(53.0), alignment: .center)
//                                    .overlay(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                            bottomLeft: 15.0, bottomRight: 15.0)
//                                            .stroke(ColorConstants.Black90001,
//                                                    lineWidth: 1))
//                                    .background(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                               bottomLeft: 15.0, bottomRight: 15.0)
//                                            .fill(ColorConstants.WhiteA700))
//                                    .padding(.top, getRelativeHeight(4.0))
//                                    .padding(.horizontal, getRelativeWidth(6.0))
//                                }
//                                Group {
//                                    Text(StringConstants.kLblArea)
//                                        .font(FontScheme
//                                            .kNunitoMedium(size: getRelativeHeight(14.0)))
//                                        .fontWeight(.medium)
//                                        .foregroundColor(ColorConstants.Black90001)
//                                        .minimumScaleFactor(0.5)
//                                        .multilineTextAlignment(.leading)
//                                        .frame(width: getRelativeWidth(43.0),
//                                               height: getRelativeHeight(20.0),
//                                               alignment: .topLeading)
//                                        .padding(.top, getRelativeHeight(15.0))
//                                        .padding(.leading, getRelativeWidth(10.0))
//                                    Picker(StringConstants.kLblSelectArea,
//                                           selection: $addaddressViewModel.selectareaPicker1) {
//                                        ForEach(addaddressViewModel.selectareaPicker1Values,
//                                                id: \.self) { value in
//                                            Text(value)
//                                        }
//                                    }
//                                    .foregroundColor(ColorConstants.Gray500C4)
//                                    .font(.system(size: getRelativeHeight(12)))
//                                    .pickerStyle(MenuPickerStyle())
//                                    Text(StringConstants.kLblBlock)
//                                        .font(FontScheme
//                                            .kNunitoMedium(size: getRelativeHeight(14.0)))
//                                        .fontWeight(.medium)
//                                        .foregroundColor(ColorConstants.Black90001)
//                                        .minimumScaleFactor(0.5)
//                                        .multilineTextAlignment(.leading)
//                                        .frame(width: getRelativeWidth(48.0),
//                                               height: getRelativeHeight(20.0),
//                                               alignment: .topLeading)
//                                        .padding(.top, getRelativeHeight(20.0))
//                                        .padding(.leading, getRelativeWidth(10.0))
//                                    HStack {
//                                        TextField(StringConstants.kLblBlock2,
//                                                  text: $addaddressViewModel.blockvalueoneText)
//                                            .font(FontScheme
//                                                .kNunitoMedium(size: getRelativeHeight(12.0)))
//                                            .foregroundColor(ColorConstants.Gray500C4)
//                                            .padding()
//                                    }
//                                    .frame(width: getRelativeWidth(335.0),
//                                           height: getRelativeHeight(53.0), alignment: .center)
//                                    .overlay(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                            bottomLeft: 15.0, bottomRight: 15.0)
//                                            .stroke(ColorConstants.Black90001,
//                                                    lineWidth: 1))
//                                    .background(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                               bottomLeft: 15.0, bottomRight: 15.0)
//                                            .fill(ColorConstants.WhiteA700))
//                                    .padding(.top, getRelativeHeight(4.0))
//                                    .padding(.horizontal, getRelativeWidth(6.0))
//                                    Text(StringConstants.kLblStreetName)
//                                        .font(FontScheme
//                                            .kNunitoMedium(size: getRelativeHeight(14.0)))
//                                        .fontWeight(.medium)
//                                        .foregroundColor(ColorConstants.Black90001)
//                                        .minimumScaleFactor(0.5)
//                                        .multilineTextAlignment(.leading)
//                                        .frame(width: getRelativeWidth(96.0),
//                                               height: getRelativeHeight(20.0),
//                                               alignment: .topLeading)
//                                        .padding(.top, getRelativeHeight(15.0))
//                                        .padding(.leading, getRelativeWidth(10.0))
//                                    HStack {
//                                        TextField(StringConstants.kLblStreetName2,
//                                                  text: $addaddressViewModel.nameText1)
//                                            .font(FontScheme
//                                                .kNunitoMedium(size: getRelativeHeight(12.0)))
//                                            .foregroundColor(ColorConstants.Gray500C4)
//                                            .padding()
//                                    }
//                                    .frame(width: getRelativeWidth(335.0),
//                                           height: getRelativeHeight(53.0), alignment: .center)
//                                    .overlay(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                            bottomLeft: 15.0, bottomRight: 15.0)
//                                            .stroke(ColorConstants.Black90001,
//                                                    lineWidth: 1))
//                                    .background(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                               bottomLeft: 15.0, bottomRight: 15.0)
//                                            .fill(ColorConstants.WhiteA700))
//                                    .padding(.top, getRelativeHeight(4.0))
//                                    .padding(.horizontal, getRelativeWidth(6.0))
//                                    Text(StringConstants.kLblBuilding)
//                                        .font(FontScheme
//                                            .kNunitoMedium(size: getRelativeHeight(14.0)))
//                                        .fontWeight(.medium)
//                                        .foregroundColor(ColorConstants.Black90001)
//                                        .minimumScaleFactor(0.5)
//                                        .multilineTextAlignment(.leading)
//                                        .frame(width: getRelativeWidth(67.0),
//                                               height: getRelativeHeight(20.0),
//                                               alignment: .topLeading)
//                                        .padding(.top, getRelativeHeight(18.0))
//                                        .padding(.leading, getRelativeWidth(10.0))
//                                    HStack {
//                                        TextField(StringConstants.kLblBuilding2,
//                                                  text: $addaddressViewModel.buildingvalueText)
//                                            .font(FontScheme
//                                                .kNunitoMedium(size: getRelativeHeight(12.0)))
//                                            .foregroundColor(ColorConstants.Gray500C4)
//                                            .padding()
//                                    }
//                                    .frame(width: getRelativeWidth(335.0),
//                                           height: getRelativeHeight(53.0), alignment: .center)
//                                    .overlay(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                            bottomLeft: 15.0, bottomRight: 15.0)
//                                            .stroke(ColorConstants.Black90001,
//                                                    lineWidth: 1))
//                                    .background(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                               bottomLeft: 15.0, bottomRight: 15.0)
//                                            .fill(ColorConstants.WhiteA700))
//                                    .padding(.horizontal, getRelativeWidth(6.0))
//                                }
//                                Group {
//                                    VStack(alignment: .leading, spacing: 0) {
//                                        Text(StringConstants.kLblAvenue)
//                                            .font(FontScheme
//                                                .kNunitoMedium(size: getRelativeHeight(14.0)))
//                                            .fontWeight(.medium)
//                                            .foregroundColor(ColorConstants.Black90001)
//                                            .minimumScaleFactor(0.5)
//                                            .multilineTextAlignment(.leading)
//                                            .frame(width: getRelativeWidth(50.0),
//                                                   height: getRelativeHeight(20.0),
//                                                   alignment: .topLeading)
//                                        HStack {
//                                            TextField(StringConstants.kLblAvenue,
//                                                      text: $addaddressViewModel.avenuevalueoneText)
//                                                .font(FontScheme
//                                                    .kNunitoMedium(size: getRelativeHeight(12.0)))
//                                                .foregroundColor(ColorConstants.Gray500C4)
//                                                .padding()
//                                        }
//                                        .frame(width: getRelativeWidth(335.0),
//                                               height: getRelativeHeight(53.0), alignment: .leading)
//                                        .overlay(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                                bottomLeft: 15.0, bottomRight: 15.0)
//                                                .stroke(ColorConstants.Black90001,
//                                                        lineWidth: 1))
//                                        .background(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                                   bottomLeft: 15.0,
//                                                                   bottomRight: 15.0)
//                                                .fill(ColorConstants.WhiteA700))
//                                    }
//                                    .frame(width: getRelativeWidth(349.0),
//                                           height: getRelativeHeight(76.0), alignment: .center)
//                                    .padding(.top, getRelativeHeight(13.0))
//                                    VStack(alignment: .leading, spacing: 0) {
//                                        Text(StringConstants.kLblNotes)
//                                            .font(FontScheme
//                                                .kNunitoMedium(size: getRelativeHeight(14.0)))
//                                            .fontWeight(.medium)
//                                            .foregroundColor(ColorConstants.Black90001)
//                                            .minimumScaleFactor(0.5)
//                                            .multilineTextAlignment(.leading)
//                                            .frame(width: getRelativeWidth(39.0),
//                                                   height: getRelativeHeight(20.0),
//                                                   alignment: .topLeading)
//                                            .padding(.leading, getRelativeWidth(4.0))
//                                        HStack {
//                                            TextField(StringConstants.kLblNotes,
//                                                      text: $addaddressViewModel.notesvalueoneText)
//                                                .font(FontScheme
//                                                    .kNunitoMedium(size: getRelativeHeight(12.0)))
//                                                .foregroundColor(ColorConstants.Gray500C4)
//                                                .padding()
//                                        }
//                                        .frame(width: getRelativeWidth(335.0),
//                                               height: getRelativeHeight(88.0), alignment: .leading)
//                                        .overlay(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                                bottomLeft: 15.0, bottomRight: 15.0)
//                                                .stroke(ColorConstants.Black90001,
//                                                        lineWidth: 1))
//                                        .background(RoundedCorners(topLeft: 15.0, topRight: 15.0,
//                                                                   bottomLeft: 15.0,
//                                                                   bottomRight: 15.0)
//                                                .fill(ColorConstants.WhiteA700))
//                                    }
//                                    .frame(width: getRelativeWidth(349.0),
//                                           height: getRelativeHeight(111.0), alignment: .center)
//                                    .padding(.top, getRelativeHeight(15.0))
//                                    Button(action: {}, label: {
//                                        HStack(spacing: 0) {
//                                            Text(StringConstants.kLblAddAddress2)
//                                                .font(FontScheme
//                                                    .kNunitoBold(size: getRelativeHeight(18.0)))
//                                                .fontWeight(.bold)
//                                                .padding(.horizontal, getRelativeWidth(30.0))
//                                                .padding(.vertical, getRelativeHeight(11.0))
//                                                .foregroundColor(ColorConstants.WhiteA700)
//                                                .minimumScaleFactor(0.5)
//                                                .multilineTextAlignment(.leading)
//                                                .frame(width: getRelativeWidth(306.0),
//                                                       height: getRelativeHeight(47.0),
//                                                       alignment: .topLeading)
//                                                .background(RoundedCorners(topLeft: 8.0,
//                                                                           topRight: 8.0,
//                                                                           bottomLeft: 8.0,
//                                                                           bottomRight: 8.0)
//                                                        .fill(ColorConstants.Blue300))
//                                                .shadow(color: ColorConstants.Black9003f, radius: 4,
//                                                        x: 0, y: 0)
//                                                .padding(.top, getRelativeHeight(15.0))
//                                                .padding(.horizontal, getRelativeWidth(23.0))
//                                        }
//                                    })
//                                    .frame(width: getRelativeWidth(306.0),
//                                           height: getRelativeHeight(47.0), alignment: .topLeading)
//                                    .background(RoundedCorners(topLeft: 8.0, topRight: 8.0,
//                                                               bottomLeft: 8.0, bottomRight: 8.0)
//                                            .fill(ColorConstants.Blue300))
//                                    .shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 0)
//                                    .padding(.top, getRelativeHeight(15.0))
//                                    .padding(.horizontal, getRelativeWidth(23.0))
//                                }
//                            }
//                            .frame(width: getRelativeWidth(356.0),
//                                   height: UIScreen.main.bounds.height, alignment: .topLeading)
//                            .background(RoundedCorners(topLeft: 28.0, topRight: 28.0,
//                                                       bottomLeft: 28.0, bottomRight: 28.0)
//                                    .fill(ColorConstants.WhiteA700))
//                            .shadow(color: ColorConstants.Black9003f, radius: 7, x: 0, y: -7)
//                            .padding(.trailing, getRelativeWidth(6.0))
//                        }
//                        .frame(width: UIScreen.main.bounds.width,
//                               height: UIScreen.main.bounds.height,
//                               alignment: .topLeading)
//                        .background(ColorConstants.Black9005b)
//                        VStack {
//                            Text(StringConstants.kLblOrderInfo)
//                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
//                                .fontWeight(.bold)
//                                .foregroundColor(ColorConstants.Black90001)
//                                .minimumScaleFactor(0.5)
//                                .multilineTextAlignment(.leading)
//                                .frame(width: getRelativeWidth(81.0),
//                                       height: getRelativeHeight(22.0), alignment: .topLeading)
//                                .padding(.leading, getRelativeWidth(25.0))
//                            HStack {
//                                Text(StringConstants.kLblSubtotal)
//                                    .font(FontScheme
//                                        .kRobotoRomanRegular(size: getRelativeHeight(14.0)))
//                                    .fontWeight(.regular)
//                                    .foregroundColor(ColorConstants.Black90001)
//                                    .minimumScaleFactor(0.5)
//                                    .multilineTextAlignment(.leading)
//                                    .frame(width: getRelativeWidth(56.0),
//                                           height: getRelativeHeight(17.0), alignment: .topLeading)
//                                Spacer()
//                                Text(StringConstants.kLblKd145000)
//                                    .font(FontScheme.kInterMedium(size: getRelativeHeight(15.0)))
//                                    .fontWeight(.medium)
//                                    .foregroundColor(ColorConstants.Blue300)
//                                    .minimumScaleFactor(0.5)
//                                    .multilineTextAlignment(.leading)
//                                    .frame(width: getRelativeWidth(84.0),
//                                           height: getRelativeHeight(19.0), alignment: .topLeading)
//                                    .padding(.leading, getRelativeWidth(195.0))
//                            }
//                            .frame(width: getRelativeWidth(335.0), height: getRelativeHeight(19.0),
//                                   alignment: .center)
//                            .padding(.top, getRelativeHeight(19.0))
//                            .padding(.leading, getRelativeWidth(25.0))
//                            .padding(.trailing, getRelativeWidth(30.0))
//                            HStack {
//                                Text(StringConstants.kLblDiscount)
//                                    .font(FontScheme
//                                        .kRobotoRomanRegular(size: getRelativeHeight(14.0)))
//                                    .fontWeight(.regular)
//                                    .foregroundColor(ColorConstants.Black90001)
//                                    .minimumScaleFactor(0.5)
//                                    .multilineTextAlignment(.leading)
//                                    .frame(width: getRelativeWidth(59.0),
//                                           height: getRelativeHeight(17.0), alignment: .topLeading)
//                                Spacer()
//                                Text(StringConstants.kLblKd05000)
//                                    .font(FontScheme.kInterMedium(size: getRelativeHeight(15.0)))
//                                    .fontWeight(.medium)
//                                    .foregroundColor(ColorConstants.Blue300)
//                                    .minimumScaleFactor(0.5)
//                                    .multilineTextAlignment(.leading)
//                                    .frame(width: getRelativeWidth(77.0),
//                                           height: getRelativeHeight(19.0), alignment: .topLeading)
//                                    .padding(.leading, getRelativeWidth(194.0))
//                            }
//                            .frame(width: getRelativeWidth(330.0), height: getRelativeHeight(19.0),
//                                   alignment: .center)
//                            .padding(.top, getRelativeHeight(7.0))
//                            .padding(.leading, getRelativeWidth(25.0))
//                            .padding(.trailing, getRelativeWidth(35.0))
//                            HStack {
//                                Text(StringConstants.kLblDelivery)
//                                    .font(FontScheme
//                                        .kRobotoRomanRegular(size: getRelativeHeight(14.0)))
//                                    .fontWeight(.regular)
//                                    .foregroundColor(ColorConstants.Black90001)
//                                    .minimumScaleFactor(0.5)
//                                    .multilineTextAlignment(.leading)
//                                    .frame(width: getRelativeWidth(53.0),
//                                           height: getRelativeHeight(17.0), alignment: .topLeading)
//                                Spacer()
//                                Text(StringConstants.kLblFree)
//                                    .font(FontScheme.kInterMedium(size: getRelativeHeight(15.0)))
//                                    .fontWeight(.medium)
//                                    .foregroundColor(ColorConstants.Blue300)
//                                    .minimumScaleFactor(0.5)
//                                    .multilineTextAlignment(.leading)
//                                    .frame(width: getRelativeWidth(32.0),
//                                           height: getRelativeHeight(19.0), alignment: .topLeading)
//                                    .padding(.leading, getRelativeWidth(247.0))
//                            }
//                            .frame(width: getRelativeWidth(332.0), height: getRelativeHeight(20.0),
//                                   alignment: .center)
//                            .padding(.top, getRelativeHeight(11.0))
//                            .padding(.leading, getRelativeWidth(25.0))
//                            .padding(.trailing, getRelativeWidth(33.0))
//                            Divider()
//                                .frame(width: UIScreen.main.bounds.width,
//                                       height: getRelativeHeight(1.0), alignment: .center)
//                                .background(ColorConstants.Teal90026)
//                                .padding(.top, getRelativeHeight(9.0))
//                            HStack {
//                                Text(StringConstants.kLblTotal)
//                                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
//                                    .fontWeight(.bold)
//                                    .foregroundColor(ColorConstants.Black90001)
//                                    .minimumScaleFactor(0.5)
//                                    .multilineTextAlignment(.leading)
//                                    .frame(width: getRelativeWidth(40.0),
//                                           height: getRelativeHeight(22.0), alignment: .topLeading)
//                                Spacer()
//                                Text(StringConstants.kLblKd1400000)
//                                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
//                                    .fontWeight(.bold)
//                                    .foregroundColor(ColorConstants.Blue300)
//                                    .minimumScaleFactor(0.5)
//                                    .multilineTextAlignment(.leading)
//                                    .frame(width: getRelativeWidth(104.0),
//                                           height: getRelativeHeight(22.0), alignment: .topLeading)
//                                    .padding(.leading, getRelativeWidth(196.0))
//                            }
//                            .frame(width: getRelativeWidth(340.0), height: getRelativeHeight(22.0),
//                                   alignment: .center)
//                            .padding(.top, getRelativeHeight(9.0))
//                            .padding(.horizontal, getRelativeWidth(24.0))
//                            Button(action: {}, label: {
//                                HStack(spacing: 0) {
//                                    Text(StringConstants.kMsgProceedToCheckout)
//                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
//                                        .fontWeight(.bold)
//                                        .padding(.horizontal, getRelativeWidth(30.0))
//                                        .padding(.vertical, getRelativeHeight(14.0))
//                                        .foregroundColor(ColorConstants.WhiteA700)
//                                        .minimumScaleFactor(0.5)
//                                        .multilineTextAlignment(.center)
//                                        .frame(width: getRelativeWidth(348.0),
//                                               height: getRelativeHeight(54.0), alignment: .center)
//                                        .background(RoundedCorners(topLeft: 8.0, topRight: 8.0,
//                                                                   bottomLeft: 8.0,
//                                                                   bottomRight: 8.0)
//                                                .fill(ColorConstants.Blue300))
//                                        .shadow(color: ColorConstants.Black9003f, radius: 4, x: 0,
//                                                y: 0)
//                                        .padding(.vertical, getRelativeHeight(28.0))
//                                        .padding(.horizontal, getRelativeWidth(22.0))
//                                }
//                            })
//                            .frame(width: getRelativeWidth(348.0), height: getRelativeHeight(54.0),
//                                   alignment: .center)
//                            .background(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0,
//                                                       bottomRight: 8.0)
//                                    .fill(ColorConstants.Blue300))
//                            .shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 0)
//                            .padding(.vertical, getRelativeHeight(28.0))
//                            .padding(.horizontal, getRelativeWidth(22.0))
//                        }
//                        .frame(width: UIScreen.main.bounds.width, height: getRelativeHeight(283.0),
//                               alignment: .center)
//                        .background(RoundedCorners(topLeft: 23.0, topRight: 23.0)
//                            .fill(ColorConstants.WhiteA700))
//                        .shadow(color: ColorConstants.Black90021, radius: 7, x: 3, y: -10)
//                        .padding(.top, getRelativeHeight(544.0))
//                    }
//                    .hideNavigationBar()
//                    .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height,
//                           alignment: .topLeading)
//                }
//            }
//            .frame(width: UIScreen.main.bounds.width, alignment: .topLeading)
//            .background(ColorConstants.WhiteA700)
//            .padding(.top, getRelativeHeight(30.0))
//            .padding(.bottom, getRelativeHeight(10.0))
//        }
//        .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height)
//        .background(ColorConstants.WhiteA700)
//        .ignoresSafeArea()
//        .hideNavigationBar()
//    }
//}
//
//struct AddaddressView_Previews: PreviewProvider {
//    static var previews: some View {
//        AddaddressView()
//    }
//}
