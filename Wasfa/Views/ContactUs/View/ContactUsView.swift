//
//  ContactUsView.swift
//  Wasfa
//
//  Created by Apple on 18/02/2025.
//

import SwiftUI

struct ContactUsView: View {
    @StateObject private var viewModel: ContactUsViewModel = .init()
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            MainScrollBody(backButtonWithTitle: "Contact Us") {
                if let contactPageModel = viewModel.contactPageModel {
                    VStack(spacing: 16) {
                        // Contact Details
                        VStack(spacing: 12) {
                            ContactItem(icon: .contactUsLocation, title: "Location", description: contactPageModel.contactAddress ?? "") { ContactHelper.openLink(urlString: contactPageModel.map ?? "")}
                            ContactItem(icon: .contactUsCall, title: "Call Us", description: contactPageModel.contactPhone ?? "") { ContactHelper.dialPhone(number: contactPageModel.contactPhone ?? "") }
                            ContactItem(icon: .contactUsEmail, title: "Email Us", description:  contactPageModel.contactEmail ?? "") { ContactHelper.sendEmail(to: contactPageModel.contactEmail ?? "") }
                        }
                        
                        // Social Media Section
                        VStack(alignment: .leading, spacing: 12) {
                            Text("Social Media")
                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(16)))
                                .fontWeight(.bold)
                                .padding(.top, 10)
                            
                            SocialMediaItem(image: .contactUsInstagram, title: contactPageModel.instagramLink ?? "", platform: "Instagram"){ ContactHelper.openLink(urlString: contactPageModel.instagramLink ?? "")}
                            SocialMediaItem(image: .contactUsMeta, title: contactPageModel.facebookLink ?? "", platform: "Facebook"){ ContactHelper.openLink(urlString: contactPageModel.facebookLink ?? "")}
                        }
                        .padding()
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .background(Color(red: 0.9, green: 0.96, blue: 1)) // Light blue background
                        .cornerRadius(12)
                        .padding(.horizontal)
                        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 3)
                        Spacer()
                    }
                }
            }
            .padding(.vertical, 32)
        }
    }
}

// MARK: - Contact Item Component

struct ContactItem: View {
    var icon: ImageResource
    var title: String
    var description: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Image(icon)
                    .foregroundColor(.black)
                
                VStack(alignment: .leading) {
                    Text(title)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(16)))
                        .fontWeight(.bold)
                        .foregroundStyle(Color.black)
                    Text(description)
                       
                        .font(FontScheme.kRobotoRomanRegular(size: getRelativeHeight(12.0)))
                        .fontWeight(.regular)
                        .foregroundStyle(Color(hex: "#363535"))
                }
                Spacer()
            }
            .padding()
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(Color(red: 0.9, green: 0.96, blue: 1)) // Light blue background
            .cornerRadius(12)
            .padding(.horizontal)
            .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 3)
        }
    }
}

// MARK: - Social Media Item Component

struct SocialMediaItem: View {
    var image: ImageResource
    var title: String
    var platform: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Circle()
                    .fill(Color.white)
                    .frame(width: 40, height: 40)
                    .overlay(
                        Image(image)
                            .resizable()
                            .scaledToFit()
                            .frame(width: 24, height: 24)
                    )

                VStack(alignment: .leading) {
                    Text(title)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(12)))
                        .fontWeight(.bold)
                    Text(platform)
                        .font(FontScheme.kRobotoRomanRegular(size: getRelativeHeight(12.0)))
                        .foregroundColor(.gray)
                }
                .foregroundStyle(Color.black)
                Spacer()
            }
        }
    }
}

// MARK: - Preview

struct ContactUsView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationStack {
            ContactUsView().attachAllEnvironmentObjects()
        }
    }
}


import SwiftUI

struct ContactHelper {
    
    // Open Apple Maps with a given address
    static func openMap(address: String) {
        let encodedAddress = address.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        if let url = URL(string: "http://maps.apple.com/?q=\(encodedAddress)") {
            UIApplication.shared.open(url)
        }
    }
    
    // Open Phone Dialer with a given number
    static func dialPhone(number: String) {
        let number = number.replacingOccurrences(of: " ", with: "")
        if let phoneURL = URL(string: "tel://\(number)"), UIApplication.shared.canOpenURL(phoneURL) {
            UIApplication.shared.open(phoneURL)
        }
    }
    
    // Open Email Client with a given email address
    static func sendEmail(to email: String) {
        if let emailURL = URL(string: "mailto:\(email)"), UIApplication.shared.canOpenURL(emailURL) {
            UIApplication.shared.open(emailURL)
        }
    }

    // Open Any URL in Browser
    static func openLink(urlString: String) {
        if let url = URL(string: urlString), UIApplication.shared.canOpenURL(url) {
            UIApplication.shared.open(url)
        }
    }
}
