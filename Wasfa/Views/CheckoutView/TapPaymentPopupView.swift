//
//  TapPaymentPopupView.swift
//  Wasfa
//
//  Created by Augment Agent on 29/05/2025.
//

import SwiftUI
import Foundation

struct TapPaymentPopupView: View {
    @ObservedObject var viewModel: CheckoutViewModel
    

    var body: some View {
        VStack(spacing: 24) {
            // Header
            VStack(spacing: 16) {
                // Handle bar
                RoundedRectangle(cornerRadius: 2.5)
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 40, height: 5)

                // Title
                Text("Tap Payments")
                    .font(.custom("Poppins", size: 24))
                    .fontWeight(.bold)
                    .foregroundColor(Color(hex: "#242424"))

                // Subtitle
                Text("Secure payment with KNET and Credit Cards")
                    .font(.custom("Poppins", size: 16))
                    .foregroundColor(Color(hex: "#808080"))
                    .multilineTextAlignment(.center)
            }
            .padding(.top, 20)

            // Payment Methods Icons
            HStack(spacing: 16) {
                PaymentMethodIcon(name: "KNET", color: .blue)
                PaymentMethodIcon(name: "VISA", color: .red)
                PaymentMethodIcon(name: "AMEX", color: .green)
            }
            .padding(.horizontal)

            // Order Summary
            if let checkoutDetails = viewModel.checkoutDetailsModel {
                VStack(alignment: .leading, spacing: 12) {
                    Text("Order Summary")
                        .font(.custom("Poppins", size: 18))
                        .fontWeight(.semibold)
                        .foregroundColor(Color(hex: "#242424"))

                    HStack {
                        Text("Total Amount:")
                            .font(.custom("Poppins", size: 16))
                            .foregroundColor(Color(hex: "#808080"))
                        Spacer()
                        Text(checkoutDetails.grandTotal)
                            .font(.custom("Poppins", size: 20))
                            .fontWeight(.bold)
                            .foregroundColor(Color(hex: "#242424"))
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(hex: "#F8F9FA"))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color(hex: "#E9ECEF"), lineWidth: 1)
                        )
                )
            }

            // Error Message
            if let error = viewModel.tapPaymentError {
                HStack {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.red)
                    Text(error)
                        .font(.custom("Poppins", size: 14))
                        .foregroundColor(.red)
                        .multilineTextAlignment(.leading)
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.red.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.red.opacity(0.3), lineWidth: 1)
                        )
                )
          
            }

            // Action Buttons
            VStack(spacing: 12) {
                // Pay Now Button
                Button(action: {
                    // Clear any previous error before attempting payment
                    viewModel.tapPaymentError = nil
                    print("🚨 TapPaymentPopupView: Cleared previous error, showing Tap checkout")

                        viewModel.showingTapCheckout = true
                }) {
                    HStack {
                        if viewModel.isProcessingTapPayment {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(0.8)
                        }

                        Text(viewModel.isProcessingTapPayment ? "Processing..." : "Pay Now")
                            .font(.custom("Poppins", size: 18))
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                    }
                    .frame(maxWidth: .infinity)
                    .frame(height: 56)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(hex: "#007AFF"))
                    )
                }
                .disabled(viewModel.isProcessingTapPayment)

                // Cancel Button (or Close button when there's an error)
                Button(action: {
                    // Clear error when closing
                    viewModel.tapPaymentError = nil
                    viewModel.updatePopUpType(nil)
                }) {
                    Text(viewModel.tapPaymentError != nil ? "Close" : "Cancel")
                        .font(.custom("Poppins", size: 16))
                        .fontWeight(.medium)
                        .foregroundColor(Color(hex: "#808080"))
                        .frame(maxWidth: .infinity)
                        .frame(height: 44)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color(hex: "#E9ECEF"), lineWidth: 1)
                        )
                }
                .disabled(viewModel.isProcessingTapPayment)
            }

            Spacer()
        }
        .padding(.horizontal, 24)
        .padding(.bottom, 32)
        .frame(height: 500)
        .background(
            RoundedCorners(topLeft: 28, topRight: 28)
                .fill(Color.white)
        )
        .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: -5)
        .onDisappear {
            // Clear error when popup disappears
            viewModel.tapPaymentError = nil
            print("🚨 TapPaymentPopupView: Popup disappeared, cleared error")
        }
        .sheet(isPresented: $viewModel.showingTapCheckout) {
            if let checkoutDetails = viewModel.checkoutDetailsModel,
               let addressModel = viewModel.addressModelRequest {

                let paymentRequest = TapPaymentRequest(
                    amount: checkoutDetails.grandTotalValue,
                    currency: "KWD",
                    customerEmail: addressModel.email ?? "",
                    customerName: addressModel.fullName,
                    customerPhone: addressModel.phone,
                    items: checkoutDetails.cartItems.map { item in
                        TapPaymentItem(
                            title: item.productName,
                            description: "Product from Wasfa",
                            price: item.price,
                            quantity: item.quantity
                        )
                    },
                    description: "Order from Wasfa App"
                )

                TapCheckoutView(paymentRequest: paymentRequest) { result in
                    print("🚨 TapPaymentPopupView: TapCheckoutView result callback called")
                    print("🚨 TapPaymentPopupView: Result: \(result)")

                    switch result {
                    case .success(let transactionId, let amount, let currency):
                        print("🚨 TapPaymentPopupView: Payment success - Transaction ID: \(transactionId)")
                        viewModel.showingTapCheckout = false // ✅ Dismiss sheet on success
                        Task {
                            await viewModel.handleSuccessfulTapPayment(
                                transactionId: transactionId,
                                amount: amount,
                                currency: currency
                            )
                        }
                    case .failure(let error):
                        print("🚨 TapPaymentPopupView: Payment failure - Error: \(error)")
                        print("🚨 TapPaymentPopupView: Calling viewModel.handleTapPaymentError")
                        viewModel.showingTapCheckout = false // ✅ Dismiss Tap SDK sheet first

                        // Handle error and keep the popup open to show error message
                        viewModel.handleTapPaymentError(error)
                        print("🚨 TapPaymentPopupView: Called viewModel.handleTapPaymentError")
                        print("🚨 TapPaymentPopupView: Keeping popup open to display error message")
                    case .cancelled:
                        print("🚨 TapPaymentPopupView: Payment cancelled")
                        viewModel.showingTapCheckout = false // ✅ Dismiss sheet on cancellation
                        // User cancelled, do nothing
                        break
                    }
                }
            }
        }
    }
}

struct PaymentMethodIcon: View {
    let name: String
    let color: Color

    var body: some View {
        VStack(spacing: 4) {
            RoundedRectangle(cornerRadius: 8)
                .fill(color.opacity(0.1))
                .overlay(
                    Text(name)
                        .font(.custom("Poppins", size: 12))
                        .fontWeight(.bold)
                        .foregroundColor(color)
                )
                .frame(width: 60, height: 40)
        }
    }
}

#Preview {
//    TapPaymentPopupView(viewModel: CheckoutViewModel(CartModel(from: <#any Decoder#>)))
}
