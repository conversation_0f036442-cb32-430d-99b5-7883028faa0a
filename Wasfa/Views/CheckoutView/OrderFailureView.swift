//
//  OrderFailureView.swift
//  Wasfa
//
//  Created by Apple on 31/05/2025.
//

import SwiftUI

struct OrderFailureView: View {
    let message:String
    @EnvironmentObject private var appState: AppState
    @Environment(\.routerManager) private var routerManager: RouterManager
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        VStack {
            VStack {
               Image(systemName: "creditcard.trianglebadge.exclamationmark.fill")
                    .resizable()
                    .scaledToFit()
                    .frame(width: getRelativeWidth(152.0), height: getRelativeWidth(152.0),
                           alignment: .center)
                    .frame(maxWidth: .infinity)
                Text("Payment Failed")
                    .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(24.0)))
                    .fontWeight(.heavy)
                    .foregroundColor(ColorConstants.Black90001)
                   
                    .padding(.top, getRelativeHeight(22.0))
                Text(message)
                    .font(FontScheme.kRobotoRomanRegular(size: getRelativeHeight(14.0)))
                    .fontWeight(.regular)
                    .foregroundColor(ColorConstants.Black90001)
                    .multilineTextAlignment(.center)
                    .padding([.top, .horizontal], getRelativeHeight(18.0))
                VStack{
                    Button(action: {
                        let routesType: RoutesType = routerManager.mapRouterWithTab(appState: appState)
                        routerManager.popToRoot(where: routesType)

                    }, label: {
                        HStack(spacing: 0) {
                            Text("Back to Home")
                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
                                .fontWeight(.bold)
                                .padding(.horizontal, getRelativeWidth(30.0))
                                .padding(.vertical, getRelativeHeight(14.0))
                                .foregroundColor(ColorConstants.WhiteA700)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.center)
                                .frame(width: getRelativeWidth(348.0), height: getRelativeHeight(54.0),
                                       alignment: .center)
                                .background(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0,
                                                           bottomRight: 8.0)
                                        .fill(ColorConstants.Blue300))
                                .shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 0)
                                .padding(.vertical, getRelativeHeight(25.0))
                        }
                    })
                    
                    Button(action: {
                        dismiss()
                    }) {
                        Text("Back to Checkout")
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
                            .fontWeight(.bold)
                            .foregroundColor(Color(hex: "#808080"))
                            .frame(width: getRelativeWidth(348.0), height: getRelativeHeight(54.0),
                                   alignment: .center)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color(hex: "#E9ECEF"), lineWidth: 1)
                            )
                    }
                }
            }

            .background(ColorConstants.WhiteA700)
            .padding(.top, getRelativeHeight(30.0))
            .padding(.bottom, getRelativeHeight(10.0))
        }
        
        .background(ColorConstants.WhiteA700)
        .hideNavigationBar()
    }
}


#Preview {
    NavigationStack {
        OrderFailureView(message: "Your order has failed")
    }
}
