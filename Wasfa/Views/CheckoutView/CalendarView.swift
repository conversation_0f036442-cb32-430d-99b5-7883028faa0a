//
//  CalendarView.swift
//  Wasfa
//
//  Created by Apple on 26/11/2024.
//

import SwiftUI

struct CalendarSelectionView: View {
    private let calendar: Calendar = .init(identifier: .gregorian)
    @Binding private var disabledDates: [String]
    private let onClose: (() -> Void)?
    private let onMonth: () -> Void
    private let monthFormatter: DateFormatter
    private let dayFormatter: DateFormatter
    private let weekDayFormatter: DateFormatter
    private let fullFormatter: DateFormatter

    @Binding var selectedDate: Date

    @Namespace private var animation
    init(selectedDate: Binding<Date>, disabledDates: Binding<[String]>, onMonth: @escaping (() -> Void), onClose: (() -> Void)? = nil) {
        self._selectedDate = selectedDate
        self._disabledDates = disabledDates
        self.onClose = onClose
        self.onMonth = onMonth
        self.monthFormatter = DateFormatter(dateFormat: "MMMM YYYY", calendar: calendar)
        self.dayFormatter = DateFormatter(dateFormat: "d", calendar: calendar)
        self.weekDayFormatter = DateFormatter(dateFormat: "EEE", calendar: calendar)
        self.fullFormatter = DateFormatter(dateFormat: "MMMM dd, yyyy", calendar: calendar)
    }

    func isDisabledDate(_ date: Date) -> Bool {
        let dateString = date.toString(outputFormate: "yyyy-MM-d")
        let isContain = disabledDates.contains(dateString)
        return date.isPastDate || isContain
    }

    var body: some View {
        VStack {
            CalendarView(
                calendar: calendar,
                date: $selectedDate,
                content: { date in
                    Button(action: { withAnimation(.bouncy) {
                        selectedDate = date
                    }}) {
                        Text(dayFormatter.string(from: date))
                            .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                            .fontWeight(.regular)
                            .padding(.horizontal, getRelativeWidth(11.0))
                            .padding(.vertical, getRelativeHeight(6.0))
                            .foregroundColor(isDisabledDate(date) ? Color(red: 0.8, green: 0.8, blue: 0.8) : calendar.isDate(date, inSameDayAs: selectedDate) ? ColorConstants.WhiteA700 : Color.black)
                            .fixedSize()
                            .multilineTextAlignment(.center)
                            .frame(width: getRelativeWidth(30.0),
                                   height: getRelativeWidth(30.0), alignment: .center)
                            .overlay(Circle()
                                .stroke(isDisabledDate(date) ? ColorConstants.BlueGray100 : Color(red: 0.11, green: 0.62, blue: 0.85),
                                        lineWidth: 1))
                            .if(isDisabledDate(date), transform: {
                                $0.overlay {
                                    Divider()

                                        .rotationEffect(.degrees(50))
                                        .frame(width: 2)
                                }
                            })
                            .background(
                                isDisabledDate(date) ? Color(red: 0.65, green: 0.67, blue: 0.67).opacity(0.25).clipShape(.circle) :
                                    calendar.isDate(date, inSameDayAs: selectedDate)
                                    ? ColorConstants.Blue600.clipShape(.circle) :
                                    Color(red: 0.11, green: 0.62, blue: 0.85).opacity(0.14).clipShape(.circle)
                            )

                            .if(calendar.isDate(date, inSameDayAs: selectedDate), transform: {
                                $0.matchedGeometryEffect(id: "calendar.selection", in: animation)
                            })
                    }
                    .disabled(isDisabledDate(date))
                },
                trailing: { date in
                    Text(dayFormatter.string(from: date))
//                        .foregroundColor(.secondary)
                        .foregroundColor(.clear)
                },
                header: { date in
                    Text(weekDayFormatter.string(from: date).uppercased())
//                    Text(StringConstants.kLblFri)
                        .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                        .fontWeight(.regular)
                        .foregroundColor(ColorConstants.Black900Bf)
                },
                title: { date in
                    VStack(spacing: 0) {
                        HStack {
                            Text(monthFormatter.string(from: date))
                                //                        Text(StringConstants.kLblNovember2023)
                                .font(FontScheme.kNunitoMedium(size: getRelativeHeight(15.0)))
                                .fontWeight(.medium)
                                .foregroundColor(ColorConstants.Black900)

                            Spacer()

                            HStack(spacing: 0) {
                                Button {
                                    //                            withAnimation {
                                    guard let newDate = calendar.date(
                                        byAdding: .month,
                                        value: -1,
                                        to: selectedDate
                                    ) else {
                                        return
                                    }

                                    selectedDate = newDate
                                    //                            }
                                } label: {
                                    Label(
                                        title: { Text("Previous") },
                                        icon: {
                                            Image(.rightArrow)
                                                .rotationEffect(.degrees(180))
                                                .frame(width: 37, height: 37)
                                        }
                                    )
                                    .labelStyle(IconOnlyLabelStyle())
                                    .padding(.horizontal)
                                    .frame(maxHeight: .infinity)
                                }.disableWithOpacity(date.isPastDate)

//                                Button(action: onMonth, label: {
//                                    HStack {
//                                        Text("Show Month")
//                                            .font(FontScheme.kNunitoMedium(size: getRelativeHeight(11.0)))
//                                            .fontWeight(.medium)
//                                            .fixedSize()
//                                            .foregroundColor(ColorConstants.Cyan8003f)
//                                        Image(systemName: "chevron.down")
//                                            .resizable()
//                                            .frame(width: 8, height: 4, alignment: .center)
//                                    }
//                                })

                                Button {
                                    //                            withAnimation {
                                    guard let newDate = calendar.date(
                                        byAdding: .month,
                                        value: 1,
                                        to: selectedDate
                                    ) else {
                                        return
                                    }

                                    selectedDate = newDate
                                    //                            }
                                } label: {
                                    Label(
                                        title: { Text("Next") },
                                        icon: { Image(.rightArrow)
                                            .frame(width: 37, height: 37)
                                        }
                                    )
                                    .labelStyle(IconOnlyLabelStyle())
                                    .padding(.horizontal)
                                    .frame(maxHeight: .infinity)
                                }
                            }

                            if let onClose = onClose {
                                Button(action: onClose) {
                                    Image(.closeX)
                                        .resizable()
                                        .scaledToFit()
                                        .frame(width: getRelativeWidth(20.0),
                                               height: getRelativeWidth(20.0), alignment: .center)

                                        .padding(.horizontal, getRelativeWidth(6.0))
                                }
                            }
                        }

                        if let _ = onClose {
                            Divider()
                                .frame(
                                    height: getRelativeHeight(1.0), alignment: .center
                                )
                                .background(ColorConstants.Cyan8003f)
                                .padding(.trailing, getRelativeWidth(4.0))
                                .padding(.top, 16)
                        }
                    }
                    .padding(.bottom)
                }
            )
            .equatable()
        }
        .padding(.horizontal)
    }
}

// MARK: - Component

public struct CalendarView<Day: View, Header: View, Title: View, Trailing: View>: View {
    // Injected dependencies
    private var calendar: Calendar
    @Binding private var date: Date
    private let content: (Date) -> Day
    private let trailing: (Date) -> Trailing
    private let header: (Date) -> Header
    private let title: (Date) -> Title

    // Constants
    private let daysInWeek = 7

    public init(
        calendar: Calendar,
        date: Binding<Date>,
        @ViewBuilder content: @escaping (Date) -> Day,
        @ViewBuilder trailing: @escaping (Date) -> Trailing,
        @ViewBuilder header: @escaping (Date) -> Header,
        @ViewBuilder title: @escaping (Date) -> Title
    ) {
        self.calendar = calendar
        self._date = date
        self.content = content
        self.trailing = trailing
        self.header = header
        self.title = title
    }

    public var body: some View {
        let month = date.startOfMonth(using: calendar)
        let days = makeDays()

        return LazyVGrid(columns: Array(repeating: GridItem(), count: daysInWeek)) {
            Section(header: title(month)) {
                ForEach(days.prefix(daysInWeek), id: \.self, content: header)

                ForEach(days, id: \.self) { date in

                    let isNotFromPreviousMonth: Bool = date.isNotFromPreviousMonth()

                    if calendar.isDate(date, equalTo: month, toGranularity: .month) && isNotFromPreviousMonth {
                        content(date)

                    } else {
                        trailing(date)
                    }
                }
            }
        }
    }
}

// MARK: - Conformances

extension CalendarView: Equatable {
    public static func == (lhs: CalendarView<Day, Header, Title, Trailing>, rhs: CalendarView<Day, Header, Title, Trailing>) -> Bool {
        lhs.calendar == rhs.calendar && lhs.date == rhs.date
    }
}

// MARK: - Helpers

private extension CalendarView {
    func makeDays() -> [Date] {
        guard let monthInterval = calendar.dateInterval(of: .month, for: date),
              let monthFirstWeek = calendar.dateInterval(of: .weekOfMonth, for: monthInterval.start),
              let monthLastWeek = calendar.dateInterval(of: .weekOfMonth, for: monthInterval.end - 1)
        else {
            return []
        }

        let dateInterval = DateInterval(start: monthFirstWeek.start, end: monthLastWeek.end)
        return calendar.generateDays(for: dateInterval)
    }
}

private extension Calendar {
    func generateDates(
        for dateInterval: DateInterval,
        matching components: DateComponents
    ) -> [Date] {
        var dates = [dateInterval.start]

        enumerateDates(
            startingAfter: dateInterval.start,
            matching: components,
            matchingPolicy: .nextTime
        ) { date, _, stop in
            guard let date = date else { return }

            guard date < dateInterval.end else {
                stop = true
                return
            }

            dates.append(date)
        }

        return dates
    }

    func generateDays(for dateInterval: DateInterval) -> [Date] {
        generateDates(
            for: dateInterval,
            matching: dateComponents([.hour, .minute, .second], from: dateInterval.start)
        )
    }
}

// MARK: - Previews

// #if DEBUG
struct CalendarView_Previews: PreviewProvider {
    static var previews: some View {
        CalendarSelectionView(selectedDate: .constant(.now), disabledDates: .constant([]), onMonth: {}, onClose: {})
    }
}

// #endif
