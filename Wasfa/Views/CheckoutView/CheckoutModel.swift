//
//  CheckoutModel.swift
//  Wasfa
//
//  Created by Apple on 12/10/2024.
//

import SwiftUI

struct PaymentMethodModel: Identifiable, Equatable {
    let id: UUID = .init()
    let key,name: String
    let image: ImageResource
}

// MARK: - CheckoutModel

struct CheckoutDetailsModel: Codable {
    let cartItems: [CartItem]
    let addressList: [AddressModel]?
    var paymentmethods: [String]
    let subTotal, shippingCost, discount, promotionDiscount: String
    let grandTotal: String
    let grandTotalValue: Double

    // MARK: - CartItem

    struct CartItem: Codable {
        let id, productID: Int
        let productName: String
        let productThumbnailImage: String
        let price: Double
        let unitPrice, currencySymbol: String
        let sellerID, quantity, freeProductsCount: Int

        enum CodingKeys: String, CodingKey {
            case id
            case productID = "productId"
            case productName, productThumbnailImage, price, unitPrice, currencySymbol
            case sellerID = "seller_id"
            case quantity
            case freeProductsCount = "free_products_count"
        }
    }
}
