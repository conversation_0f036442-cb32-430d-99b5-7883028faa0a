import SwiftUI

struct DeliverySectionCell: View {
    let title, subtitle: String
    let image: ImageResource
    let buttonTitle: String
    var isDate: Bool = false
    let buttonAction: () -> Void
    var body: some View {
        HStack {
            Button(action: {}, label: {
                Image(image)
            })
            .frame(width: getRelativeWidth(35.0), height: getRelativeWidth(37.0),
                   alignment: .center)
            .background(RoundedCorners(topLeft: 4.0, topRight: 4.0, bottomLeft: 4.0,
                                       bottomRight: 4.0)
                    .fill(ColorConstants.WhiteA700).shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 0))

            VStack(alignment: .leading, spacing: 0) {
                Text(title)
                    .font(FontScheme.kNunitoMedium(size: getRelativeHeight(14.0)))
                    .fontWeight(.medium)
                    .foregroundColor(ColorConstants.Black90001)
                    .multilineTextAlignment(.leading)

                Text(subtitle)
                    .multilineTextAlignment(.leading)
                    .if(isDate, transform: {
                        $0.font(FontScheme.kNunitoMedium(size: getRelativeHeight(12.0)))
                            .fontWeight(.medium)
                            .foregroundColor(ColorConstants.Blue600)
                    })
                    .if(!isDate) {
                        $0.font(
                            Font.custom("Nunito", size: 11)
                                .weight(.medium)
                        )
                        .kerning(0.5)
                        .foregroundColor(Color(red: 0.63, green: 0.63, blue: 0.63))
                    }
            }

            .padding(.leading, getRelativeWidth(8.0))
            .frame(maxWidth: .infinity, alignment: .leading)
            Button(action: buttonAction, label: {
                HStack(spacing: 0) {
                    Text(buttonTitle)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
                        .fontWeight(.bold)
                        .padding(.horizontal, getRelativeWidth(8.0))
                        .padding(.vertical, getRelativeHeight(6.0))
                        .foregroundColor(ColorConstants.WhiteA700)
                        .multilineTextAlignment(.center)
                        .frame(height: getRelativeHeight(30.0),
                               alignment: .center)
                        .background(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0,
                                                   bottomRight: 8.0)
                                .fill(ColorConstants.Blue600))
                }
            })
        }
        .padding(8)
        .frame(maxWidth: .infinity, alignment: .leading)
        .overlay(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0, bottomRight: 8.0)
            .stroke(ColorConstants.Black90001,
                    lineWidth: 1))
        .background(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0, bottomRight: 8.0)
            .fill(Color.clear.opacity(0.7)))
        .hideNavigationBar()
    }
}

struct AddressitemCell_Previews: PreviewProvider {
    static var previews: some View {
        VStack {
            DeliverySectionCell(title: "Delivery date", subtitle: "MAY 05, 2024", image: .imgGroup148, buttonTitle: "Schedule", isDate: true) {}

            DeliverySectionCell(title: "Shipping address", subtitle: "Sorry, There is no shipping\naddress", image: .imgPinFill, buttonTitle: "Add address") {}
        }.padding()
    }
}
