import SwiftUI

struct PaymentMethodCell: View {
    let model: PaymentMethodModel
    let selected: Bool
//    let animation: Namespace.ID
   
    var body: some View {
        HStack(spacing: 12) {
            VStack {
                ZStack {}
                    .hideNavigationBar()
                    .frame(width: getRelativeWidth(14.0), height: getRelativeWidth(16.0),
                           alignment: .leading)
                   
                    
                    .background(Circle()
                            .fill(ColorConstants.Black90001))
                    
                    .visibility(selected ? .visible : .gone)
//                    .if(selected, transform: {
//                        $0.matchedGeometryEffect(id: "payment.method.cell", in: animation)
//                    })
                    
            }
            .frame(width: getRelativeWidth(24.0), height: getRelativeWidth(26.0),
                   alignment: .center)
            .overlay(Circle()
                .stroke(ColorConstants.Black90001,
                        lineWidth: 1))
            .background(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                       bottomRight: 13.0)
                    .fill(Color.clear.opacity(0.7)))
            Text(model.name)
                .font(FontScheme.kNunitoMedium(size: getRelativeHeight(14.0)))
                .fontWeight(.medium)
                .foregroundColor(ColorConstants.Black90001)
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.leading)
                .frame(height: getRelativeHeight(20.0),
                       alignment: .leading)
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(.vertical, getRelativeHeight(7.0))
               
            Image(model.image)
                .resizable()
                .scaledToFit()
                .frame(width: getRelativeWidth(38.0), height: getRelativeHeight(30.0),
                       alignment: .leading)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .frame(width: getRelativeWidth(347.0), alignment: .leading)
        .overlay(RoundedCorners(topLeft: 6.0, topRight: 6.0, bottomLeft: 6.0, bottomRight: 6.0)
            .stroke(ColorConstants.Black90001,
                    lineWidth: 1))
        .background(RoundedCorners(topLeft: 6.0, topRight: 6.0, bottomLeft: 6.0, bottomRight: 6.0)
            .fill(Color.clear.opacity(0.7)))
    }
}

struct RowtextCell_Previews: PreviewProvider {
    static var previews: some View {
        PaymentMethodCell(model: .init(key: "COD", name: "Cash on delivery", image: .cashDelivery), selected: true)
    }
}

// let paymentMethods: [PaymentMethodModel] = [
//    .init(name: "KNET", image: .imgImage4),
//    .init(name: "Credit card", image: .creditCard),
//    .init(name: "Cash on delivery", image: .cashDelivery),
// ]
