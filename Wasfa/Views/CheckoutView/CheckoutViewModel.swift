import Foundation
import SwiftUI
import UIKit

enum PopUpType: Equatable {
    case date, address(model: AddressModel? = nil), tapPayment
}

class CheckoutViewModel: SuperViewModel {
    private static var now = Date() // Cache now
    @Published var popUpType: PopUpType?
    @Published var checkoutDetailsModel: CheckoutDetailsModel?
    @Published var selectedDate: Date = CheckoutViewModel.now
    @Published var submittedDate: Date = CheckoutViewModel.now

    @Published var isDateChanged: Bool = false

    @Published var addressModelRequest: AddressModel?

    @Published var showingTapCheckout = false

    // Updated payment methods to include Tap Payments
    let paymentMethods: [PaymentMethodModel] = [
        .init(key: "tap", name: "Tap Payments (KNET/Cards)", image: .creditCard),
        .init(key: "knet", name: "KNET", image: .imgImage4),
        .init(key: "wallet", name: "Wallet Payment", image: .creditCard),
        .init(key: "COD", name: "Cash on delivery", image: .cashDelivery),
    ]
    @Published var selectedPaymentMethod: PaymentMethodModel?

    // Tap Payment integration
    @Published var isProcessingTapPayment = false
    @Published var tapPaymentError: String?
    private let tapPaymentManager = TapPaymentManager.shared

    private var orderCode: String?

    func updatePaymentMethod(_ value: PaymentMethodModel?) {
        selectedPaymentMethod = value
    }

    var deliveryDateString: String {
        submittedDate.toString(outputFormate: "MMM dd, yyyy")
    }

    let cartModel: CartModel
    init(_ cartModel: CartModel) {
        self.cartModel = cartModel

        super.init()
        getCheckoutData()
    }

    func onDeliveryAddressSelection() {
        if AppState.isLoggedIn {
            routerManager?.push(to: .deliveryAddress(onSelectFromCheckout: .init(onBack: { value in
                self.updateAddressModelRequest(value)
            })), appState: appState)
        } else {
            updatePopUpType(.address())
        }
    }

    func updatePopUpType(_ value: PopUpType? = nil) { withAnimation(.bouncy) { self.popUpType = value }}

    func getCheckoutData() {
        onApiCall(api.checkoutPageDetails, parameters: ["is_rx_list": cartModel.isRxList ? 1 : 0]) {
            self.checkoutDetailsModel = $0.data

            self.addCODForTesting()

            let defaultAddressModel: AddressModel? = self.checkoutDetailsModel?.addressList?.first(where: { $0.isDefault })
            self.updateAddressModelRequest(defaultAddressModel)
        }
    }

    func addCODForTesting() {
//       let cod = PaymentMethodModel(key: "1", name: "COD", image: .cashDelivery)

//        self.checkoutDetailsModel?.paymentmethods.append("cod")
    }

    func updateAddressModelRequest(_ value: AddressModel?) {
        addressModelRequest = value
    }

    var disableProceedButton: Bool {
        addressModelRequest == nil
    }

    func onProceedCheckout() {
        guard let addressModelRequest = addressModelRequest else {
            appState?.showToast(.init(type: .error, message: "Please select your shipping address"))
            return
        }
        guard let selectedPaymentMethod = selectedPaymentMethod else {
            appState?.showToast(.init(type: .error, message: "Please select a payment method"))
            return
        }

        // Handle other payment methods (existing logic)
        let deliveryDateString = submittedDate.toString(outputFormate: "yyyy-MM-dd")
        var parameters: [String: Any] = [:]

        if AppState.isLoggedIn {
            if let addressID = addressModelRequest.addressId {
                parameters.updateValue(addressID, forKey: "addressId")
            }
        } else {
            parameters = addressModelRequest.toDictionary
        }
        parameters.updateValue(selectedPaymentMethod.key, forKey: "paymentType")
        parameters.updateValue(deliveryDateString, forKey: "deliveryDate")
        parameters.updateValue(cartModel.isRxList ? 1 : 0, forKey: "is_rx_list")

        onApiCall(api.placeOrder, parameters: parameters) { response in
            let orderCode: String? = response.data?.orderCode
            self.orderCode = orderCode

            // Check if Tap Payments is selected
            if selectedPaymentMethod.key == "tap" || selectedPaymentMethod.key == "knet" {
                self.handleTapPayment()

            } else {
                let routesType = self.routerManager!.mapRouterWithTab(appState: self.appState!)
                self.routerManager?.replace(stack: [.orderSuccess], where: routesType)
            }
        }
    }

    // MARK: - Tap Payment Methods

    private func handleTapPayment() {
        guard let checkoutDetails = checkoutDetailsModel,
              let addressModel = addressModelRequest
        else {
            tapPaymentError = "Missing checkout details or address"
            return
        }

        // Show Tap Payment UI
//        updatePopUpType(.tapPayment)
        showingTapCheckout = true
    }

    @MainActor
    func processTapPayment() async {
        guard let checkoutDetails = checkoutDetailsModel,
              let addressModel = addressModelRequest
        else {
            tapPaymentError = "Missing checkout details or address"
            return
        }

        isProcessingTapPayment = true
        tapPaymentError = nil

        // Debug checkout details before creating payment request
        print("🔍 CheckoutViewModel: Creating payment request with checkout details:")
        print("  - Grand Total String: '\(checkoutDetails.grandTotal)'")
        print("  - Grand Total Value: \(checkoutDetails.grandTotalValue)")
        print("  - Sub Total: '\(checkoutDetails.subTotal)'")
        print("  - Shipping Cost: '\(checkoutDetails.shippingCost)'")
        print("  - Discount: '\(checkoutDetails.discount)'")
        print("  - Promotion Discount: '\(checkoutDetails.promotionDiscount)'")
        print("  - Cart Items Count: \(checkoutDetails.cartItems.count)")

        // Debug individual cart items
        for (index, item) in checkoutDetails.cartItems.enumerated() {
            print("  - Item \(index + 1): '\(item.productName)' - Price: \(item.price) x \(item.quantity) = \(item.price * Double(item.quantity))")
        }

        // Calculate manual total for verification
        let itemsSubtotal = checkoutDetails.cartItems.reduce(0.0) { $0 + ($1.price * Double($1.quantity)) }
        print("  - Calculated Items Subtotal: \(itemsSubtotal)")

        // Check if amount is valid
        if checkoutDetails.grandTotalValue <= 0 {
            print("❌ CheckoutViewModel: Invalid grand total - Amount is zero or negative!")
        }
        if checkoutDetails.grandTotalValue != checkoutDetails.grandTotalValue {
            print("❌ CheckoutViewModel: Invalid grand total - Amount is NaN!")
        }

        // Create payment request
        let paymentRequest = TapPaymentRequest(
            amount: checkoutDetails.grandTotalValue,
            currency: "KWD", // You might want to make this dynamic
            customerEmail: addressModel.email ?? "",
            customerName: addressModel.fullName,
            customerPhone: addressModel.phone,
            items: checkoutDetails.cartItems.map { item in
                TapPaymentItem(
                    title: item.productName,
                    description: "Product from Wasfa",
                    price: item.price,
                    quantity: item.quantity
                )
            },
            description: "Order from Wasfa App",
            metadata: [
                "order_date": submittedDate.toString(outputFormate: "yyyy-MM-dd"),
                "delivery_address": addressModel.deliveryAddress,
            ]
        )

        // Get presenting view controller (this will need to be passed from the view)
        guard let presentingVC = getCurrentViewController() else {
            tapPaymentError = "Unable to present payment screen"
            isProcessingTapPayment = false
            return
        }

        let result = await tapPaymentManager.processPayment(
            request: paymentRequest,
            presentingViewController: presentingVC
        )

        isProcessingTapPayment = false

        switch result {
        case .success(let transactionId, let amount, let currency):
            // Handle successful payment
            await handleSuccessfulTapPayment(transactionId: transactionId, amount: amount, currency: currency)

        case .failure(let error):
            handleTapPaymentError(error)

        case .cancelled:
            // User cancelled payment
            updatePopUpType(nil)
        }
    }

    @MainActor
    func handleSuccessfulTapPayment(transactionId: String, amount: Double, currency: String) async {
        // Close payment popup
        updatePopUpType(nil)

        var parameters: [String: Any] = [:]

//        if AppState.isLoggedIn {
//            if let addressID = addressModelRequest?.addressId {
//                parameters.updateValue(addressID, forKey: "addressId")
//            }
//        } else {
//            parameters = addressModelRequest?.toDictionary ?? [:]
//        }

        guard let orderCode = orderCode else { return }

        parameters.updateValue("1", forKey: "status")
        parameters.updateValue(orderCode, forKey: "orderId")
        parameters.updateValue(transactionId, forKey: "transactionId")
        parameters.updateValue(cartModel.isRxList ? 1 : 0, forKey: "is_rx_list")

        onApiCall(api.paymentResponse, parameters: parameters) { response in
            let orderId: Int? = response.data?.orderID

            let routesType = self.routerManager!.mapRouterWithTab(appState: self.appState!)
            self.routerManager?.replace(stack: [.orderSuccess], where: routesType)
        }
    }

    /// Handle Tap Payment errors with enhanced user-friendly messages and appropriate UI feedback
    func handleTapPaymentError(_ error: TapPaymentError) {
        // Ensure UI updates happen on main thread
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            // Set the error message for display in the UI
            self.tapPaymentError = error.userFriendlyMessage

            // Show user-friendly error message using the existing alert system
            // This replaces technical error messages with clear, actionable messages
//            self.showAPIErrorAlert(errorMessage: error.userFriendlyMessage)
            self.routerManager?.push(to: .orderFailure(message: error.userFriendlyMessage), appState: self.appState)
        }
    }

    /// Show a specific alert for API key configuration errors
    private func showAPIErrorAlert(errorMessage: String) {
        let alertConfig = AlertConfig(
            title: "Payment Error",
            text: errorMessage,
            alertType: .alert,
        )

        // Update page state to show the alert
        DispatchQueue.main.async {
            self.updatePageState(.message(config: alertConfig))
        }
    }

    private func getCurrentViewController() -> UIViewController? {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first
        else {
            return nil
        }
        return window.rootViewController?.topMostViewController()
    }
}
