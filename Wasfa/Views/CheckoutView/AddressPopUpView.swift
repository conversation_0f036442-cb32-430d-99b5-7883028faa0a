//
//  AddressPopUpView.swift
//  Wasfa
//
//  Created by Apple on 26/11/2024.
//

import SwiftUI

struct AddressPopUpView: View {
    let onAddAddress: (AddressModel?, Bool) -> Void
    @StateObject private var viewModel: AddressPopUpViewModel

    init(model: AddressModel? = nil, onAddAddress: @escaping (AddressModel?, Bool) -> Void) {
        self.onAddAddress = onAddAddress
        self._viewModel = StateObject(wrappedValue: AddressPopUpViewModel(model: model))
    }

    var body: some View {
        VStack(spacing: 0) {
            // Header
            HStack {
                Text(viewModel.model != nil ? "Update Current Address" : StringConstants.kLblAddNewAddress)
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(ColorConstants.Black90001)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.top, 8)
                <PERSON><PERSON>(action: { self.onAddAddress(nil, viewModel.model != nil) }) {
                    Image("close.x")
                        .resizable()
                        .frame(width: 24, height: 24)
                }
            }
            .padding(.horizontal, 16)
            .padding(.top, 16)

            Divider()
                .frame(height: 1)
                .background(ColorConstants.Cyan8003f)
                .padding(.top)

            ScrollView {
                VStack(spacing: 16) {
                    // Using InputFieldView for Address Title

                    // Area Picker
                    CustomPickerView(title: "Title", placeholder: "Select Title", list: AddressTitleType.allCases, selectedItem: self.$viewModel.selectedTitle)

                    // Using InputFieldView for First Name
                    InputFieldView(
                        title: "First Name",
                        placeholder: "First Name",
                        text: self.$viewModel.firstNameText,
                        isRequired: true
                    )

                    // Using InputFieldView for Last Name
                    InputFieldView(
                        title: "Last Name",
                        placeholder: "Last Name",
                        text: self.$viewModel.lastNameText,
                        isRequired: true
                    )

                    // Using InputFieldView for Email
                    InputFieldView(
                        title: "Email",
                        placeholder: "<EMAIL>",
                        text: self.$viewModel.emailText,
                        isRequired: true
                    )

                    // Using InputFieldView for Phone
                    InputFieldView(
                        title: "Phone",
                        placeholder: "Enter Phone Number",
                        text: self.$viewModel.phoneText,
                        selectedCountryCode: $viewModel.selectedCountryCode,
                        isRequired: true,
                        isPhoneNumber: true
                    )

                    // Using InputFieldView for Alternative Phone Number
                    InputFieldView(
                        title: "Alternative Phone",
                        placeholder: "Enter Alternative Phone Number",
                        text: self.$viewModel.alternativePhoneText,
                        selectedCountryCode: $viewModel.selectedAlternativeCountryCode,
                        isRequired: false,
                        isPhoneNumber: true
                    )

                    // Governate Picker
                    CustomPickerView(title: "Governorate", placeholder: "Select Governorate", list: self.viewModel.kuwaitGovernorate, selectedItem: self.$viewModel.selectedGovernorate)
                        .onChange(of: viewModel.selectedGovernorate, viewModel.onChangeGovernorate)

                    // Area Picker
                    CustomPickerView(title: "Area", placeholder: "Select Area", list: self.viewModel.kuwaitAreas, selectedItem: self.$viewModel.selectedArea)

//                    VStack(alignment: .leading, spacing: 8) {
//                        Text(StringConstants.kLblArea + " *")
//                            .font(.system(size: 14, weight: .medium))
//                            .foregroundColor(ColorConstants.Black90001)
//
                    ////                        CustomPickerView(areas: <#T##[String]#>, selectedArea: <#T##String?#>)
//
//
//
                    ////                        Picker(StringConstants.kLblSelectArea, selection: $viewModel.selectareaPicker1) {
                    ////                            ForEach(viewModel.kuwaitAreas, id: \.self) { area in
                    ////                                Button {
                    ////                                    viewModel.updateArea(area)
                    ////                                } label: {
                    ////                                    Text(area)
                    ////                                }
                    ////
                    ////                            }
                    ////                        }
                    ////                        .pickerStyle(MenuPickerStyle())
//                        .frame(maxWidth: .infinity)
//
//
//                    }
                    // Using InputFieldView for Block
                    InputFieldView(
                        title: "Block",
                        placeholder: "Block",
                        text: self.$viewModel.blockText,
                        isRequired: true
                    )

                    // Using InputFieldView for Street Name
                    InputFieldView(
                        title: "Street Name",
                        placeholder: "Street Name",
                        text: self.$viewModel.streetText,
                        isRequired: true
                    )

                    // Using InputFieldView for Building
                    InputFieldView(
                        title: "Building",
                        placeholder: "Building",
                        text: self.$viewModel.buildingText,
                        isRequired: true
                    )

                    // Using InputFieldView for Avenue
                    InputFieldView(
                        title: "Apartment",
                        placeholder: "Apartment",
                        text: self.$viewModel.apartmentText,
                        isRequired: false
                    )

                    // Using InputFieldView for Floor
                    InputFieldView(
                        title: "Floor",
                        placeholder: "Floor",
                        text: self.$viewModel.floorText,
                        isRequired: false
                    )

//                    // Using InputFieldView for Notes
//                    VStack(alignment: .leading, spacing: 8) {
//                        Text(StringConstants.kLblNotes)
//                            .font(.system(size: 14, weight: .medium))
//                            .foregroundColor(ColorConstants.Black90001)
//
//                        TextField(StringConstants.kLblNotes, text: $viewModel.floorText, axis: .vertical)
//                            .lineLimit(3...4)
//
//
//                            .textFieldStyle()
//                    }
                }
                .padding(.horizontal, 16)
            }
            .safeAreaPadding(.vertical)
            .scrollIndicators(.hidden)

            // Add Address Button
            Button(action: {
                self.onAddAddress(viewModel.getAddressModelRequest, viewModel.model != nil)
            }) {
                Text(viewModel.model != nil ? "Update Address" : StringConstants.kLblAddAddress2)
                    .font(.system(size: 18, weight: .bold))
                    .frame(maxWidth: .infinity)
                    .padding()
                    .foregroundColor(ColorConstants.WhiteA700)
                    .background(ColorConstants.Blue300)
                    .cornerRadius(8)
                    .padding([.bottom, .horizontal])
            }
            .disableWithOpacity(viewModel.disableAddAddressButton)
            .padding(.top)
        }
        .background(
            RoundedRectangle(cornerRadius: 28)
                .fill(ColorConstants.WhiteA700)
                .shadow(color: ColorConstants.Black9003f, radius: 7, x: 0, y: -7)
        )
        .padding(16)
    }
}

struct InputFieldView: View {
    // MARK: - Properties

    let title: String
    let placeholder: String
    @Binding var text: String
    var selectedCountryCode: Binding<CountryCode>?
    let isRequired: Bool
    var isPhoneNumber: Bool = false
    var isDisabled: Bool = false

    // MARK: - Body

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
//            Text("\(LocalizedStringKey(self.title))\(self.isRequired ? " *" : "")")
            HStack(spacing: 0){
                Text(LocalizedStringKey(self.title))
                if self.isRequired {
                    Text(" *")
                        .font(.system(size: 14, weight: .medium))
                }
            }
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(ColorConstants.Black90001)

            HStack {
                CountryCodePickerView(showBorder: true, selectedCountryCode: selectedCountryCode ?? .constant(.kuwait))
                    .frame(maxWidth: 45, maxHeight: 26)
                    .textFieldStyle()
                    .visibility(isPhoneNumber ? .visible : .gone)

                TextField(LocalizedStringKey(self.placeholder), text: self.$text)
                    .textFieldStyle()
                    .foregroundStyle(isDisabled ? .gray.opacity(0.6) : ColorConstants.Black90001)
                    .keyboardType(isPhoneNumber ? .phonePad : .default)
            }
            .disabled(isDisabled)
        }
    }
}

// MARK: - Country Code Picker Component

struct CountryCodePickerView: View {
    var showBorder: Bool = false

    // MARK: - Properties

    @Binding var selectedCountryCode: CountryCode

    // MARK: - Body

    var body: some View {
        Menu {
            ForEach(CountryCode.allCases) { country in
                Button(action: {
                    selectedCountryCode = country
                }) {
                    HStack {
                        HStack(spacing: 4) {
                            Text("\(country.flag) (\(country.dialCode)) \(country.countryName)")
                        }
                        // add custom font named "Poppins" with 16px size and semibold
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(ColorConstants.Black90001)
                        .frame(maxWidth: .infinity, alignment: .leading)

                        if selectedCountryCode == country {
                            Image(systemName: "checkmark.circle")
                                .foregroundColor(.green)
                        }
                    }
                }
            }
        } label: {
            HStack(spacing: 2) {
//                Text(selectedCountryCode.flag)
                Text(selectedCountryCode.dialCode)
            }
            .frame(width: 65)
            .padding(.vertical, 8)
            .padding(.horizontal, 4)
            .background(
                RoundedRectangle(cornerRadius: 15)
                    .fill(.clear)
//                    .shadow(color: ColorConstants.Black9003f.opacity(0.1), radius: 2)
            )
        }
        .tint(ColorConstants.Black90001)
    }

    // MARK: - Binding Accessor

    func countryCodeBinding() -> Binding<CountryCode> {
        Binding(
            get: { selectedCountryCode },
            set: { selectedCountryCode = $0 }
        )
    }

    // MARK: - Helper Methods

    func dialCode() -> String {
        selectedCountryCode.dialCode
    }
}

// MARK: - Country Code Model

enum CountryCode: String, CaseIterable, Identifiable {
    case kuwait
    case uae
    case bahrain
    case qatar
    case oman
    case saudiArabia

    var id: String { rawValue }

    var flag: String {
        switch self {
        case .kuwait: return "🇰🇼"
        case .uae: return "🇦🇪"
        case .bahrain: return "🇧🇭"
        case .qatar: return "🇶🇦"
        case .oman: return "🇴🇲"
        case .saudiArabia: return "🇸🇦"
        }
    }

    var dialCode: String {
        switch self {
        case .kuwait: return "+965"
        case .uae: return "+971"
        case .bahrain: return "+973"
        case .qatar: return "+974"
        case .oman: return "+968"
        case .saudiArabia: return "+966"
        }
    }

    var countryName: String {
        switch self {
        case .kuwait: return "Kuwait"
        case .uae: return "UAE"
        case .bahrain: return "Bahrain"
        case .qatar: return "Qatar"
        case .oman: return "Oman"
        case .saudiArabia: return "Saudi Arabia"
        }
    }
}

// make a string extension to get the country code and phone numebr from the string
extension String {
    func extractCountryCodeAndPhoneNumber(from knownCodes: [CountryCode]) -> (countryCode: CountryCode, phoneNumber: String) {
        // Try to find a matching dial code prefix
        for code in knownCodes.sorted(by: { $0.dialCode.count > $1.dialCode.count }) {
            if starts(with: code.dialCode) {
                let number = replacingOccurrences(of: code.dialCode, with: "")
                return (countryCode: code, phoneNumber: number)
            }
        }
        // Fallback if no match
        return (countryCode: .kuwait, phoneNumber: self)
    }
}

// TextFieldStyle Helper
extension View {
    func textFieldStyle() -> some View {
        padding()
            .background(
                RoundedRectangle(cornerRadius: 15)
                    .fill(ColorConstants.WhiteA700)
                    .shadow(color: ColorConstants.Black9003f.opacity(0.1), radius: 2)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 15)
                    .stroke(ColorConstants.Black90001, lineWidth: 1)
            )
    }
}

#Preview {
    SlideUpAnimationContainerView(perform: {}) {
        AddressPopUpView(model: nil, onAddAddress: { _, _ in })
    }
}

enum AddressTitleType: String, CaseIterable, Codable, NamedItem {
    case homeApartment, office

    enum CodingKeys: String, CodingKey {
        case homeApartment = "home/apartment"
        case office
    }

    var id: Int {
        switch self {
        case .homeApartment:
            0
        case .office:
            1
        }
    }

    var name: String {
        switch self {
        case .homeApartment:
            return "Home/Apartment"
        case .office:
            return "Office"
        }
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        self = try AddressTitleType(rawValue: container.decode(String.self)) ?? .homeApartment
    }
}

class AddressPopUpViewModel: SuperViewModel {
    @Published var firstNameText: String = .init()
    @Published var lastNameText: String = .init()
    @Published var emailText: String = .init()

    @Published var phoneText: String = .init()
    @Published var alternativePhoneText: String = .init()
    @Published var selectedCountryCode: CountryCode = .kuwait
    @Published var selectedAlternativeCountryCode: CountryCode = .kuwait

    @Published var selectedArea: AreaModel?
    @Published var selectedTitle: AddressTitleType?
    @Published var kuwaitAreas: [AreaModel] = []

    @Published var selectedGovernorate: GovernorateModel?
    @Published var kuwaitGovernorate: [GovernorateModel] = []

    @Published var blockText: String = ""
    @Published var streetText: String = ""

    @Published var buildingText: String = ""
    @Published var apartmentText: String = ""
    @Published var floorText: String = ""

    let model: AddressModel?

    init(model: AddressModel?) {
        self.model = model
        super.init()
        assignModelValues()
        getGovernateDate()
        getAreaData()
    }

    func assignModelValues() {
        guard let model = model else { return }
        selectedTitle = model.addressTitle
        firstNameText = model.firstName
        lastNameText = model.lastName
        emailText = model.email ?? ""
//        phoneText = model.phone
        blockText = model.block
        streetText = model.street
        buildingText = model.building
        apartmentText = model.appartment ?? ""
        floorText = model.floor ?? ""
//        alternativePhoneText = model.alternatePhone ?? ""

        let phoneResult = model.phone.extractCountryCodeAndPhoneNumber(from: CountryCode.allCases)
        phoneText = phoneResult.phoneNumber
        selectedCountryCode = phoneResult.countryCode

        if let alternativePhoneText = model.alternatePhone {
            let alternativePhoneResult = alternativePhoneText.extractCountryCodeAndPhoneNumber(from: CountryCode.allCases)
            self.alternativePhoneText = alternativePhoneResult.phoneNumber
            selectedAlternativeCountryCode = alternativePhoneResult.countryCode
        }
    }

    func getGovernateDate() {
        onApiCall(api.governorateList, parameters: emptyDictionary, withLoadingIndicator: false) {
            self.kuwaitGovernorate = $0.data ?? []
            self.selectedGovernorate = self.kuwaitGovernorate.first(where: { $0.id == self.model?.governorateID })
        }
    }

    func getAreaData(gID: Int? = nil) {
        kuwaitAreas.removeAll()
        selectedArea = nil
        onApiCall(api.areaList, parameters: ["governorateId": gID ?? 0], withLoadingIndicator: false) {
            self.kuwaitAreas = $0.data ?? []
            self.selectedArea = self.kuwaitAreas.first(where: { $0.id == self.model?.areaID })
        }
    }

    func onChangeGovernorate(oldValue: GovernorateModel?, newValue: GovernorateModel?) {
        getAreaData(gID: newValue?.id)
    }

    var disableAddAddressButton: Bool {
        firstNameText.isEmpty || lastNameText.isEmpty || emailText.isEmpty || phoneText.isEmpty || selectedTitle == nil || selectedArea == nil || selectedGovernorate == nil || blockText.isEmpty || streetText.isEmpty || buildingText.isEmpty
    }

    var getAddressModelRequest: AddressModel {
        let phoneNumberWithDialCode: String = selectedCountryCode.dialCode + phoneText
        let alternativePhoneNumberWithDialCode: String = selectedAlternativeCountryCode.dialCode + alternativePhoneText

        return .init(addressId: model?.addressId, addressTitle: selectedTitle!, firstName: firstNameText, lastName: lastNameText, email: emailText, governorateID: selectedGovernorate!.id, governorateName: selectedGovernorate!.name, areaID: selectedArea!.id, areaName: selectedArea!.name, block: blockText, phone: phoneNumberWithDialCode, setDefault: 1, street: streetText, building: buildingText, floor: floorText, appartment: apartmentText, alternatePhone: alternativePhoneNumberWithDialCode)
    }
}

struct CustomPickerView<Item: NamedItem & Hashable>: View {
    let title: String
    let placeholder: String
    let list: [Item]
    @Binding var selectedItem: Item?

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Title
            Text("\(self.title) *")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.black)

            // Menu for Selection
            Menu {
                ForEach(self.list) { item in
                    Button(action: {
                        self.selectedItem = item
                    }) {
                        HStack {
                            Text(item.name) // Display item's unique identifier or name
                                .foregroundColor(.primary)

                            if self.selectedItem == item {
                                Image(systemName: "checkmark.circle")
                                    .foregroundColor(.green)
                            }
                        }
                    }
                }
            } label: {
                HStack {
                    if self.list.isEmpty {
                        ProgressView()
                        Spacer()
                    } else {
                        Text(self.selectedItem?.name ?? self.placeholder) // Placeholder or Selected Value
                            .foregroundColor(self.selectedItem == nil ? .gray : .black)
                        Spacer()
                        Image(systemName: "chevron.down") // Dropdown indicator
                            .foregroundColor(.black)
                    }
                }
                .padding(.vertical)
                .padding(.horizontal)
                .background(
                    RoundedRectangle(cornerRadius: 15)
                        .fill(Color.white)
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 15)
                        .stroke(Color.black, lineWidth: 1)
                )
            }.disabled(self.list.isEmpty)
        }
    }
}
