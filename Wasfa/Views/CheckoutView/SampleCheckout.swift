//
//  SampleCheckout.swift
//  Wasfa
//
//  Created by Apple on 05/04/2025.
//

import SwiftUI

struct PaymentOptionView: View {
    let model: PaymentMethodModel
    let isSelected: Bool
    
    var body: some View {
        HStack {
            Image(systemName: isSelected ? "largecircle.fill.circle" : "circle")
            
                .resizable()
                .fontWeight(.thin)
                .frame(width: 26.relativeFontSize, height: 26.relativeFontSize)
                .foregroundColor(isSelected ? Color.init(hex: "#1B9ED9"): ColorConstants.Gray400)
            Text(model.name)
                .font(.custom("Poppins", size: 14.relativeFontSize))
                .fontWeight(.regular)
                
                .foregroundColor(.black)
            Spacer()
            Image(model.image)
                .resizable()
                .scaledToFit()
                .frame(width: 40.relativeWidth, height: 30.relativeHeight)
        }
        .padding()
        .frame(height: 46.relativeHeight)
        .background(RoundedRectangle(cornerRadius: 17).fill(Color.white))
        .overlay(
            RoundedRectangle(cornerRadius: 17)
                .stroke(Color(hex: "#ECEDF0"), lineWidth: 1)
        )
        
    }
}

