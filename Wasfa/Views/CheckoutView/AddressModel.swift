//
//  AddressModel.swift
//  Wasfa
//
//  Created by Apple on 27/11/2024.
//

struct AddressModelRequest: Encodable {
    let addressId: Int?
    let governorateId, areaId: Int
    let phone,
        firstName,
        lastName,
        email,
        block,
        street,
        appartment,
        building,
        floor,
        addressTitle: String

//    var deliveryAddress: String {
//        var components: [String] = []
//        if !addressTitle.isEmpty { components.append("Title: \(addressTitle)") }
////        if !firstName.isEmpty || !lastName.isEmpty {
////            components.append("\(firstName) \(lastName)".trimmingCharacters(in: .whitespaces))
////        }
////        if !email.isEmpty { components.append("Email: \(email)") }
////        if !phone.isEmpty { components.append("Phone: \(phone)") }
//        if !block.isEmpty { components.append("Block: \(block)") }
//        if !street.isEmpty { components.append("Street: \(street)") }
//        if !building.isEmpty { components.append("Building: \(building)") }
//        if !floor.isEmpty { components.append("Floor: \(floor)") }
//        if !appartment.isEmpty { components.append("Apartment: \(appartment)") }
//        
//
//        return components.joined(separator: ", ")
//    }
    
    var deliveryAddress: String {
        var components: [String] = []
        if !addressTitle.isEmpty { components.append("\(addressTitle): ") }
//        if !firstName.isEmpty || !lastName.isEmpty {
//            components.append("\(firstName) \(lastName)".trimmingCharacters(in: .whitespaces))
//        }
//        if !email.isEmpty { components.append("Email: \(email)") }
//        if !phone.isEmpty { components.append("Phone: \(phone)") }
        if !block.isEmpty { components.append("\(block)") }
        if !street.isEmpty { components.append("\(street)") }
        if !building.isEmpty { components.append("\(building)") }
        if !floor.isEmpty { components.append("\(floor)") }
        if !appartment.isEmpty { components.append("\(appartment)") }
        

        return components.joined(separator: ", ")
    }
}
