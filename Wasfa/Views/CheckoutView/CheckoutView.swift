import SwiftUI

// struct CheckoutView1: View {
//    @StateObject var viewModel: CheckoutViewModel
//    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
//    @Namespace private var animation
//
//    init(cartModel: CartModel) {
//        self._viewModel = StateObject(wrappedValue: CheckoutViewModel(cartModel))
//    }
//
//    var body: some View {
//        SuperView(pageState: $viewModel.pageState) {
//            MainScrollBody(backButtonWithTitle: "Checkout") {
//                VStack(spacing: 24) {
//                    VStack(alignment: .leading, spacing: 16) {
//                        Text(StringConstants.kLblDelivery)
//                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(20.0)))
//                            .foregroundColor(ColorConstants.Black90001)
//                            .padding(.bottom, 12)
//
//                        VStack(spacing: 16) {
//                            DeliverySectionCell(
//                                title: "Delivery date",
//                                subtitle: viewModel.deliveryDateString,
//                                image: .imgGroup148,
//                                buttonTitle: "Schedule",
//                                isDate: true
//                            ) {
//                                viewModel.updatePopUpType(.date)
//                            }
//                            DeliverySectionCell(
//                                title: "Shipping address",
//                                subtitle: viewModel.addressModelRequest?.deliveryAddress ?? "No shipping address available",
//                                image: .imgPinFill,
//                                buttonTitle: viewModel.addressModelRequest != nil ? "Change address" : "Add address",
//                                buttonAction: viewModel.onDeliveryAddressSelection
//                            )
//                        }
//                    }
//                    .padding(.horizontal, 20)
//
//                    if let checkoutDetailsModel = viewModel.checkoutDetailsModel {
//                        let paymentMethods = viewModel.paymentMethods.filter {
//                            checkoutDetailsModel.paymentmethods.contains($0.key.lowercased())
//                        }
//
//                        VStack(alignment: .leading, spacing: 16) {
//                            Text(StringConstants.kLblPaymentMethod)
//                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(20.0)))
//                                .foregroundColor(ColorConstants.Black90001)
//                                .padding(.bottom, 12)
//
//                            VStack(spacing: 16) {
//                                ForEach(paymentMethods) { model in
//                                    PaymentMethodCell(model: model, selected: viewModel.selectedPaymentMethod == model)
//                                        .onTapGesture {
//                                            viewModel.updatePaymentMethod(model)
//                                        }
//                                }
//                            }
//                            .padding(.horizontal, 20)
//                        }
//                    }
//                }
//                .padding(.bottom, 24)
//                .background(ColorConstants.WhiteA700)
//            }
//            .safeAreaInset(edge: .bottom) {
//                SafeAreaViewSection
//            }
//            .toolbarBackground(.visible, for: .tabBar)
//            .overlay(alignment: .bottom) {
//                Group {
//                    switch viewModel.popUpType {
//                    case .date:
//                        datePopUpView
//                    case .address:
//                        addressPopUpView
//                    case .none:
//                        EmptyView()
//                    }
//                }
//                .animation(.easeOut, value: self.viewModel.popUpType)
//            }
//        }
//        .injectEnvironmentValues(viewModel)
//    }
//
//    var SafeAreaViewSection: some View {
//        VStack(alignment: .leading) {
//            if let cartModel = viewModel.checkoutDetailsModel {
//                Text(StringConstants.kLblOrderInfo)
//                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
//                    .foregroundColor(ColorConstants.Black90001)
//                    .padding(.top, getRelativeHeight(24.0))
//                    .padding(.leading, getRelativeWidth(20.0))
//
//                VStack(spacing: 12) {
//                    orderInfoRow(label: StringConstants.kLblSubtotal, value: cartModel.subTotal, topPadding: 16.relativeHeight)
//                    orderInfoRow(label: StringConstants.kLblDiscount, value: cartModel.discount)
//                    orderInfoRow(label: StringConstants.kLblDelivery, value: cartModel.shippingCost)
//                }
//
//                Divider()
//                    .background(ColorConstants.Teal90026)
//                    .padding(.top, getRelativeHeight(12.0))
//
//                orderInfoRow(
//                    label: StringConstants.kLblTotal,
//                    value: cartModel.grandTotal,
//                    labelFont: FontScheme.kNunitoBold(size: 18.relativeFontSize).bold(),
//                    valueFont: FontScheme.kNunitoBold(size: 18.relativeFontSize).bold(),
//                    topPadding: 8.0
//                )
//                .padding(.bottom, 16)
//
//                Button(action: viewModel.onProceedCheckout) {
//                    Text(StringConstants.kMsgProceedToCheckout)
//                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(20.0)))
//                        .fontWeight(.bold)
//                        .padding(.horizontal, getRelativeWidth(32.0))
//                        .padding(.vertical, getRelativeHeight(16.0))
//                        .foregroundColor(ColorConstants.WhiteA700)
//                        .frame(maxWidth: .infinity)
//                        .background(
//                            RoundedCorners(topLeft: 10.0, topRight: 10.0, bottomLeft: 10.0, bottomRight: 10.0)
//                                .fill(ColorConstants.Blue300)
//                        )
//                        .padding(.horizontal, 24.relativeWidth)
//                        .shadow(color: ColorConstants.Black9003f, radius: 6, x: 0, y: 2)
//                }
//                .disableWithOpacity(viewModel.disableProceedButton)
//                .frame(maxWidth: .infinity)
//                .padding(.bottom, 20)
//            }
//        }
//        .background(
//            RoundedCorners(topLeft: 24.0, topRight: 24.0)
//                .fill(ColorConstants.WhiteA700)
//        )
//        .shadow(color: ColorConstants.Black90021, radius: 8, x: 3, y: -10)
//    }
//
//    func orderInfoRow(label: String, value: String, labelFont: Font = FontScheme.kRobotoRomanRegular(size: 14.relativeFontSize).weight(.regular), valueFont: Font = FontScheme.kInterMedium(size: 15.relativeFontSize).weight(.medium), topPadding: CGFloat = 0.0) -> some View {
//        HStack {
//            Text(label)
//                .font(labelFont)
//                .foregroundColor(ColorConstants.Black90001)
//                .multilineTextAlignment(.leading)
//            Spacer()
//            Text(value)
//                .font(valueFont)
//                .foregroundColor(ColorConstants.Blue300)
//                .multilineTextAlignment(.leading)
//        }
//        .padding(.top, topPadding)
//        .padding(.horizontal, getRelativeWidth(25.0))
//    }
//
//    var datePopUpView: some View {
//        SlideUpAnimationContainerView {
//            self.viewModel.updatePopUpType()
//        } content: {
//            VStack(spacing: 0.relativeHeight) {
//                CalendarSelectionView(selectedDate: $viewModel.selectedDate, disabledDates: .constant([]), onMonth: {}, onClose: {
//                    self.viewModel.updatePopUpType(nil)
//                })
//                .padding(.top)
//
//                Spacer()
//                Button(action: {
//                    self.viewModel.updatePopUpType(nil)
//
//                    Utilities.enQueue(after: .now() + 0.1) {
//                        self.viewModel.submittedDate = viewModel.selectedDate
//                        self.viewModel.isDateChanged = true
//                    }
//
//                }, label: {
//                    Text(StringConstants.kLblSave)
//                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
//                        .fontWeight(.bold)
//                        .padding(.horizontal, getRelativeWidth(30.0))
//                        .padding(.vertical, getRelativeHeight(7.0))
//                        .foregroundColor(ColorConstants.WhiteA700)
//                        .minimumScaleFactor(0.5)
//                        .multilineTextAlignment(.center)
//                        .frame(width: getRelativeWidth(129.0),
//                               height: getRelativeHeight(35.0), alignment: .center)
//                        .background(RoundedCorners(topLeft: 8, topRight: 8,
//                                                   bottomLeft: 8,
//                                                   bottomRight: 8)
//                                .fill(ColorConstants.Blue300))
//                        .padding(.bottom, 16.0.relativeHeight)
//                        .padding(.horizontal, getRelativeWidth(21.0))
//
//                })
//            }
//            .frame(height: 420.0.relativeHeight, alignment: .top)
//            .background(RoundedCorners(topLeft: 28.0, topRight: 28.0)
//                .fill(ColorConstants.WhiteA700))
//            .clipped()
//            .shadow(color: ColorConstants.Black9003f, radius: 7.6, x: 0, y: -7)
//        }
//        .transition(.asymmetric(
//            insertion: .move(edge: .bottom),
//            removal: .move(edge: .bottom)
//        ))
//    }
//
//    var addressPopUpView: some View {
//        SlideUpAnimationContainerView(perform: { self.viewModel.updatePopUpType() }) {
//            AddressPopUpView(onAddAddress: { value, _ in
//                viewModel.updateAddressModelRequest(value)
//                self.viewModel.updatePopUpType()
//            })
//        }
//        .transition(.asymmetric(
//            insertion: .move(edge: .bottom),
//            removal: .move(edge: .bottom)
//        ))
//    }
// }

struct CheckoutView: View {
    @StateObject var viewModel: CheckoutViewModel
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Namespace private var animation

    init(cartModel: CartModel) {
        self._viewModel = StateObject(wrappedValue: CheckoutViewModel(cartModel))
    }

    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            MainScrollBody(backButtonWithTitle: viewModel.cartModel.isRxList ? "Rx Checkout" : "Checkout") {
                VStack(alignment: .leading, spacing: 36) {
                    // Shipping Address Section
                    VStack(alignment: .leading, spacing: 8.relativeHeight) {
                        HStack {
                            Text("Shipping Address")
                                .font(.custom("Poppins", size: 18.relativeFontSize))
                                .fontWeight(.semibold)
                                .foregroundColor(.init(hex: "#909090"))
                                .frame(maxWidth: .infinity, alignment: .leading)

                            Button { viewModel.onDeliveryAddressSelection() } label: {
                                if viewModel.addressModelRequest != nil {
                                    Image(.editPencil)
                                        .resizable()
                                        .frame(width: 24.relativeFontSize, height: 24.relativeFontSize)
                                } else {
                                    Image(systemName: "plus.square")

                                        .resizable()
                                        .frame(width: 24.relativeFontSize, height: 24.relativeFontSize)
                                        .foregroundColor(.init(hex: "#242424"))
                                }
                            }
                        }

                        if let addressModelRequest = viewModel.addressModelRequest {
                            HStack {
                                VStack(alignment: .leading, spacing: 5) {
                                    Text(addressModelRequest.addressTitle.name)
                                        .font(.custom("Poppins", size: 18.relativeFontSize))
                                        .fontWeight(.bold)
                                        .foregroundColor(.init(hex: "#303030"))
                                        .frame(maxWidth: .infinity, alignment: .leading)
                                        .padding()

                                    Rectangle()
                                        .fill(Color(hex: "#F0F0F0"))
                                        .frame(height: 2.relativeHeight)

                                    Text(addressModelRequest.deliveryAddress)
                                        .font(.custom("Poppins", size: 14.relativeFontSize))
                                        .fontWeight(.regular)
                                        .foregroundColor(.init(hex: "#808080"))
                                        .frame(maxWidth: .infinity, alignment: .leading)
                                        .padding()
                                }
                            }

                            .background(RoundedRectangle(cornerRadius: 10).fill(Color.white).shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2))
                        }
                    }

                    // Delivery Date Section
                    VStack(alignment: .leading, spacing: 8.relativeHeight) {
                        Text("Delivery Date")
                            .font(.custom("Poppins", size: 18.relativeFontSize))
                            .fontWeight(.semibold)
                            .foregroundColor(.init(hex: "#909090"))

                        HStack {
                            Text(viewModel.deliveryDateString)
                                .font(.custom("Poppins", size: 12.relativeFontSize))
                                .fontWeight(.medium)
                                .foregroundColor(.init(hex: "#555E67"))
                            Spacer()

                            Button {
                                viewModel.updatePopUpType(.date)
                            } label: {
                                Image(.calendarWithBackground)
                                    .resizable()
                                    .frame(width: 37.relativeFontSize, height: 37.relativeFontSize)
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding(8)
                        .frame(height: 48.relativeHeight)
                        .background(RoundedRectangle(cornerRadius: 12).fill(Color(hex: "#FAFAFA")).shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 0))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color(hex: "#ECEDF0"), lineWidth: 1)
                        )
                    }

                    // Payment Method Section
                    VStack(alignment: .leading, spacing: 8.relativeHeight) {
                        Text("Payment method")
                            .font(.custom("Poppins", size: 18.relativeFontSize))
                            .fontWeight(.semibold)
                            .foregroundColor(.init(hex: "#909090"))

                        if let checkoutDetailsModel = viewModel.checkoutDetailsModel {
                            let paymentMethods = viewModel.paymentMethods.filter {
                                checkoutDetailsModel.paymentmethods.contains($0.key.lowercased())
                            }

                            VStack(spacing: 10.relativeHeight) {
                                ForEach(paymentMethods) { model in
                                    PaymentOptionView(model: model, isSelected: viewModel.selectedPaymentMethod == model)
                                        .onTapGesture {
                                            viewModel.updatePaymentMethod(model)
                                        }
                                }
                            }
                            .onAppear {
                                if let selectedMethod = paymentMethods.first {
                                    viewModel.updatePaymentMethod(selectedMethod)
                                }
                            }
                        }
                    }
                }
                .padding(.horizontal, 28)
                .padding(.vertical, 32)
            }
            .safeAreaInset(edge: .bottom, content: {
                SafeAreaViewSection

            })
            .background(ColorConstants.WhiteA700)
            .toolbarBackground(.visible, for: .tabBar)
            .overlay(alignment: .bottom) {
                Group {
                    switch viewModel.popUpType {
                    case .date:
                        datePopUpView
                    case .address:
                        addressPopUpView
                    case .tapPayment:
                        tapPaymentView
                    case .none:
                        EmptyView()
                    }
                }
                .animation(.easeOut, value: self.viewModel.popUpType)
            }
            .sheet(isPresented: $viewModel.showingTapCheckout) {
                if let checkoutDetails = viewModel.checkoutDetailsModel,
                   let addressModel = viewModel.addressModelRequest
                {
                    let paymentRequest = TapPaymentRequest(
                        amount: checkoutDetails.grandTotalValue,
                        currency: "KWD",
                        customerEmail: addressModel.email ?? "",
                        customerName: addressModel.fullName,
                        customerPhone: addressModel.phone,
                        items: checkoutDetails.cartItems.map { item in
                            TapPaymentItem(
                                title: item.productName,
                                description: "Product from Wasfa",
                                price: item.price,
                                quantity: item.quantity
                            )
                        },
                        description: "Order from Wasfa App"
                    )

                    TapCheckoutView(paymentRequest: paymentRequest) { result in

                        switch result {
                        case .success(let transactionId, let amount, let currency):
                            print("🚨 TapPaymentPopupView: Payment success - Transaction ID: \(transactionId)")
                            viewModel.showingTapCheckout = false // ✅ Dismiss sheet on success
                            Task {
                                await viewModel.handleSuccessfulTapPayment(
                                    transactionId: transactionId,
                                    amount: amount,
                                    currency: currency
                                )
                            }
                        case .failure(let error):

                            viewModel.showingTapCheckout = false // ✅ Dismiss Tap SDK sheet first

                            // Handle error and keep the popup open to show error message
                            viewModel.handleTapPaymentError(error)

                        case .cancelled:

                            viewModel.showingTapCheckout = false // ✅ Dismiss sheet on cancellation
                            // User cancelled, do nothing
                        }
                    }
                }
            }
        }
        .injectEnvironmentValues(viewModel)
    }

    var SafeAreaViewSection: some View {
        VStack(alignment: .center, spacing: 16.relativeHeight) {
            if let cartModel = viewModel.checkoutDetailsModel {
                // Order Summary Section
                VStack(alignment: .leading, spacing: 12.relativeHeight) {
                    orderInfoRow(label: "Order:", value: cartModel.subTotal)
                    orderInfoRow(label: "Discount:", value: cartModel.discount)
                    orderInfoRow(label: "Promo Code Discount:", value: cartModel.promotionDiscount)
                        .visibility(cartModel.promotionDiscount.isEmpty || cartModel.promotionDiscount == "KD 0.000" ? .gone : .visible)
                    orderInfoRow(label: "Delivery:", value: cartModel.shippingCost)
                    orderInfoRow(label: "Total:", value: cartModel.grandTotal)
                }
                .padding()
                .background(RoundedRectangle(cornerRadius: 10).fill(Color.white).shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2))

                Button(action: viewModel.onProceedCheckout, label: {
                    Text("Proceed to checkout")
                        .font(Font.custom("Poppins", size: 20.relativeFontSize))
                        .fontWeight(.semibold)
                        .padding(.horizontal, getRelativeWidth(30.0))
                        .padding(.vertical, getRelativeHeight(14.0))
                        .foregroundColor(ColorConstants.WhiteA700)
                        .multilineTextAlignment(.center)
                        .frame(maxWidth: .infinity, maxHeight: 60.relativeHeight)
                        .background(RoundedCorners(topLeft: 12.relativeFontSize, topRight: 12.relativeFontSize, bottomLeft: 12.relativeFontSize, bottomRight: 12.relativeFontSize)
                            .fill(ColorConstants.Blue300).shadow(color: ColorConstants.Black9003f, radius: 8, x: 0, y: 5))

                })
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.horizontal, 28)
        .padding(.bottom)
    }

    func orderInfoRow(label: String, value: String, labelFont: Font = FontScheme.kRobotoRomanRegular(size: 14.relativeFontSize).weight(.regular), valueFont: Font = FontScheme.kInterMedium(size: 15.relativeFontSize).weight(.medium), topPadding: CGFloat = 0.0) -> some View {
        HStack {
            Text(LocalizedStringKey(label))
                .font(.custom("Poppins", size: 18))
                .fontWeight(.regular)
                .foregroundColor(Color(hex: "#808080"))
            Spacer()
            Text(value)
                .font(.custom("Poppins", size: 18))
                .fontWeight(.semibold)
                .foregroundColor(Color(hex: "#242424"))
        }
    }

    var datePopUpView: some View {
        SlideUpAnimationContainerView {
            self.viewModel.updatePopUpType()
        } content: {
            VStack(spacing: 0.relativeHeight) {
                CalendarSelectionView(selectedDate: $viewModel.selectedDate, disabledDates: .constant([]), onMonth: {}, onClose: {
                    self.viewModel.updatePopUpType(nil)
                })
                .padding(.top)

                Spacer()
                Button(action: {
                    self.viewModel.updatePopUpType(nil)

                    Utilities.enQueue(after: .now() + 0.1) {
                        self.viewModel.submittedDate = viewModel.selectedDate
                        self.viewModel.isDateChanged = true
                    }

                }, label: {
                    Text(StringConstants.kLblSave)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.bold)
                        .padding(.horizontal, getRelativeWidth(30.0))
                        .padding(.vertical, getRelativeHeight(7.0))
                        .foregroundColor(ColorConstants.WhiteA700)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.center)
                        .frame(width: getRelativeWidth(129.0),
                               height: getRelativeHeight(35.0), alignment: .center)
                        .background(RoundedCorners(topLeft: 8, topRight: 8,
                                                   bottomLeft: 8,
                                                   bottomRight: 8)
                                .fill(ColorConstants.Blue300))
                        .padding(.bottom, 16.0.relativeHeight)
                        .padding(.horizontal, getRelativeWidth(21.0))

                })
            }
            .frame(height: 420.0.relativeHeight, alignment: .top)
            .background(RoundedCorners(topLeft: 28.0, topRight: 28.0)
                .fill(ColorConstants.WhiteA700))
            .clipped()
            .shadow(color: ColorConstants.Black9003f, radius: 7.6, x: 0, y: -7)
        }
        .transition(.asymmetric(
            insertion: .move(edge: .bottom),
            removal: .move(edge: .bottom)
        ))
    }

    var addressPopUpView: some View {
        SlideUpAnimationContainerView(perform: { self.viewModel.updatePopUpType() }) {
            AddressPopUpView(onAddAddress: { value, _ in
                viewModel.updateAddressModelRequest(value)
                self.viewModel.updatePopUpType()
            })
        }
        .transition(.asymmetric(
            insertion: .move(edge: .bottom),
            removal: .move(edge: .bottom)
        ))
    }

    var tapPaymentView: some View {
        SlideUpAnimationContainerView(perform: { self.viewModel.updatePopUpType() }) {
            TapPaymentPopupView(viewModel: viewModel)
        }
        .transition(.asymmetric(
            insertion: .move(edge: .bottom),
            removal: .move(edge: .bottom)
        ))
    }
}

struct SlideUpAnimationContainerView<Content>: View where Content: View {
    let perform: () -> Void
    let content: () -> Content

    var body: some View {
        ZStack(alignment: .bottom) {
            ZStack {}
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(ColorConstants.Black90021)
                .transition(.opacity)
                .onTapGesture(perform: self.perform)
                .transaction { transaction in
                    transaction.animation = nil
                }
            self.content()
        }
    }
}
