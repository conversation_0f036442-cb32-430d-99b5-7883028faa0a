import SwiftUI

struct ProductcardCartCell: View {
    let model: CartModel.CartItem
    var divider: Bool = true
    var isRx:Bool = false
    let onQuantityChange: (_ cartID : Int, _ quantity : Int) -> Void
    let onRemove: (_ cartID: Int) -> Void
    @State private var quantity: Int = 1
   

    /// Updates the quantity with debounce logic
    func updateQuantity() {
        DispatchQueue.debounce(delay: 0.5, key: "\(model.id)_quantity") {
            onQuantityChange(model.id ,quantity )
        }
    }

    var increment: Void {
        if model.currentStock > quantity {
            quantity += 1
            updateQuantity()
        }
    }

    var decrement: Void {
        if quantity > 1 {
            quantity -= 1
            updateQuantity()
        }
    }

    var body: some View {
        VStack(spacing: 16.relativeHeight) {
            HStack(alignment: .top,spacing: 12) {
                // Product Image
                NetworkImageView(path: model.productThumbnailImage)
                    .padding(8)
                    .blendMode(.multiply)
                    .frame(width: 105.relativeWidth, height: 100.relativeHeight)
                    .background(Color(red: 0.94, green: 0.94, blue: 0.94, opacity: 0.54))
                    .clipShape(RoundedRectangle(cornerRadius: 10.relativeFontSize))
                
                // Product Details
                VStack(alignment: .leading, spacing: 16.relativeHeight) {
                    HStack(alignment: .top) {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(model.productName)
                                .font(Font.custom("Poppins", size: 14.relativeFontSize).weight(.medium))
                                .foregroundColor(.init(hex: "#999999"))
//                                .lineLimit(1)
                            
                            HStack{
                                Text(model.unitPrice)
                                    .font(Font.custom("Poppins", size: 17.relativeFontSize).weight(.bold))
                                    .foregroundColor(ColorConstants.Black90001)
                                
                                if let strokedPrice = model.strokedPrice {
                                    Text(strokedPrice)
                                        .font(Font.custom("Poppins", size: 12.relativeFontSize))
                                        .strikethrough()
                                        .fontWeight(.regular)
                                        .foregroundColor(ColorConstants.Gray500)
                //                            .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.leading)
                                }
                            }
                        }.frame(maxWidth: .infinity, alignment: .leading)
                        
                        // Remove Button
                        Button(action: {
                            onRemove(model.id)
                        }) {
                            Image(systemName: "xmark.circle")
                               
                                .font(.system(size: 24, weight: .medium))
                                .foregroundColor(.init(hex: "#242424"))
                        }
                        .visibility(isRx ? .gone : .visible)
                    }
                    
                    // Quantity Controls
                    HStack {
                        quantityPickerView
                            .disableWithOpacity(model.currentStock < 1)
                        
                        NetworkImageView(path: model.sellerLogo, contentMode: .fill)
                            .blendMode(.multiply)
                            .frame(width: 36.relativeWidth, height: 36.relativeHeight)
                            .background(Color(red: 0.94, green: 0.94, blue: 0.94, opacity: 0.54))
                            .clipShape(Circle())
                    }
                }
                    
//                Spacer()
            }
            
            if divider { Divider()}
        }
        .padding(12)
        .background(RoundedRectangle(cornerRadius: 12).fill(ColorConstants.WhiteA700))
        .onAppear {
            quantity = model.quantity
           
        }
    }
    
    private var quantityPickerView: some View {
        HStack(spacing: 10.relativeWidth) {
            Button { self.decrement } label: {
                Image(systemName: "minus")
                    .fontWeight(.semibold)
                    .foregroundStyle(.primary)
                    .frame(width: 30, height: 30)
                    .background(Color(hex: "#DBEFF9"))
                    .clipShape(.rect(cornerRadius: 8))
            }
            
            Text(String(format: "%02d", self.quantity))
                .font(Font.custom("Poppins", size: 18).weight(.medium))
                .foregroundColor(ColorConstants.Black90001)
                .contentTransition(.numericText())
                .animation(.bouncy, value: self.quantity)
//                        .frame(width: 10)
            
            Button { self.increment } label: {
                Image(systemName: "plus")
                    .fontWeight(.semibold)
                    .foregroundStyle(.primary)
                    .frame(width: 30, height: 30)
                    .background(Color(hex: "#DBEFF9"))
                    .clipShape(.rect(cornerRadius: 8))
            }
        }
        .disableWithOpacity(model.quantity < 1)
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(.top, getRelativeHeight(4.0))
        .scaleEffect(1.05, anchor: .leading)
    }
}

#Preview {
    let model = CartModel.CartItem(id: 1, productID: 1, productName: "Sample Product", productThumbnailImage: "https://stagging.apixrx.com/public/uploads/all/D1221556-1.jpg", sellerLogo: "https://stagging.apixrx.com/public/uploads/all/EphYCNbIE8eb3M1iUIEWxPRU2pVHPpXG9LdWbpBd.jpg", variation: "Size M", price: 29.99, currentStock: 10, unitPrice: "$29.99", currencySymbol: "$", strikedPrice: "$29.99", shippingCost: 5, quantity: 1)
    
    ProductcardCartCell(model: model, onQuantityChange: { _, _ in
        
    }, onRemove: { _ in
        
    })
}

extension DispatchQueue {
    private static var debounceTracker = [String: DispatchWorkItem]()

    /// Debounces a closure, ensuring it executes only after a delay.
    /// - Parameters:
    ///   - delay: The delay time in seconds.
    ///   - key: A unique key for tracking debounce tasks.
    ///   - action: The closure to debounce.
    static func debounce(delay: TimeInterval, key: String, action: @escaping () -> Void) {
        // Cancel any existing work item
        debounceTracker[key]?.cancel()

        // Create a new work item
        let workItem = DispatchWorkItem(block: action)
        debounceTracker[key] = workItem

        // Execute after delay
        DispatchQueue.main.asyncAfter(deadline: .now() + delay, execute: workItem)
    }
}
