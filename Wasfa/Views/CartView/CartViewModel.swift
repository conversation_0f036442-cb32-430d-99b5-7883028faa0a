import Foundation
import SwiftUI

@MainActor
class CartViewModel: SuperViewModel {
    // MARK: - Published Properties
    @Published private(set) var cartModel: CartModel?
    @Published var promoCode: String = ""
    @Published private(set) var promoCodeModel: PromoCodeModel?
   let isRx: Bool
    // MARK: - Initialization
    init(isRx: Bool = false) {
        self.isRx = isRx
        super.init()
        getCartList()
    }
    
  

    // MARK: - Public Methods
    func onUpdatePromoCode() {
        if promoCodeModel != nil {
            removePromoCode()
        } else {
            applyPromoCode()
        }
    }

    func onRemoveCartItem(_ id: Int) {
        let parameters: [String: Any] = ["id": id, "is_rx_list": isRx ? 1 : 0]
        onApiCall(api.removeCart, parameters: parameters) {
            if $0.success {
                self.getCartList()
                self.appState?.updateCartQuantity(self.cartModel?.cartItems.count)
            }
        }
    }
    
    func onUpdateCartQuantity(_ productID: Int, _ quantity: Int) {
        let parameters: [String: Any] = ["id": productID, "quantity": quantity, "is_rx_list": isRx ? 1 : 0]
        onApiCall(api.changeQuantity, parameters: parameters, withLoadingIndicator: false) {
            if $0.success { self.getCartList(withLoadingIndicator: false) }
        }
    }

    func onProceedToCheckout() {
        guard var cartModel = cartModel else { return }
        cartModel.isRx = isRx
        routerManager?.push(to: .checkout(model: cartModel), appState: appState)
    }

    // MARK: - Private Methods
    private func applyPromoCode() {
        let parameters: [String: Any] = ["promoCode": promoCode]
        onApiCall(api.applyPromoCode, parameters: parameters) {
            if $0.success {
                self.promoCodeModel = $0.data
//                self.getCartList()
                if let grandTotal = $0.data?.grandTotal, let couponDiscount = $0.data?.promotionDiscount {
                    self.cartModel?.grandTotal = grandTotal
                    self.cartModel?.couponDiscount = couponDiscount
                    
                }
                self.appState?.showToast(.init(type: .success, message: $0.message))
            }
        }
    }

    private func removePromoCode() {
        onApiCall(api.removePromoCode, parameters: emptyDictionary) {
            if $0.success {
                self.promoCodeModel = nil
                self.promoCode = ""
                self.getCartList()
                self.appState?.showToast(.init(type: .success, message: $0.message))
            }
        }
    }

    private func getCartList(withLoadingIndicator: Bool = true) {
        onApiCall(api.carts, parameters: ["is_rx_list": isRx ? 1 : 0], withLoadingIndicator: withLoadingIndicator) {
            self.cartModel = $0.data
            self.appState?.updateCartQuantity(self.cartModel?.cartItems.count)
        }
    }
}
