//
//  ExpandableSheetView.swift
//  Wasfa
//
//  Created by Apple on 04/01/2025.
//

import SwiftUI

struct ExpandableSheetView<Content: View>: View {
    @Binding var isExpanded: Bool
    let content: Content

    // Heights for expanded and collapsed states
    private let fullHeight: CGFloat
    private let minHeight: CGFloat
    private let hideDragIndicator: Bool

    // State to track the current height
    @State private var currentHeight: CGFloat
    @GestureState private var dragOffset: CGFloat = 0

    init(isExpanded: Binding<Bool>, fullHeight: CGFloat, minHeight: CGFloat, hideDragIndicator: Bool = false, @ViewBuilder content: () -> Content) {
        self._isExpanded = isExpanded
        self.fullHeight = fullHeight
        self.minHeight = minHeight
        self.hideDragIndicator = hideDragIndicator
        self.content = content()
        self._currentHeight = State(initialValue: isExpanded.wrappedValue ? fullHeight : minHeight)
    }

    var body: some View {
        VStack {
            Spacer()

            VStack(spacing: 0) {
                // Drag Handle
                if !hideDragIndicator {
                    HStack {
                        Capsule()
                            .fill(Color.gray.opacity(0.5))
                            .frame(width: 40, height: 6)
                            .padding(8)
                    }
                    
                    .frame(maxWidth: .infinity, maxHeight: 16)
                    .background(.white)
                    .gesture(
                        DragGesture()
                            .updating($dragOffset) { value, state, _ in
                                state = value.translation.height
                            }
                            .onChanged { value in
                                // Adjust the current height dynamically based on drag position
                                let newHeight = currentHeight - value.translation.height
                                currentHeight = max(min(newHeight, fullHeight + 25), minHeight - 25)

                                
                            }
                            .onEnded { _ in
                                // Snap back to valid bounds with animation
                                withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                                    if currentHeight > (fullHeight + minHeight) / 2 {
                                        currentHeight = fullHeight
                                            isExpanded = true
                                    } else {
                                        currentHeight = minHeight
                                            isExpanded = false
                                    }
                                }
                            }
                    )
                }
                
                
                
               

                // Sheet Content
                content
                    .frame(maxHeight: max(min(currentHeight, fullHeight + 25), minHeight - 25), alignment: .bottom) // Allow extra stretch
                    .clipped()
            }

            .background(Color.white)
            .cornerRadius(16)
            .shadow(color: Color.black.opacity(0.1), radius: 6)
        }
        .frame(maxHeight: isExpanded ? fullHeight + 32 : minHeight + 32, alignment: .bottom)
//        .transition(.move(edge: .bottom))
//        .animation(.easeInOut, value: currentHeight)
    }
}

struct ContentView: View {
    @State private var isExpanded = true
    private let fullHeight: CGFloat = UIScreen.main.bounds.height * 0.4
    private let minHeight: CGFloat = UIScreen.main.bounds.height * 0.12
    var body: some View {
        ZStack {
            Color.blue.opacity(0.2).ignoresSafeArea()

            ExpandableSheetView(isExpanded: $isExpanded, fullHeight: fullHeight, minHeight: minHeight) {
                VStack {
                    if isExpanded {
                        Text("This is a custom expandable sheet")
                            .font(.headline)
                            .foregroundColor(.black)
                            .transition(.opacity.combined(with: .scale))
                    }

                    Button(action: {
                        withAnimation {
                            isExpanded.toggle()
                        }
                    }) {
                        Text(isExpanded ? "Collapse" : "Expand")
                            .foregroundColor(.blue)
                            .padding()
                            .background(Color.gray.opacity(0.2))
                            .cornerRadius(8)
                    }
                }

                .animation(.bouncy, value: isExpanded)
            }
        }
    }
}

#Preview(body: {
    ContentView()
})
