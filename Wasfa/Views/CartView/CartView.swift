import SwiftUI

struct CartView: View {
    @StateObject var viewModel:CartViewModel
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @State private var isExpanded = true
    
    init(isRx:Bool = false) {
        self._viewModel = StateObject(wrappedValue: CartViewModel(isRx: isRx))
    }
    
    private let fullHeight: CGFloat =  232.relativeHeight
    private let minHeight: CGFloat = 80
    var body: some View {
        GeometryReader { proxy in
            let size = proxy.size
            SuperView(pageState: $viewModel.pageState) {
                MainScrollBody(backButtonWithTitle: viewModel.isRx ? "Rx Cart" : "My Cart") {
                    if
                        let cartList = viewModel.cartModel?.cartItems,
                        !cartList.isEmpty
                    
                    {
                        LazyVStack(spacing: -8) {
                            ForEach(cartList) {
                                ProductcardCartCell(model: $0, divider: cartList.last?.id != $0.id,isRx: viewModel.isRx, onQuantityChange: viewModel.onUpdateCartQuantity, onRemove: viewModel.onRemoveCartItem)
                            }
                        }
                            
                        .frame(width: getRelativeWidth(357.0), alignment: .center)
                        .padding(.leading, getRelativeWidth(18.0))
                        .padding(.trailing, getRelativeWidth(15.0))
                        .padding(.top, getRelativeHeight(16.0))
                        .padding(.bottom, getRelativeHeight(10.0))
                        .background(ColorConstants.WhiteA700)
                    } else {
                        VStack(alignment: .center) {
                            ContentUnavailableView(viewModel.isRx ? "Your Rx cart is empty" : "Your cart is empty", systemImage: "cart", description: Text(viewModel.isRx ? "" : "Add some products to your cart to see them here."))
                        }
                        .frame(width: size.width, height: size.height)
                    }
                }
                
                .safeAreaInset(edge: .bottom) {
                    SafeAreaViewSection
                }
                .animation(.bouncy, value: isExpanded)
                .toolbarBackground(.visible, for: .tabBar)
            }
            
        }.injectEnvironmentValues(viewModel)
    }
    @FocusState private var focused: Bool
    var SafeAreaViewSection: some View {
        ExpandableSheetView(isExpanded: .constant(true), fullHeight: viewModel.isRx ? 160.relativeHeight : fullHeight, minHeight: minHeight, hideDragIndicator: true) {
            VStack(alignment: .leading) {
                if let cartModel = viewModel.cartModel {
                    Group {
                        HStack(spacing: 0) {
                            TextField("Enter your promo code", text: $viewModel.promoCode)
                                .font(Font.custom("Poppins", size: 16.relativeFontSize).weight(.regular))
                                .foregroundColor( viewModel.promoCodeModel != nil ? .green : ColorConstants.Black900)
                                .padding(.horizontal, 16)
                                .padding(.trailing, 38)
                                .frame(height: 44.relativeHeight)
                                .background(RoundedRectangle(cornerRadius: 8).fill(ColorConstants.WhiteA700).shadow(color: ColorConstants.Black9000f, radius: 2, x: 0, y: 2))
//                                .overlay(
//                                    RoundedRectangle(cornerRadius: 8)
//                                        .stroke(ColorConstants.Gray200, lineWidth: 1)
//                                )
                                .disabled(viewModel.promoCodeModel != nil)
                                .overlay(alignment: .trailing) {
                                    Button(action: viewModel.onUpdatePromoCode) {
                                        Image(systemName: viewModel.promoCodeModel != nil ? "xmark.circle" : "chevron.right")
                                            .font(.system(size: 18, weight: .bold))
                                            .foregroundColor(ColorConstants.WhiteA700)
                                            .frame(width: 44.relativeWidth, height: 44.relativeHeight)
                                            .background(RoundedRectangle(cornerRadius: 8).fill(ColorConstants.Blue300))
                                    }
                                    .disableWithOpacity(viewModel.promoCode.isEmpty)
                                }
                        }
                        .visibility(viewModel.isRx ? .gone : .visible)
                        .padding(.horizontal, 20)
                        .padding(.top, 32.relativeHeight)
                        Spacer()
                        Group {
                            if let couponDiscount = cartModel.couponDiscount {
                                orderInfoRow(label: "Promo Code Discount:", value: couponDiscount, labelFont: Font.custom("Poppins", size: 16.relativeFontSize).bold(), valueFont: Font.custom("Poppins", size: 16.relativeFontSize), topPadding: 6.0)
                            }
                            orderInfoRow(label: "Total:", value: cartModel.grandTotal, labelFont: Font.custom("Poppins", size: 20.relativeFontSize).bold(), valueFont: Font.custom("Poppins", size: 20.relativeFontSize).bold(), topPadding: 6.0)
                                .padding(.bottom)
                        }
                    }
                       
                    .frame(maxHeight: isExpanded ? fullHeight : minHeight, alignment: .bottom)
                    
                    Button(action: viewModel.onProceedToCheckout, label: {
                        Text(isExpanded ? LocalizedStringKey("Check Out") :  LocalizedStringKey(cartModel.grandTotal) )
                            .font(Font.custom("Poppins", size: 20.relativeFontSize))
                            .fontWeight(.semibold)
                            .padding(.horizontal, getRelativeWidth(30.0))
                            .padding(.vertical, getRelativeHeight(14.0))
                            .foregroundColor(ColorConstants.WhiteA700)
                            .multilineTextAlignment(.center)
                            .frame(maxWidth: .infinity, maxHeight: 60.relativeHeight)
                            .background(RoundedCorners(topLeft: 12.relativeFontSize, topRight: 12.relativeFontSize,bottomLeft: 12.relativeFontSize,bottomRight: 12.relativeFontSize)
                                    .fill(ColorConstants.Blue300).shadow(color: ColorConstants.Black9003f, radius: 8, x: 0,
                                                                         y: 5))
                            .padding(.horizontal, 20.relativeWidth)
                            
                    })
                    .frame(maxWidth: .infinity)
                    .padding(.bottom)
                }
            }
        }
        .visibility((viewModel.cartModel?.cartItems.isEmpty ?? true) ? .gone : .visible)
    }
    
    func orderInfoRow(label: String, value: String, labelFont: Font = FontScheme.kRobotoRomanRegular(size: 14.relativeFontSize).weight(.regular), valueFont: Font = FontScheme.kInterMedium(size: 15.relativeFontSize).weight(.medium), topPadding: CGFloat = 0.0) -> some View {
        HStack {
            Text(LocalizedStringKey(label))
                .font(labelFont)
                .foregroundColor(.init(hex: "#808080"))
                .multilineTextAlignment(.leading)

            Spacer()
            Text(value)
                .font(valueFont)
                .foregroundColor(.init(hex: "#303030"))
                .multilineTextAlignment(.leading)
                .contentTransition(.numericText())
                .animation(.bouncy, value: value)
               
        }
        .padding(.top, topPadding)
        .padding(.horizontal, getRelativeWidth(25.0))
    }
}

#Preview {
    NavigationStack {
        CartView()
    }
}
