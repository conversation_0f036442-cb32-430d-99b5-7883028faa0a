import SwiftUI

struct MyAccountView: View {
    @StateObject var viewModel = MyAccountViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
   
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            MainScrollBody(backButtonWithTitle: StringConstants.kLblMyAccount, hideBackButton: true) {
                VStack {
                    profileHeader
                    
                    VStack(spacing: 28.relativeHeight) {
                        settingsOption(icon: "img_account_configuration", title: "Account", action: viewModel.onAccountSettings)
                        settingsOption(icon: "img_order_list", title: StringConstants.kLblOrderHistory, action: viewModel.onOrderHistory)
                        
                        settingsOption(icon: "account.pressure", title: "RX Details", action: viewModel.onRxDetails)
                        
                        settingsOption(icon: "img_chat_alt", title: StringConstants.kMsgDeliveryAddresses, action: viewModel.onDeliveryAddresses)
                        
                        settingsOption(icon: "img_favorite_light_blue_600_28x28", title: StringConstants.kLblWishlist, action: viewModel.onWishlist)
                        
//                        settingsOption(icon: "img_unlock_light", title: StringConstants.kLblChangePassword, action: viewModel.onChangePassword)
                        logoutButton
                    }
                    .background(ColorConstants.WhiteA700)
                    .padding(.top, 30)
                    .padding(.bottom, 10)
                }
                .padding()
                .background(ColorConstants.WhiteA700)
                .alert("Logout", isPresented: $viewModel.showLogoutAlert) {
                    Button("Cancel", role: .cancel) {}
                    Button("Logout", role: .destructive, action: viewModel.onLogout)
                } message: {
                    Text("Are you sure you want to logout?")
                }
            }
        }
        .injectEnvironmentValues(viewModel)
    }
    
    // MARK: - Profile Header View

    private var profileHeader: some View {
        VStack(spacing: 15) {
            HStack {
                Image(systemName: "person.circle")
                    .resizable()
                    .frame(width: getRelativeWidth(70.0), height: getRelativeWidth(70.0))
                    .clipShape(Circle())
                    .foregroundStyle(ColorConstants.BlueGray10002)
                    
                VStack(alignment: .leading) {
                    Text(viewModel.profileDetailsModel?.name ?? "")
                        .font(Font.custom("Poppins", size: 16.relativeFontSize))
                        .fontWeight(.semibold)
                        .foregroundColor(ColorConstants.Black90001)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    
                    Text(viewModel.profileDetailsModel?.email ?? "")
                        .font(Font.custom("Poppins", size: 12.relativeFontSize))
                        .fontWeight(.regular)
                        .foregroundColor(ColorConstants.Black90001)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
                .frame(maxWidth: .infinity)
                .padding(.leading, 16)
                
                
                
//                Button(action: viewModel.onEditUserDetails) {
//                    Image("img_expand_right_light")
//                        .frame(width: 24, height: 24)
//                        
//                }
            }
            .padding(.horizontal, 16)
            
//            Divider().background(ColorConstants.Black9000f)
        }
    }
    
    // MARK: - Settings Option View

    private func settingsOption(icon: String, title: LocalizedStringKey, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            VStack {
                HStack {
//                    Image(icon)
//                        .resizable()
//                        .scaledToFit()
//                        .frame(width: 28, height: 28)
//                        .padding(6)
//                        .background(UnevenRoundedRectangle(topLeadingRadius: 8, bottomLeadingRadius: 8, bottomTrailingRadius: 8, topTrailingRadius: 8, style: .continuous).fill(ColorConstants.Blue60054))
                        
                    Text(title)
                        .font(Font.custom("Poppins", size: 16.relativeFontSize))
                        .fontWeight(.semibold)
                        .foregroundColor(ColorConstants.Black90001)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(.leading, 16)
                        
                    Image("img_expand_right_light")
                        .rotateBasedOnLanguage()
                        .frame(width: 24, height: 24)
                }
                .frame(height: 26.relativeHeight)
                .background(RoundedCorners().fill(ColorConstants.WhiteA700))
                .padding(.horizontal, 8)
//                .overlay(Divider().background(ColorConstants.Black9000f), alignment: .bottom)
            }
        }
    }
    
    // MARK: - Logout Button

    private var logoutButton: some View {
        HStack(spacing: 4) {
            Button(action: viewModel.updateLogoutAlertToggle, label: {
                Image(.accountLogout)
                
                Text(StringConstants.kLblLogOut)
                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                    .foregroundColor(ColorConstants.RedA700)
            })
        }
        .padding(.leading, 20)
        .frame(height: 24)
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(.vertical, 59)
    }
}

#Preview {
    NavigationStack {
        MyAccountView().attachAllEnvironmentObjects()
    }
}
