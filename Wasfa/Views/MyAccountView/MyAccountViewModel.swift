import Foundation
import SwiftUI

class MyAccountViewModel: SuperViewModel {
    @Published var nextScreen: String? = nil

    @Published var showLogoutAlert: Bool = false
    @Published var profileDetailsModel: ProfileDetailsModel?
    
    override init() {
        super.init()
        getUserDetails()
    }
    
    func updateLogoutAlertToggle() {
        showLogoutAlert.toggle()
    }
    
    func onEditUserDetails(){
        routerManager?.push(to: .accountSettings, appState: appState)
    }
    
    func getUserDetails() {
        onApiCall(api.profileDetails, parameters: emptyDictionary) {
            self.profileDetailsModel = $0.data?.first
            AppState.user = $0.data?.first
        }
    }

    func onLogout() {
        onApiCall(api.logout, parameters: ["device_id": AppState.deviceID]) { _ in
            self.appState?.onLogout()
        }
    }
    
    func onAccountSettings() {
        routerManager?.push(to: .accountSettings, appState: appState)
    }
    
    func onOrderHistory() {
        routerManager?.push(to: .orderHistory, appState: appState)
    }
    
    func onRxDetails() {
        routerManager?.push(to: .rxListing, appState: appState)
    }
    
    func onDeliveryAddresses() {
        routerManager?.push(to: .deliveryAddress(), appState: appState)
    }
    
    func onWishlist() {
        routerManager?.push(to: .wishlist, appState: appState)
    }
    
    func onChangePassword() {
//        routerManager?.push(to: .changePassword, appState: appState)
    }
}
