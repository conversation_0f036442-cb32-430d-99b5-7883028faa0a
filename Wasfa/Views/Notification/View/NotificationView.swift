//
//  NotificationView.swift
//  Wasfa
//
//  Created by Apple on 23/02/2025.
//

import SwiftUI

struct NotificationView: View {
    @StateObject private var viewModel: NotificationViewModel = .init()
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            MainScrollBody(backButtonWithTitle: "Notifications") {
                LazyVStack(spacing: 8, pinnedViews: [.sectionHeaders]) {
                    if viewModel.notifications.isEmpty {
                        ContentUnavailableView(
                            "Notifications are not available yet",
                            systemImage: "bell.badge",
                            description: Text("There are no data found at the moment. Please check back later.")
                        )
                        .padding()
                    } else {
                        let groupedNotifications = Dictionary(grouping: viewModel.notifications) { notification in
                            notification.date.toFormattedDate(inputFormat: "dd-MM-yyyy hh:mm a", outputFormat: "dd MMMM yyyy")
                        }

                        let sortedDates = groupedNotifications.keys.compactMap { dateString in
                            dateString.toDate(format: "dd MMMM yyyy")
                        }.sorted(by: >) // Sort dates in descending order (most recent first)

                        ForEach(sortedDates, id: \.self) { date in
                            let dateString = date.toFormattedString(format: "dd MMMM yyyy")
                            let headerTitle = {
                                if Calendar.current.isDateInToday(date) {
                                    return "Today"
                                } else if Calendar.current.isDateInYesterday(date) {
                                    return "Yesterday"
                                } else {
                                    return dateString
                                }
                            }()

                            Section {
                                ForEach(groupedNotifications[dateString] ?? []) { notification in
                                    NotificationCardView(model: notification)
                                }
                            } header: {
                                Text(headerTitle)
                                    .font(.custom("Poppins", size: 16.relativeFontSize))
                                    .fontWeight(.bold)
                                    .foregroundColor(Color(hex: "#1B9ED9"))
                                    .padding()
                                    .padding(.leading, 8)
                                    .padding(.bottom, -8)
                                    .padding(.top)
                                    .frame(maxWidth: .infinity, alignment: .leading)
                                    .background(ColorConstants.WhiteA700)
                            }
                        }
                    }
                }
            }
            .background(ColorConstants.WhiteA700)
        }
    }
}

// #Preview {
//    NavigationStack {}
// }

struct NotificationCardView: View {
    let model: NotificationModel
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Image(.notificationBell)
                .resizable()
                .scaledToFit()
                .frame(width: 16.relativeFontSize, height: 16.relativeFontSize)
                .foregroundColor(ColorConstants.Blue300)
                .padding(.vertical, 8)

            VStack(alignment: .leading, spacing: 4) {
                Text(model.message)
                    .font(.custom("Poppins", size: 14.relativeFontSize))
                    .fontWeight(.regular)
                    .foregroundColor(Color(hex: "#091C3F"))

                Text(model.date.toFormattedDate(inputFormat: "dd-MM-yyyy hh:mm a", outputFormat: "hh:mm a"))
                    .font(.custom("Poppins", size: 13.relativeFontSize))
                    .fontWeight(.regular)
                    .foregroundColor(Color(hex: "#091C3F").opacity(0.6))
            }
            Spacer()
        }
        .padding()
        .background(Color.white)
//        .padding(.top, 8)
        .overlay(Divider()
            .background(ColorConstants.BlueGray10001)
            .frame(height: 1)
            .padding(.horizontal, -12), alignment: .top)
        .padding(.horizontal, 8)
    }
}

extension String {
    func toDate(format: String) -> Date? {
        let formatter = DateFormatter()
        formatter.dateFormat = format
        return formatter.date(from: self)
    }

    func toFormattedString(format: String) -> String {
        guard let date = self.toDate(format: format) else { return self }
        let formatter = DateFormatter()
        formatter.dateFormat = format
        return formatter.string(from: date)
    }

}

extension Date {
    func toFormattedString(format: String) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = format
        return formatter.string(from: self)
    }
}

