//
//  NotificationViewModel.swift
//  Wasfa
//
//  Created by Apple on 23/02/2025.
//

import Foundation

class NotificationViewModel: SuperViewModel {
    @Published private(set) var notifications: [NotificationModel] = []
    
    override init() {
        super.init()
        getNotifications()
    }
    
    func getNotifications() {
        onApiCall(api.notifications, parameters: emptyDictionary) {
            self.notifications = $0.data ?? []
        }
    }
}
