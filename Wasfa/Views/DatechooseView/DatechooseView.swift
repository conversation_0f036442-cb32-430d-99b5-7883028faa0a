import SwiftUI

struct DatechooseView: View {
    @StateObject var datechooseViewModel = DatechooseViewModel(_isOpen: .constant(false))
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    var body: some View {
        VStack {
            ScrollView(.vertical, showsIndicators: false) {
                VStack {
                    HStack {
                        Text(StringConstants.kMsgChooseDelivery)
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.Black90001)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(166.0), height: getRelativeHeight(22.0),
                                   alignment: .topLeading)
                            .padding(.top, getRelativeHeight(5.0))
                        Button(action: {}, label: {
                            Image("img_close_round_light")
                        })
                        .frame(width: getRelativeWidth(24.0), height: getRelativeWidth(24.0),
                               alignment: .center)
                        .padding(.leading, getRelativeWidth(166.0))
                    }
                    .frame(width: getRelativeWidth(356.0), height: getRelativeHeight(27.0),
                           alignment: .center)
                    .padding(.top, getRelativeHeight(4.0))
                    CalendarModuleView(firstDate: $datechooseViewModel.calendarStartDate,
                                       lastDate: $datechooseViewModel.calendarEndDate)
                        .frame(width: getRelativeWidth(349.0), height: getRelativeHeight(291.0),
                               alignment: .center)
                        .padding(.top, getRelativeHeight(14.0))
                    Button(action: {}, label: {
                        HStack(spacing: 0) {
                            Text(StringConstants.kLblSave)
                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                .fontWeight(.bold)
                                .padding(.horizontal, getRelativeWidth(30.0))
                                .padding(.vertical, getRelativeHeight(7.0))
                                .foregroundColor(ColorConstants.WhiteA700)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.center)
                                .frame(width: getRelativeWidth(129.0),
                                       height: getRelativeHeight(35.0), alignment: .center)
                                .background(RoundedCorners(topLeft: 8.0, topRight: 8.0,
                                                           bottomLeft: 8.0, bottomRight: 8.0)
                                        .fill(ColorConstants.Blue300))
                                .padding(.top, getRelativeHeight(22.0))
                        }
                    })
                    .frame(width: getRelativeWidth(129.0), height: getRelativeHeight(35.0),
                           alignment: .center)
                    .background(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0,
                                               bottomRight: 8.0)
                            .fill(ColorConstants.Blue300))
                    .padding(.top, getRelativeHeight(22.0))
                }
                .frame(width: UIScreen.main.bounds.width, alignment: .topLeading)
                .background(RoundedCorners(topLeft: 28.0, topRight: 28.0)
                    .fill(ColorConstants.WhiteA700))
            }
        }
        .frame(width: UIScreen.main.bounds.width)
        .hideNavigationBar()
    }
}

struct DatechooseView_Previews: PreviewProvider {
    static var previews: some View {
        DatechooseView()
    }
}
