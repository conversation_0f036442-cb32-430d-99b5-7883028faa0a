//
//  TabExtension.swift
//  Wasfa
//
//  Created by Apple on 20/09/2024.
//

import SwiftUI

extension Tab {
    @ViewBuilder
    var view: some View {
        switch self {
        case .home:
            HomeView()
        case .category:
            CategoryView()
        case .wishlist:
            WishlistView()
        case .myAccount:
            AuthView()
        }
    }
}

struct AuthView: View {
    var body: some View {
        if AppState.isLoggedIn {
            MyAccountView()
        } else {
            SignInView()
        }
    }
}
