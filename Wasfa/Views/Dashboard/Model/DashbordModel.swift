//
//  DashbordModel.swift
//  Wasfa
//
//  Created by Apple on 20/09/2024.
//

import SwiftUI

enum Tab: Identifiable, CaseIterable, Hashable {
    case home, category, wishlist, myAccount

    var id: UUID {
        switch self {
        case .home,
             .category,
             .wishlist,
             .myAccount
             : return UUID()
        }
    }

    var image: ImageResource {
        switch self {
        case .home: .imgHomeLightGray400
        case .category: .imgDarhboard
        case .wishlist: .imgFavoriteLight
        case .myAccount: .imgUser
        }
    }

    var title: String {
        let title: String = {
            switch self {
            case .home: return "Home"
            case .category: return "Category"
            case .wishlist: return "Wishlist"
            case .myAccount: return "Account"
            }
        }()

        return title
    }

    var index: Int {
        return Tab.allCases.firstIndex(of: self) ?? 0
    }
}

// HomeView()
//    .tabItem {
//        Image(.imgHomeLightGray400)
//            .renderingMode(.template)
//
//
//    }.tag(1)
// CategoryView()
//    .tabItem {
//        Image(.imgDarhboard)
//            .renderingMode(.template)
//
//    }.tag(2)
// DemoView(content: "Tab Content 3")
//    .tabItem {
//        Image(.imgFavoriteLight)
//            .renderingMode(.template)
//
//    }.tag(3)
// MyAccountView()
//    .tabItem {
//        Image(.imgUser)
//            .renderingMode(.template)
//
//    }.tag(4)
