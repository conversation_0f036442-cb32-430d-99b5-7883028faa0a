//
//  TabView.swift
//  Wasfa
//
//  Created by Apple on 16/09/2024.
//

import SwiftUI

struct DashboardView: View {
    @Environment(\.routerManager) var routerManager: RouterManager
    @EnvironmentObject var appState: AppState
    @State private var selection: Int = 1
    var body: some View {
        ZStack {
            TabView(selection: selectionBinding) {
                ForEach(Tab.allCases, id: \.self) { type in
                    NavigationStack(path: getRouterManagerPath(type)) {
                        type.view.navigationDestination(for: Route.self) { $0 }
                            .id(appState.rootViewId)
                    }
                    .tabItem {
                        Image(type.image)
                            .resizable()
                            .renderingMode(.template)
                            .frame(width: 24, height: 24)
                        //                    Text(LocalizedStringKey(type.title))
                    }
                    .if(type == .wishlist) { $0.badge(appState.wishlistCount) }
                    .tag(type)
                }
            }
            .tint(ColorConstants.Blue300)
           
          

            SideMenu(content: AnyView(MenuView()))
        }.onLoad {
            configureTabBarBadgeOffset()
        }
    }

    // MARK: - Configure Badge Offset for Tab Bar

    func configureTabBarBadgeOffset() {
        let appearance = UITabBarAppearance()

        // Set badge position offset
        let tabBarItemAppearance = UITabBarItemAppearance()
        tabBarItemAppearance.normal.badgePositionAdjustment = UIOffset(horizontal: 2, vertical: 2) // Adjust badge offset

        appearance.stackedLayoutAppearance = tabBarItemAppearance
        appearance.inlineLayoutAppearance = tabBarItemAppearance
        appearance.compactInlineLayoutAppearance = tabBarItemAppearance

        UITabBar.appearance().standardAppearance = appearance
        UITabBar.appearance().scrollEdgeAppearance = appearance
    }
}

#Preview {
    DashboardView()
}

struct DemoView: View {
    let content: String
    var body: some View {
        NavigationStack {
            GeometryReader(content: { geometry in
                let size = geometry.size
                ScrollView {
                    ForEach(1 ... 50, id: \.self) { index in

                        Text("\(content) \(index)")
                    }
                }
                .scrollIndicators(.hidden)
                .navigationTitle(content)
                .navigationBarTitleDisplayMode(.large)
                .frame(width: size.width, height: size.height)
                //            .background(.orange)

            })
        }
    }
}
