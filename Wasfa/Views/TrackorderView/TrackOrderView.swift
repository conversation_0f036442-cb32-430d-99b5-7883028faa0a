import SwiftUI

struct TrackOrderView: View {
    @StateObject var viewModel: TrackorderViewModel

    init(orderID: Int) {
        self._viewModel = StateObject(wrappedValue: TrackorderViewModel(orderID: orderID))
    }

    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            MainScrollBody(backButtonWithTitle: "Order Tracking") {
                if let orderDetails = viewModel.orderDetails {
                    VStack(alignment: .leading, spacing: 0) {
                        VStack(alignment: .leading, spacing: 0) {
                            LazyVStack(spacing: 16) {
                                ForEach(orderDetails.itemsList) { model in
                                    VStack{
                                        HStack {
                                            Text("Order#: \(orderDetails.code)")
                                                .font(FontScheme.kRobotoRomanMedium(size: getRelativeHeight(12.0)))
                                                .fontWeight(.medium)
                                                .foregroundColor(ColorConstants.WhiteA700)
                                               
                                                .multilineTextAlignment(.leading)
                                                .frame(height: getRelativeHeight(15.0), alignment: .topLeading)
                                            Spacer()

                                            Button {
                                                viewModel.onCancelOrderProduction(model.id)
                                            } label: {
                                                Text("Cancel")
                                                    .font(FontScheme.kRobotoRomanRegular(size: getRelativeHeight(11.0)))
                                                    .fontWeight(.regular)
                                                    .foregroundColor(ColorConstants.WhiteA700)
                                                    .multilineTextAlignment(.leading)
                                                    .frame(
                                                        height: getRelativeHeight(13.0), alignment: .topLeading)
                                            }
                                            .disableWithOpacity(viewModel.disableCancelButton)
                                        }
                                        .padding(.horizontal, getRelativeWidth(8.0))
                                        .frame(height: getRelativeHeight(32.0),
                                               alignment: .leading)
                                        .overlay(RoundedCorners(topLeft: 8.0, topRight: 8.0)
                                            .stroke(ColorConstants.Teal900, lineWidth: 1))
                                        .background(RoundedCorners(topLeft: 8.0, topRight: 8.0)
                                            .fill(ColorConstants.Blue600))
                                        HStack {
                                            ZStack {
                                                NetworkImageView(path: model.thumbnailImage)
                                                    .frame(width: getRelativeWidth(46.0),
                                                           height: getRelativeHeight(49.0), alignment: .center)

                                                    .padding(.bottom, getRelativeHeight(3.14))
                                                    .padding(.horizontal, getRelativeWidth(2.86))
                                            }

                                            .frame(width: getRelativeWidth(52.0), height: getRelativeHeight(55.0),
                                                   alignment: .top)
                                            .overlay(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0,
                                                                    bottomRight: 8.0)
                                                    .stroke(ColorConstants.Teal900,
                                                            lineWidth: 1))
                                            .background(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0,
                                                                       bottomRight: 8.0)
                                                    .fill(Color.clear.opacity(0.7)))
                                            VStack(alignment: .leading, spacing: 0) {
                                                Text(model.productName)
                                                    .font(FontScheme
                                                        .kRobotoRomanMedium(size: getRelativeHeight(13.0)))
                                                    .fontWeight(.medium)
                                                    .foregroundColor(ColorConstants.Black90001)
                                                    .minimumScaleFactor(0.5)
                                                    .multilineTextAlignment(.leading)
                                                    .frame(width: getRelativeWidth(247.0),
                                                           height: getRelativeHeight(34.0), alignment: .topLeading)
                                                    .padding(.leading, getRelativeWidth(6.0))
                                                Text(model.price)
                                                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                                    .fontWeight(.bold)
                                                    .foregroundColor(ColorConstants.Blue600)
                                                    .minimumScaleFactor(0.5)
                                                    .multilineTextAlignment(.leading)
                                                    .frame(width: getRelativeWidth(65.0),
                                                           height: getRelativeHeight(20.0), alignment: .topLeading)
                                                    .padding(.top, getRelativeHeight(9.0))
                                                    .padding(.leading, getRelativeWidth(6.0))
                                            }
                                            
                                            .padding(.leading, getRelativeWidth(6.0))
                                        }
                                    }
                                    .padding(.bottom, getRelativeHeight(8.0))
                                    .overlay(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0,
                                                            bottomRight: 8.0)
                                            .stroke(ColorConstants.Black90001,
                                                    lineWidth: 1))
                                    .background(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0,
                                                               bottomRight: 8.0)
                                            .fill(ColorConstants.WhiteA700))

                                   
                                }
                            }.padding(.vertical)
                        }

                        Text(StringConstants.kLblTrackOrder)
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.Black90001)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(89.0), height: getRelativeHeight(22.0),
                                   alignment: .topLeading)
                            .padding(.top, getRelativeHeight(15.0))

                        TrackOrderSubView(trackingList: viewModel.orderDetails?.tracking ?? [])
                        
                        
                        Button(action: {
                            guard let orderID = viewModel.orderDetails?.id else { return }
                            viewModel.onCancelOrder(orderID)
                        }, label: {
                            HStack(spacing: 0) {
                                Text(StringConstants.kLblCancelOrder)
                                    .font(FontScheme
                                        .kNunitoBold(size: getRelativeHeight(18.0)))
                                    .fontWeight(.bold)
                                    .padding(.horizontal, getRelativeWidth(28.0))
                                    .padding(.vertical, getRelativeHeight(14.0))
                                    .foregroundColor(ColorConstants.Blue300)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(169.0),
                                           height: getRelativeHeight(54.0),
                                           alignment: .topLeading)
                                    .overlay(RoundedCorners(topLeft: 8.0, topRight: 8.0,
                                                            bottomLeft: 8.0,
                                                            bottomRight: 8.0)
                                            .stroke(ColorConstants.Blue300,
                                                    lineWidth: 1))
                                    .background(RoundedCorners(topLeft: 8.0, topRight: 8.0,
                                                               bottomLeft: 8.0,
                                                               bottomRight: 8.0)
                                            .fill(ColorConstants.WhiteA700).shadow(color: ColorConstants.Black9003f, radius: 4,
                                                                                   x: 0, y: 0))
                            }
                        })
                        .padding([.horizontal, .vertical], 8)
                        .padding(.top)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .disableWithOpacity(viewModel.disableCancelButton)
                    }
                    .padding(.vertical, getRelativeHeight(16.0))
                    .padding(.horizontal, getRelativeWidth(20.0))
                }
            }
           
            .background(ColorConstants.WhiteA700)
            .alert("Cancel Order", isPresented: $viewModel.showCancelAlert) {
                TextField("Enter reason for cancellation", text: $viewModel.cancelReason, axis: .vertical)
                    .lineLimit(3 ... 6)
                Button("Cancel", role: .cancel) {
                    viewModel.showCancelAlert = false
                }
                Button("Confirm") {
                    viewModel.confirmCancelOrder()
                }
                .disableWithOpacity(viewModel.cancelReason.isEmpty)
            } message: {
                Text("Please provide a reason for cancelling this order")
            }
        }
        .injectEnvironmentValues(viewModel)
    }
}

#Preview {
    NavigationStack {
        TrackOrderView(orderID: 32).attachAllEnvironmentObjects()
    }
}
