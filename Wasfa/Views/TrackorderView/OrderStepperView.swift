//
//  OrderStepperView.swift
//  Wasfa
//
//  Created by Apple on 19/02/2025.
//

import SwiftUI

// MARK: - Order Step Model

struct OrderStep: Identifiable {
    let id = UUID()
    let title: String
    var description: String
    let icon: String
    var date: String?
    let status: StepStatus
}

// Enum for step status
enum StepStatus {
    case completed, active, pending
}

// MARK: - Stepper View

struct OrderStepperView: View {
    // Constants
    private enum Constants {
        static let lineHeight: CGFloat = 45.relativeFontSize
        static let dotSize: CGFloat = 14.relativeFontSize
        static let iconSize: CGFloat = 44.relativeFontSize
        static let spacing: CGFloat = 20.relativeFontSize
        static let contentSpacing: CGFloat = 16.relativeFontSize
    }

    let steps: [OrderStep]
    
    


    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            ForEach(steps.indices, id: \.self) { index in
                HStack(alignment: .center, spacing: Constants.spacing) {
                    // Vertical Line
                    VStack(spacing: 0) {
                        VStack(spacing: 0) {
                            Rectangle()
                                .fill(index < 1 ? .clear : steps[index].status != .pending ? ColorConstants.Blue600 : ColorConstants.BlueGray10001)
                                .frame(width: 3)
                            Rectangle()
                                .fill((steps.count - 2) < index ? .clear : steps[index].status == .completed ? ColorConstants.Blue600 : ColorConstants.BlueGray10001)
                                .frame(width: 3)
                        }
                        .overlay {
                            Circle()
                                .fill(colorForStep(steps[index].status))
                                .frame(width: Constants.dotSize, height: Constants.dotSize)
                        }
                    }

                    HStack(spacing: Constants.contentSpacing) {
                        // Status Icon

                        Image(steps[index].icon)
                            .renderingMode(.template)
                            .foregroundColor(.white)
                            .frame(width: Constants.iconSize, height: Constants.iconSize)
                            .background(Circle().fill(colorForStep(steps[index].status)))

                        // Order Step Details
                        VStack(alignment: .leading, spacing: 4) {
                            Text(LocalizedStringKey(steps[index].title))
                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                .fontWeight(.bold)
                                .foregroundColor(steps[index].status == .active ? ColorConstants.Blue600: ColorConstants.Black90001)

                            Text(LocalizedStringKey(steps[index].description))
                                .font(FontScheme
                                    .kRobotoRomanLight(size: getRelativeHeight(11.0)))
                                .fontWeight(.light)
                               
                                .foregroundColor(steps[index].status == .active ? ColorConstants.Blue600 : ColorConstants.Gray50003)
                        }
                        .accessibilityElement(children: .combine)
                        .accessibilityLabel("\(steps[index].title): \(steps[index].description)")
                    }
                }
            }
        }
        .frame(minHeight: CGFloat(steps.count) * 85)
        .padding(.leading, 16)
    }

    // Determines the color of the step based on status
    private func colorForStep(_ status: StepStatus) -> Color {
        switch status {
        case .completed, .active:
            return ColorConstants.Blue600
        case .pending:
            return ColorConstants.BlueGray10001
        }
    }
}

// MARK: - Order Tracking View

struct TrackOrderSubView: View {
    let trackingList: [Tracking]
    
    var orderSteps: [OrderStep] {
        // Define regular steps
        let regularSteps = [
            ("created", "Order placed", "We have received your order", "img_icon"),
            ("confirmed", "Order confirmed", "Your order has been confirmed", "img_confirmation_order"),
            ("picked_up", "Order picked up", "Your order has been picked up", "img_order_papers"),
            ("on_the_way", "On the way", "Your order is on the way", "img_delivery_truck"),
            ("delivered", "Delivered", "Your order has been delivered", "img_food_delivery")
            
            
           
        ]
        
        // Check if order is cancelled
        let isCancelled = trackingList.contains { $0.status == "cancelled" }
        
        // If cancelled, only show Order Placed and Cancelled status
        let relevantSteps = isCancelled ? [
            ("created", "Order placed", "We have received your order", "img_icon"),
            ("cancelled", "Cancelled", "Your order has been cancelled", "img_food_delivery")
            
            
        ] : regularSteps
        
        // Convert to OrderSteps with appropriate status
        return relevantSteps.enumerated().map { index, step in
            let (stepStatus, title, baseDescription, icon) = step
            let tracking = trackingList.first { $0.status == stepStatus }
            let description = tracking.map { "\(baseDescription)\non \($0.formattedCreatedAt)" } ?? baseDescription
            
            let status: StepStatus
            if isCancelled {
                // For cancelled orders, both steps should be completed
                status = .completed
            } else {
                // For regular orders, determine status based on current progress
                let currentStatusIndex = trackingList.last.map { current in
                    regularSteps.firstIndex { $0.0 == current.status } ?? -1
                } ?? -1
                
                if index < currentStatusIndex {
                    status = .completed
                } else if index == currentStatusIndex {
                    status = .active
                } else {
                    status = .pending
                }
            }
            
            return OrderStep(
                title: title,
                description: description,
                icon: icon,
                date: tracking?.formattedCreatedAt,
                status: status
            )
        }
    }

    var body: some View {
        OrderStepperView(steps: orderSteps)
            
    }
    
  
}

// MARK: - Preview

struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        TrackOrderSubView(trackingList: [.init(status: "created", formattedCreatedAt: "19-02-2025 10:24:AM")])
    }
}



