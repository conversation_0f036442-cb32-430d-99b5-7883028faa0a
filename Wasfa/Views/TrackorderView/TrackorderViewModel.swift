import Foundation
import SwiftUI

class TrackorderViewModel: SuperViewModel {
    @Published var orderDetails: OrderDetailsModel?
    
    // New state variables for cancel alert
    @Published var showCancelAlert = false
    @Published var cancelReason = ""
    @Published private var orderIdToCancel: Int?
    
    let orderID: Int

    init(orderID: Int) {
        self.orderID = orderID
        super.init()
        self.getOrderDetails()
    }

    var disableCancelButton: Bool {
        self.orderDetails?.tracking.contains(where: { $0.status == "cancelled" }) ?? false
    }

    func getOrderDetails() {
        onApiCall(api.orderDetails, parameters: ["id": self.orderID]) {
            self.orderDetails = $0.data?.first

//            self.changeTrackingDetailsForTesting()
        }
    }
    
    private var isProductCancel: Bool = false

    func onCancelOrder(_ id: Int) {
        self.isProductCancel = false
        self.orderIdToCancel = id
        self.cancelReason = ""
        self.showCancelAlert = true
    }
    
    func onCancelOrderProduction(_ id: Int) {
        self.isProductCancel = true
        self.orderIdToCancel = id
        self.cancelReason = ""
        self.showCancelAlert = true
    }
    
    func confirmCancelOrder() {
        guard let id = orderIdToCancel, !cancelReason.isEmpty else { return }
        
        if self.isProductCancel {
            self.cancelProductOrder(id)
        } else {
            self.cancelWholeOrder(id)
        }
    }
    
    func cancelProductOrder(_ id: Int) {
        onApiCall(api.cancelItem(parameters:completionHandler:), parameters: ["id": id, "reason": self.cancelReason]) { response in
            if response.success {
                self.appState?.showToast(.init(type: .success, message: response.message))
                self.routerManager?.goBack(appState: self.appState)
            }
            self.showCancelAlert = false
            self.orderIdToCancel = nil
            self.cancelReason = ""
        }
    }

    func cancelWholeOrder(_ id: Int) {
        onApiCall(api.cancelOrder, parameters: ["id": id, "reason": self.cancelReason]) { response in
            if response.success {
                self.appState?.showToast(.init(type: .success, message: response.message))
                self.routerManager?.goBack(appState: self.appState)
            }
            self.showCancelAlert = false
            self.orderIdToCancel = nil
            self.cancelReason = ""
        }
    }
    
    func changeTrackingDetailsForTesting() {
        let tracking: [Tracking] = [
            .init(status: "created", formattedCreatedAt: "19-02-2025 10:24:AM"),
            .init(status: "confirmed", formattedCreatedAt: "19-02-2025 10:24:AM"),
            .init(status: "picked_up", formattedCreatedAt: "19-02-2025 10:24:AM"),
            .init(status: "cancelled", formattedCreatedAt: "19-02-2025 10:24:AM"),
        ]

        self.orderDetails?.tracking = tracking
    }
}
