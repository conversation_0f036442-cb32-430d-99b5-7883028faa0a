//import SwiftUI
//
//struct Productcard3Cell: View {
//    var body: some View {
//        VStack {
//            ZStack(alignment: .center) {
//                Image("img_613xc1bj1dl_1_123x157")
//                    .resizable()
//                    .frame(width: getRelativeWidth(155.0), height: getRelativeHeight(123.0),
//                           alignment: .leading)
//                    .scaledToFit()
//                    .cornerRadius(11.0)
//                    .padding(.horizontal, getRelativeWidth(3.25))
//                VStack(alignment: .leading, spacing: 0) {
//                    Image("img_favorite")
//                        .resizable()
//                        .frame(width: getRelativeWidth(24.0), height: getRelativeWidth(26.0),
//                               alignment: .leading)
//                        .scaledToFit()
//                    VStack {
//                        Text(StringConstants.kLblSoldOut)
//                            .font(FontScheme.kNunitoLight(size: getRelativeHeight(9.0)))
//                            .fontWeight(.light)
//                            .foregroundColor(ColorConstants.WhiteA700)
//                            .minimumScaleFactor(0.5)
//                            .multilineTextAlignment(.leading)
//                            .frame(width: getRelativeWidth(33.0), height: getRelativeHeight(13.0),
//                                   alignment: .leading)
//                    }
//                    .frame(width: getRelativeWidth(48.0), height: getRelativeHeight(14.0),
//                           alignment: .leading)
//                    .background(RoundedCorners(topLeft: 6.0, topRight: 6.0, bottomLeft: 6.0,
//                                               bottomRight: 6.0)
//                            .fill(ColorConstants.Red600))
//                    .padding(.leading, getRelativeWidth(99.0))
//                }
//                .frame(width: getRelativeWidth(148.0), height: getRelativeHeight(35.0),
//                       alignment: .leading)
//                .padding(.bottom, getRelativeHeight(82.57))
//                .padding(.horizontal, getRelativeWidth(6.51))
//            }
//            .hideNavigationBar()
//            .frame(width: getRelativeWidth(161.0), height: getRelativeHeight(123.0),
//                   alignment: .leading)
//            .background(RoundedCorners(topLeft: 11.0, topRight: 11.0).fill(ColorConstants.Gray50))
//            Text(StringConstants.kMsgAnivageneVials)
//                .font(FontScheme.kNunitoLight(size: getRelativeHeight(13.0)))
//                .fontWeight(.light)
//                .foregroundColor(ColorConstants.Black90001)
//                .minimumScaleFactor(0.5)
//                .multilineTextAlignment(.center)
//                .frame(width: getRelativeWidth(148.0), height: getRelativeHeight(46.0),
//                       alignment: .center)
//                .padding(.top, getRelativeHeight(5.0))
//            HStack {
//                Text(StringConstants.kLblKd28840)
//                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
//                    .fontWeight(.bold)
//                    .foregroundColor(ColorConstants.Black90001)
//                    .minimumScaleFactor(0.5)
//                    .multilineTextAlignment(.center)
//                    .frame(width: getRelativeWidth(40.0), height: getRelativeHeight(30.0),
//                           alignment: .center)
//                    .padding(.bottom, getRelativeHeight(16.0))
//                Button(action: {}, label: {
//                    Image("img_group_737")
//                })
//                .frame(width: getRelativeWidth(28.0), height: getRelativeWidth(30.0),
//                       alignment: .center)
//                .background(RoundedCorners(topLeft: 5.0, topRight: 5.0, bottomLeft: 5.0,
//                                           bottomRight: 5.0)
//                        .fill(ColorConstants.Blue300))
//                .shadow(color: ColorConstants.Black90054, radius: 2, x: 0, y: 1)
//                .padding(.top, getRelativeHeight(15.0))
//                .padding(.leading, getRelativeWidth(27.0))
//            }
//            .frame(width: getRelativeWidth(97.0), height: getRelativeHeight(46.0),
//                   alignment: .leading)
//            .padding(.top, getRelativeHeight(10.0))
//        }
//        .frame(width: getRelativeWidth(174.0), alignment: .leading)
//        .background(RoundedCorners(topLeft: 11.0, topRight: 11.0, bottomLeft: 11.0,
//                                   bottomRight: 11.0)
//                .fill(ColorConstants.WhiteA700))
//        .shadow(color: ColorConstants.Black90023, radius: 5, x: 0, y: 0)
//        .hideNavigationBar()
//    }
//}
//
///* struct Productcard3Cell_Previews: PreviewProvider {
//
// static var previews: some View {
// 			Productcard3Cell()
// }
// } */
