import SwiftUI

struct ProductListingView: View {
    @StateObject var viewModel: ProductpageTabContainerViewModel

    private let columns: [GridItem] = [GridItem(), GridItem()]

    var body: some View {
        VStack(alignment: .center) {
            if !self.viewModel.productList.isEmpty {
                Section {
                    LazyVGrid(columns: self.columns, spacing: 15.relativeHeight) {
                        ForEach(self.viewModel.productList) { model in
                            ProductCardCell(model: model, onSelect: self.viewModel.onSelectProduct, onAddToCart: viewModel.onAddToCart, onAddToWishlist: viewModel.onAddToWishlist)
                                .onAppear { self.viewModel.loadMore(model: model) }
                                .frame(maxHeight: .infinity, alignment: .top)
                        }
                    }
                } footer: {
                    // Loading indicator
                    if self.viewModel.isLoading {
                        ProgressView("Loading...")

                            .padding(.vertical, 10)
                    }

                    // No more items indicator
                    if !self.viewModel.hasMorePages && !self.viewModel.isLoading {
                        Text("\(self.viewModel.productList.count) of \(self.viewModel.totalCount) products loaded")
                            .font(.footnote)
                            .foregroundColor(.gray)
                            .padding(.vertical, 10)
                    }
                }

            } else {
                // No product available ContentUnavailableView Placeholder
                ContentUnavailableView("No Products Available", systemImage: "exclamationmark.triangle", description: Text("Please try again later."))
            }
        }
        .padding(.horizontal, getRelativeWidth(16.0))
        .background(ColorConstants.WhiteA700)
    }
}

// struct ProductpageView_Previews: PreviewProvider {
//    static var previews: some View {
//        ProductpageView()
//    }
// }

extension DispatchQueue {
    private static var debounceWorkItems: [String: DispatchWorkItem] = [:]

    static func debounce(delay: TimeInterval, identifier: String, action: @escaping () -> Void) {
        if let existingWorkItem = debounceWorkItems[identifier] {
            existingWorkItem.cancel()
        }

        let workItem = DispatchWorkItem(block: action)
        self.debounceWorkItems[identifier] = workItem

        DispatchQueue.main.asyncAfter(deadline: .now() + delay, execute: workItem)
    }
}
