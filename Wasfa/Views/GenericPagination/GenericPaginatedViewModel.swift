//
//  GenericPaginatedViewModel.swift
//  Wasfa
//
//  Created by Apple on 01/01/2025.
//

import Foundation
import Combine

class GenericPaginatedViewModel<Item: Identifiable>: ObservableObject {
    @Published var items: [Item] = []
    @Published var isLoading: Bool = false
    @Published var hasMorePages: Bool = true

    private var currentPage: Int = 1
    private let pageSize: Int
    private let fetchItems: (Int, Int) -> AnyPublisher<[Item], Error>

    private var cancellables = Set<AnyCancellable>()

    init(pageSize: Int = 10, fetchItems: @escaping (Int, Int) -> AnyPublisher<[Item], Error>) {
        self.pageSize = pageSize
        self.fetchItems = fetchItems
    }

    func loadNextPage() {
        guard !isLoading && hasMorePages else { return }

        isLoading = true
        fetchItems(currentPage, pageSize)
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { [weak self] completion in
                guard let self = self else { return }
                self.isLoading = false
                if case .failure = completion {
                    self.hasMorePages = false
                }
            }, receiveValue: { [weak self] newItems in
                guard let self = self else { return }
                self.items.append(contentsOf: newItems)
                self.hasMorePages = newItems.count >= self.pageSize
                self.currentPage += 1
            })
            .store(in: &cancellables)
    }
}
