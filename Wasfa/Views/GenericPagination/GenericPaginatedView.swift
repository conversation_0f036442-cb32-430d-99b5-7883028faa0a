//
//  GenericPaginatedView.swift
//  Wasfa
//
//  Created by Apple on 01/01/2025.
//

import SwiftUI
import Combine

struct GenericPaginatedView<Item: Identifiable, Content: View>: View {
    @ObservedObject var viewModel: GenericPaginatedViewModel<Item>
    let content: (Item) -> Content
    let layout: AnyView

    init(
        viewModel: GenericPaginatedViewModel<Item>,
        layout: @escaping () -> AnyView,
        @ViewBuilder content: @escaping (Item) -> Content
    ) {
        self.viewModel = viewModel
        self.content = content
        self.layout = layout()
    }

    var body: some View {
        VStack {
            if !viewModel.items.isEmpty {
                layout
                    .environmentObject(viewModel)
            } else if viewModel.isLoading {
                ProgressView("Loading...")
            } else {
                ContentUnavailableView("No Items Available", systemImage: "exclamationmark.triangle", description: Text("Please try again later."))
            }
        }
        .onAppear {
            viewModel.loadNextPage()
        }
    }
}

