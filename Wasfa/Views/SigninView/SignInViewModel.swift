import Foundation
import SwiftUI

class SignInViewModel: SuperViewModel {
    @Published var phoneText: String = .init()
    @Published var otpText: String = .init()

    @Published var selectedCountryCode: CountryCode = .kuwait

    override init() {
        super.init()

//        phoneText = "+1555123"
    }

    func sendOtp() {
        let phoneWithCountryCode = selectedCountryCode.dialCode + phoneText
        let parameters: [String: Any] = ["phone": phoneWithCountryCode]
        onApiCall(api.sendOtp, parameters: parameters) { _ in
            self.routerManager?.push(to: .otpVerification(phoneWithCountryCode), appState: self.appState)
        }
    }

    func verifyOtp(phoneText: String) {
        let parameters: [String: Any] = ["phone": phoneText, "otp": otpText]
        onApiCall(api.verifyOtp, parameters: parameters) { [weak self] response in
            self?.saveFCMTokenToBackend {
                self?.appState?.onLogin(message: response.message, isResetRootID: true)
                self?.routerManager?.popToAllRoot()
            }
            
            
        }
    }

    var disableSendOtp: Bool {
        otpText.count < 5
    }

    func saveFCMTokenToBackend(onComplete: @escaping () -> Void) {
        onApiCall(api.saveFcmToken, parameters: ["deviceToken": AppState.fcmToken]) {
            if $0.success {
                onComplete()
            }
        }
    }
}

// extension SignInViewModel: Hashable {
//    static func == (lhs: SignInViewModel, rhs: SignInViewModel) -> Bool {
//        ObjectIdentifier(lhs) == ObjectIdentifier(rhs)
//    }
//
//    func hash(into hasher: inout Hasher) {
//        hasher.combine(ObjectIdentifier(self))
//    }
// }
