//import SwiftUI
//
//struct ForgotpasswordView: View {
//    @StateObject var forgotpasswordViewModel = ForgotpasswordViewModel()
//    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
//    var body: some View {
//        VStack {
//            VStack {
//                HStack {
//                    HStack {
//                        Button(action: {}, label: {
//                            Image("img_vector_9")
//                        })
//                        .frame(width: getRelativeWidth(24.0), height: getRelativeWidth(24.0),
//                               alignment: .center)
//                        .padding(.bottom, getRelativeHeight(4.0))
//                        Text(StringConstants.kLblForgotPassword)
//                            .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(16.0)))
//                            .fontWeight(.heavy)
//                            .foregroundColor(ColorConstants.Black90001)
//                            .minimumScaleFactor(0.5)
//                            .multilineTextAlignment(.leading)
//                            .frame(width: getRelativeWidth(129.0), height: getRelativeHeight(22.0),
//                                   alignment: .topLeading)
//                            .padding(.leading, getRelativeWidth(20.0))
//                        Image("img_search_light")
//                            .resizable()
//                            .frame(width: getRelativeWidth(27.0), height: getRelativeWidth(27.0),
//                                   alignment: .center)
//                            .scaledToFit()
//                            .clipped()
//                            .padding(.leading, getRelativeWidth(36.0))
//                        Image("img_bell_pin_light")
//                            .resizable()
//                            .frame(width: getRelativeWidth(27.0), height: getRelativeWidth(27.0),
//                                   alignment: .center)
//                            .scaledToFit()
//                            .clipped()
//                            .padding(.leading, getRelativeWidth(33.0))
//                        ZStack(alignment: .topLeading) {
//                            Image("img_vector")
//                                .resizable()
//                                .frame(width: getRelativeWidth(18.0),
//                                       height: getRelativeHeight(15.0), alignment: .center)
//                                .scaledToFit()
//                                .clipped()
//                                .padding(.top, getRelativeHeight(6.85))
//                                .padding(.trailing, getRelativeWidth(11.0))
//                            Image("img_vector_gray_900_03")
//                                .resizable()
//                                .frame(width: getRelativeWidth(9.0), height: getRelativeHeight(4.0),
//                                       alignment: .center)
//                                .scaledToFit()
//                                .clipped()
//                                .padding(.bottom, getRelativeHeight(15.88))
//                                .padding(.trailing, getRelativeWidth(15.06))
//                            Image("img_vector_gray_900_03_1x1")
//                                .resizable()
//                                .frame(width: getRelativeWidth(1.0), height: getRelativeWidth(1.0),
//                                       alignment: .center)
//                                .scaledToFit()
//                                .clipped()
//                                .padding(.top, getRelativeHeight(11.31))
//                                .padding(.trailing, getRelativeWidth(21.65))
//                            HStack {
//                                Image("img_vector_gray_900_03_1x1")
//                                    .resizable()
//                                    .frame(width: getRelativeWidth(1.0),
//                                           height: getRelativeWidth(1.0), alignment: .center)
//                                    .scaledToFit()
//                                    .clipped()
//                                    .padding(.vertical, getRelativeHeight(11.0))
//                                ZStack(alignment: .topTrailing) {
//                                    ZStack {}
//                                        .hideNavigationBar()
//                                        .frame(width: getRelativeWidth(16.0),
//                                               height: getRelativeWidth(16.0), alignment: .center)
//                                        .background(RoundedCorners(topLeft: 8.0, topRight: 8.0,
//                                                                   bottomLeft: 8.0,
//                                                                   bottomRight: 8.0)
//                                                .fill(ColorConstants.Blue300))
//                                    Text(StringConstants.kLbl2)
//                                        .font(FontScheme
//                                            .kInterRegular(size: getRelativeHeight(11.0)))
//                                        .fontWeight(.regular)
//                                        .foregroundColor(ColorConstants.WhiteA700)
//                                        .minimumScaleFactor(0.5)
//                                        .multilineTextAlignment(.leading)
//                                        .frame(width: getRelativeWidth(6.0),
//                                               height: getRelativeHeight(14.0),
//                                               alignment: .topLeading)
//                                        .padding(.leading, getRelativeWidth(5.33))
//                                }
//                                .hideNavigationBar()
//                                .frame(width: getRelativeWidth(16.0),
//                                       height: getRelativeWidth(16.0), alignment: .center)
//                            }
//                            .frame(width: getRelativeWidth(16.0), height: getRelativeWidth(16.0),
//                                   alignment: .topTrailing)
//                            .padding(.bottom, getRelativeHeight(6.0))
//                            .padding(.leading, getRelativeWidth(12.42))
//                        }
//                        .hideNavigationBar()
//                        .frame(width: getRelativeWidth(29.0), height: getRelativeHeight(22.0),
//                               alignment: .center)
//                        .padding(.leading, getRelativeWidth(29.0))
//                    }
//                    .frame(width: getRelativeWidth(355.0), height: getRelativeHeight(28.0),
//                           alignment: .leading)
//                    .padding(.leading, getRelativeWidth(19.0))
//                    .padding(.trailing, getRelativeWidth(16.0))
//                }
//                .frame(width: UIScreen.main.bounds.width - 20, height: getRelativeHeight(73.0),
//                       alignment: .leading)
//                .background(ColorConstants.WhiteA700)
//                .shadow(color: ColorConstants.Black90021, radius: 14, x: 0, y: 6)
//                VStack {
//                    Image("img_wasfa_logo_1")
//                        .resizable()
//                        .frame(width: getRelativeWidth(170.0), height: getRelativeHeight(69.0),
//                               alignment: .center)
//                        .scaledToFit()
//                        .clipped()
//                    Text(StringConstants.kMsgDonTWorryIt)
//                        .font(FontScheme.kRobotoRomanRegular(size: getRelativeHeight(13.0)))
//                        .fontWeight(.regular)
//                        .foregroundColor(ColorConstants.Black90001)
//                        .minimumScaleFactor(0.5)
//                        .multilineTextAlignment(.leading)
//                        .frame(width: getRelativeWidth(247.0), height: getRelativeHeight(36.0),
//                               alignment: .topLeading)
//                        .padding(.top, getRelativeHeight(22.0))
//                        .padding(.leading, getRelativeWidth(61.0))
//                        .padding(.trailing, getRelativeWidth(41.0))
//                    VStack(alignment: .leading, spacing: 0) {
//                        Text(StringConstants.kLblEmail2)
//                            .font(FontScheme.kRobotoRomanMedium(size: getRelativeHeight(14.0)))
//                            .fontWeight(.medium)
//                            .foregroundColor(ColorConstants.Black90001)
//                            .minimumScaleFactor(0.5)
//                            .multilineTextAlignment(.leading)
//                            .frame(width: getRelativeWidth(37.0), height: getRelativeHeight(17.0),
//                                   alignment: .topLeading)
//                            .padding(.leading, getRelativeWidth(8.0))
//                        HStack {
//                            TextField(StringConstants.kMsgExambleGamilCom,
//                                      text: $forgotpasswordViewModel.emailText)
//                                .font(FontScheme.kRobotoRomanMedium(size: getRelativeHeight(12.0)))
//                                .foregroundColor(ColorConstants.Gray500C4)
//                                .padding()
//                        }
//                        .frame(width: getRelativeWidth(347.0), height: getRelativeHeight(55.0),
//                               alignment: .leading)
//                        .overlay(RoundedCorners(topLeft: 15.0, topRight: 15.0, bottomLeft: 15.0,
//                                                bottomRight: 15.0)
//                                .stroke(ColorConstants.Black90001,
//                                        lineWidth: 1))
//                        .background(RoundedCorners(topLeft: 15.0, topRight: 15.0, bottomLeft: 15.0,
//                                                   bottomRight: 15.0)
//                                .fill(ColorConstants.WhiteA700))
//                        .padding(.top, getRelativeHeight(6.0))
//                    }
//                    .frame(width: getRelativeWidth(348.0), height: getRelativeHeight(78.0),
//                           alignment: .center)
//                    .padding(.top, getRelativeHeight(39.0))
//                    Button(action: {}, label: {
//                        HStack(spacing: 0) {
//                            Text(StringConstants.kLblSendCode)
//                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
//                                .fontWeight(.bold)
//                                .padding(.horizontal, getRelativeWidth(30.0))
//                                .padding(.vertical, getRelativeHeight(14.0))
//                                .foregroundColor(ColorConstants.WhiteA700)
//                                .minimumScaleFactor(0.5)
//                                .multilineTextAlignment(.center)
//                                .frame(width: getRelativeWidth(348.0),
//                                       height: getRelativeHeight(54.0), alignment: .center)
//                                .background(RoundedCorners(topLeft: 8.0, topRight: 8.0,
//                                                           bottomLeft: 8.0, bottomRight: 8.0)
//                                        .fill(ColorConstants.Blue300))
//                                .shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 0)
//                                .padding(.vertical, getRelativeHeight(23.0))
//                        }
//                    })
//                    .frame(width: getRelativeWidth(348.0), height: getRelativeHeight(54.0),
//                           alignment: .center)
//                    .background(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0,
//                                               bottomRight: 8.0)
//                            .fill(ColorConstants.Blue300))
//                    .shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 0)
//                    .padding(.vertical, getRelativeHeight(23.0))
//                }
//                .frame(width: UIScreen.main.bounds.width, height: getRelativeHeight(747.0),
//                       alignment: .center)
//            }
//            .frame(width: UIScreen.main.bounds.width, alignment: .topLeading)
//            .background(ColorConstants.WhiteA700)
//            .padding(.top, getRelativeHeight(30.0))
//            .padding(.bottom, getRelativeHeight(10.0))
//        }
//        .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height)
//        .background(ColorConstants.WhiteA700)
//        .ignoresSafeArea()
//        .hideNavigationBar()
//    }
//}
//
//struct ForgotpasswordView_Previews: PreviewProvider {
//    static var previews: some View {
//        ForgotpasswordView()
//    }
//}
