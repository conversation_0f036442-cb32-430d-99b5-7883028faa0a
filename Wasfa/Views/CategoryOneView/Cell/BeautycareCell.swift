//import SwiftUI
//
//struct BeautycareCell: View {
//    var body: some View {
//        ZStack(alignment: .center) {
//            Image("img_rectangle_22490_88x358")
//                .resizable()
//                .frame(width: getRelativeWidth(356.0), height: getRelativeHeight(88.0),
//                       alignment: .leading)
//                .scaledToFit()
//                .background(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
//                                           bottomRight: 13.0))
//            HStack {
//                Text(StringConstants.kLblBeautyCare)
//                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
//                    .fontWeight(.bold)
//                    .foregroundColor(ColorConstants.WhiteA700)
//                    .minimumScaleFactor(0.5)
//                    .multilineTextAlignment(.leading)
//                    .frame(width: getRelativeWidth(101.0), height: getRelativeHeight(25.0),
//                           alignment: .leading)
//                Button(action: {}, label: {
//                    Image("img_add_ring")
//                })
//                .frame(width: getRelativeWidth(22.0), height: getRelativeWidth(24.0),
//                       alignment: .center)
//                .padding(.leading, getRelativeWidth(188.0))
//            }
//            .frame(width: getRelativeWidth(356.0), height: getRelativeHeight(88.0),
//                   alignment: .leading)
//            .background(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
//                                       bottomRight: 13.0)
//                    .fill(ColorConstants.Black90042))
//            .shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 1)
//        }
//        .hideNavigationBar()
//        .frame(width: getRelativeWidth(356.0), alignment: .leading)
//    }
//}
//
///* struct BeautycareCell_Previews: PreviewProvider {
//
// static var previews: some View {
// 			BeautycareCell()
// }
// } */
