//import SwiftUI

//struct CategoryOneView: View {
//    @StateObject var categoryOneViewModel = CategoryOneViewModel()
//    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
//    var body: some View {
//        VStack {
//            VStack {
//                HStack {
//                    HStack {
//                        Button(action: {}, label: {
//                            Image("img_vector_9")
//                        })
//                        .frame(width: getRelativeWidth(24.0), height: getRelativeWidth(24.0),
//                               alignment: .center)
//                        .padding(.bottom, getRelativeHeight(4.0))
//                        Text(StringConstants.kLblCategories)
//                            .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(16.0)))
//                            .fontWeight(.heavy)
//                            .foregroundColor(ColorConstants.Black90001)
//                            .minimumScaleFactor(0.5)
//                            .multilineTextAlignment(.leading)
//                            .frame(width: getRelativeWidth(82.0), height: getRelativeHeight(22.0),
//                                   alignment: .topLeading)
//                            .padding(.leading, getRelativeWidth(20.0))
//                        Image("img_search_light")
//                            .resizable()
//                            .frame(width: getRelativeWidth(27.0), height: getRelativeWidth(27.0),
//                                   alignment: .center)
//                            .scaledToFit()
//                            .clipped()
//                            .padding(.leading, getRelativeWidth(83.0))
//                        Image("img_bell_pin_light")
//                            .resizable()
//                            .frame(width: getRelativeWidth(27.0), height: getRelativeWidth(27.0),
//                                   alignment: .center)
//                            .scaledToFit()
//                            .clipped()
//                            .padding(.leading, getRelativeWidth(33.0))
//                        ZStack(alignment: .topLeading) {
//                            Image("img_vector")
//                                .resizable()
//                                .frame(width: getRelativeWidth(18.0),
//                                       height: getRelativeHeight(15.0), alignment: .center)
//                                .scaledToFit()
//                                .clipped()
//                                .padding(.top, getRelativeHeight(7.1))
//                                .padding(.trailing, getRelativeWidth(11.0))
//                            Image("img_vector_gray_900_03")
//                                .resizable()
//                                .frame(width: getRelativeWidth(9.0), height: getRelativeHeight(4.0),
//                                       alignment: .center)
//                                .scaledToFit()
//                                .clipped()
//                                .padding(.bottom, getRelativeHeight(15.63))
//                                .padding(.trailing, getRelativeWidth(15.06))
//                            Image("img_vector_gray_900_03_1x1")
//                                .resizable()
//                                .frame(width: getRelativeWidth(1.0), height: getRelativeWidth(1.0),
//                                       alignment: .center)
//                                .scaledToFit()
//                                .clipped()
//                                .padding(.top, getRelativeHeight(11.56))
//                                .padding(.trailing, getRelativeWidth(21.65))
//                            HStack {
//                                Image("img_vector_gray_900_03_1x1")
//                                    .resizable()
//                                    .frame(width: getRelativeWidth(1.0),
//                                           height: getRelativeWidth(1.0), alignment: .center)
//                                    .scaledToFit()
//                                    .clipped()
//                                    .padding(.vertical, getRelativeHeight(11.0))
//                                Text("2")
//                                    .font(FontScheme.kInterRegular(size: getRelativeHeight(11.0)))
//                                    .fontWeight(.regular)
//                                    .padding(.horizontal, getRelativeWidth(4.0))
//                                    .foregroundColor(ColorConstants.WhiteA700)
//                                    .minimumScaleFactor(0.5)
//                                    .multilineTextAlignment(.leading)
//                                    .frame(width: getRelativeWidth(16.0),
//                                           height: getRelativeWidth(16.0), alignment: .topLeading)
//                                    .background(ColorConstants.Blue300)
//                            }
//                            .frame(width: getRelativeWidth(16.0), height: getRelativeWidth(16.0),
//                                   alignment: .topTrailing)
//                            .padding(.bottom, getRelativeHeight(6.0))
//                            .padding(.leading, getRelativeWidth(12.42))
//                        }
//                        .hideNavigationBar()
//                        .frame(width: getRelativeWidth(29.0), height: getRelativeHeight(22.0),
//                               alignment: .center)
//                        .padding(.leading, getRelativeWidth(29.0))
//                    }
//                    .frame(width: getRelativeWidth(355.0), height: getRelativeHeight(28.0),
//                           alignment: .leading)
//                    .padding(.leading, getRelativeWidth(19.0))
//                    .padding(.trailing, getRelativeWidth(16.0))
//                }
//                .frame(width: UIScreen.main.bounds.width - 20, height: getRelativeHeight(73.0),
//                       alignment: .leading)
//                .background(ColorConstants.WhiteA700)
//                .shadow(color: ColorConstants.Black90021, radius: 14, x: 0, y: 6)
//                ScrollView(.vertical, showsIndicators: false) {
//                    VStack {
//                        ZStack(alignment: .center) {
//                            VStack {
//                                Group {
//                                    VStack(alignment: .leading, spacing: 0) {
//                                        HStack {
//                                            Text(StringConstants.kLblMedicalCare)
//                                                .font(FontScheme
//                                                    .kNunitoBold(size: getRelativeHeight(14.0)))
//                                                .fontWeight(.bold)
//                                                .foregroundColor(ColorConstants.Black90001)
//                                                .minimumScaleFactor(0.5)
//                                                .multilineTextAlignment(.leading)
//                                                .frame(width: getRelativeWidth(85.0),
//                                                       height: getRelativeHeight(20.0),
//                                                       alignment: .topLeading)
//                                            Button(action: {}, label: {
//                                                Image("img_expand_down_light")
//                                            })
//                                            .frame(width: getRelativeWidth(24.0),
//                                                   height: getRelativeWidth(24.0),
//                                                   alignment: .center)
//                                            .padding(.leading, getRelativeWidth(225.0))
//                                        }
//                                        .frame(width: getRelativeWidth(334.0),
//                                               height: getRelativeHeight(24.0), alignment: .leading)
//                                        HStack {
//                                            Text(StringConstants.kMsgAirFiltersHumidifiers)
//                                                .font(FontScheme
//                                                    .kNunitoLight(size: getRelativeHeight(10.0)))
//                                                .fontWeight(.light)
//                                                .padding(.horizontal, getRelativeWidth(8.0))
//                                                .padding(.vertical, getRelativeHeight(4.0))
//                                                .foregroundColor(ColorConstants.Black90001)
//                                                .minimumScaleFactor(0.5)
//                                                .multilineTextAlignment(.center)
//                                                .frame(width: getRelativeWidth(121.0),
//                                                       height: getRelativeHeight(24.0),
//                                                       alignment: .center)
//                                                .background(ColorConstants.Blue6003a)
//                                            Text(StringConstants.kLblBloodPressure)
//                                                .font(FontScheme
//                                                    .kNunitoLight(size: getRelativeHeight(10.0)))
//                                                .fontWeight(.light)
//                                                .padding(.horizontal, getRelativeWidth(8.0))
//                                                .padding(.vertical, getRelativeHeight(4.0))
//                                                .foregroundColor(ColorConstants.Black90001)
//                                                .minimumScaleFactor(0.5)
//                                                .multilineTextAlignment(.center)
//                                                .frame(width: getRelativeWidth(84.0),
//                                                       height: getRelativeHeight(24.0),
//                                                       alignment: .center)
//                                                .background(ColorConstants.Blue6003a)
//                                            Text(StringConstants.kLblCovidTest)
//                                                .font(FontScheme
//                                                    .kNunitoLight(size: getRelativeHeight(10.0)))
//                                                .fontWeight(.light)
//                                                .padding(.horizontal, getRelativeWidth(8.0))
//                                                .padding(.vertical, getRelativeHeight(4.0))
//                                                .foregroundColor(ColorConstants.Black90001)
//                                                .minimumScaleFactor(0.5)
//                                                .multilineTextAlignment(.center)
//                                                .frame(width: getRelativeWidth(62.0),
//                                                       height: getRelativeHeight(24.0),
//                                                       alignment: .center)
//                                                .background(ColorConstants.Blue6003a)
//                                            Text(StringConstants.kLblDiabetes)
//                                                .font(FontScheme
//                                                    .kNunitoLight(size: getRelativeHeight(10.0)))
//                                                .fontWeight(.light)
//                                                .padding(.horizontal, getRelativeWidth(8.0))
//                                                .padding(.vertical, getRelativeHeight(4.0))
//                                                .foregroundColor(ColorConstants.Black90001)
//                                                .minimumScaleFactor(0.5)
//                                                .multilineTextAlignment(.center)
//                                                .frame(width: getRelativeWidth(56.0),
//                                                       height: getRelativeHeight(24.0),
//                                                       alignment: .center)
//                                                .background(ColorConstants.Blue6003a)
//                                                .padding(.leading, getRelativeWidth(7.0))
//                                        }
//                                        .frame(width: getRelativeWidth(336.0),
//                                               height: getRelativeHeight(24.0), alignment: .leading)
//                                        .padding(.top, getRelativeHeight(21.0))
//                                        HStack {
//                                            Text(StringConstants.kMsgElectricHeating)
//                                                .font(FontScheme
//                                                    .kNunitoLight(size: getRelativeHeight(10.0)))
//                                                .fontWeight(.light)
//                                                .padding(.horizontal, getRelativeWidth(8.0))
//                                                .padding(.vertical, getRelativeHeight(4.0))
//                                                .foregroundColor(ColorConstants.Black90001)
//                                                .minimumScaleFactor(0.5)
//                                                .multilineTextAlignment(.center)
//                                                .frame(width: getRelativeWidth(106.0),
//                                                       height: getRelativeHeight(24.0),
//                                                       alignment: .center)
//                                                .background(ColorConstants.Blue6003a)
//                                            Text(StringConstants.kMsgElectricToothbrush)
//                                                .font(FontScheme
//                                                    .kNunitoLight(size: getRelativeHeight(10.0)))
//                                                .fontWeight(.light)
//                                                .padding(.horizontal, getRelativeWidth(8.0))
//                                                .padding(.vertical, getRelativeHeight(4.0))
//                                                .foregroundColor(ColorConstants.Black90001)
//                                                .minimumScaleFactor(0.5)
//                                                .multilineTextAlignment(.center)
//                                                .frame(width: getRelativeWidth(101.0),
//                                                       height: getRelativeHeight(24.0),
//                                                       alignment: .center)
//                                                .background(ColorConstants.Blue6003a)
//                                                .padding(.leading, getRelativeWidth(4.0))
//                                            Text(StringConstants.kLblMassagers)
//                                                .font(FontScheme
//                                                    .kNunitoLight(size: getRelativeHeight(10.0)))
//                                                .fontWeight(.light)
//                                                .padding(.horizontal, getRelativeWidth(8.0))
//                                                .padding(.vertical, getRelativeHeight(4.0))
//                                                .foregroundColor(ColorConstants.Black90001)
//                                                .minimumScaleFactor(0.5)
//                                                .multilineTextAlignment(.center)
//                                                .frame(width: getRelativeWidth(64.0),
//                                                       height: getRelativeHeight(24.0),
//                                                       alignment: .center)
//                                                .background(ColorConstants.Blue6003a)
//                                                .padding(.leading, getRelativeWidth(6.0))
//                                        }
//                                        .frame(width: getRelativeWidth(281.0),
//                                               height: getRelativeHeight(24.0), alignment: .leading)
//                                        .padding(.top, getRelativeHeight(4.0))
//                                        .padding(.trailing, getRelativeWidth(54.0))
//                                        HStack {
//                                            Text(StringConstants.kLblNebulizer)
//                                                .font(FontScheme
//                                                    .kNunitoLight(size: getRelativeHeight(10.0)))
//                                                .fontWeight(.light)
//                                                .padding(.horizontal, getRelativeWidth(8.0))
//                                                .padding(.vertical, getRelativeHeight(4.0))
//                                                .foregroundColor(ColorConstants.Black90001)
//                                                .minimumScaleFactor(0.5)
//                                                .multilineTextAlignment(.center)
//                                                .frame(width: getRelativeWidth(59.0),
//                                                       height: getRelativeHeight(24.0),
//                                                       alignment: .center)
//                                                .background(ColorConstants.Blue6003a)
//                                            Text(StringConstants.kLblOximeters)
//                                                .font(FontScheme
//                                                    .kNunitoLight(size: getRelativeHeight(10.0)))
//                                                .fontWeight(.light)
//                                                .padding(.horizontal, getRelativeWidth(8.0))
//                                                .padding(.vertical, getRelativeHeight(4.0))
//                                                .foregroundColor(ColorConstants.Black90001)
//                                                .minimumScaleFactor(0.5)
//                                                .multilineTextAlignment(.center)
//                                                .frame(width: getRelativeWidth(62.0),
//                                                       height: getRelativeHeight(24.0),
//                                                       alignment: .center)
//                                                .background(ColorConstants.Blue6003a)
//                                                .padding(.leading, getRelativeWidth(4.0))
//                                            Text(StringConstants.kLblScales)
//                                                .font(FontScheme
//                                                    .kNunitoLight(size: getRelativeHeight(10.0)))
//                                                .fontWeight(.light)
//                                                .padding(.horizontal, getRelativeWidth(8.0))
//                                                .padding(.vertical, getRelativeHeight(4.0))
//                                                .foregroundColor(ColorConstants.Black90001)
//                                                .minimumScaleFactor(0.5)
//                                                .multilineTextAlignment(.center)
//                                                .frame(width: getRelativeWidth(45.0),
//                                                       height: getRelativeHeight(24.0),
//                                                       alignment: .center)
//                                                .background(ColorConstants.Blue6003a)
//                                                .padding(.leading, getRelativeWidth(4.0))
//                                            Text(StringConstants.kLblStepCounter)
//                                                .font(FontScheme
//                                                    .kNunitoLight(size: getRelativeHeight(10.0)))
//                                                .fontWeight(.light)
//                                                .padding(.horizontal, getRelativeWidth(8.0))
//                                                .padding(.vertical, getRelativeHeight(4.0))
//                                                .foregroundColor(ColorConstants.Black90001)
//                                                .minimumScaleFactor(0.5)
//                                                .multilineTextAlignment(.center)
//                                                .frame(width: getRelativeWidth(74.0),
//                                                       height: getRelativeHeight(24.0),
//                                                       alignment: .center)
//                                                .background(ColorConstants.Blue6003a)
//                                                .padding(.leading, getRelativeWidth(5.0))
//                                        }
//                                        .frame(width: getRelativeWidth(253.0),
//                                               height: getRelativeHeight(24.0), alignment: .leading)
//                                        .padding(.top, getRelativeHeight(4.0))
//                                        .padding(.trailing, getRelativeWidth(81.0))
//                                        HStack {
//                                            Text(StringConstants.kMsgSterilizersDevices)
//                                                .font(FontScheme
//                                                    .kNunitoLight(size: getRelativeHeight(10.0)))
//                                                .fontWeight(.light)
//                                                .padding(.horizontal, getRelativeWidth(8.0))
//                                                .padding(.vertical, getRelativeHeight(4.0))
//                                                .foregroundColor(ColorConstants.Black90001)
//                                                .minimumScaleFactor(0.5)
//                                                .multilineTextAlignment(.center)
//                                                .frame(width: getRelativeWidth(97.0),
//                                                       height: getRelativeHeight(24.0),
//                                                       alignment: .center)
//                                                .background(ColorConstants.Blue6003a)
//                                            Text(StringConstants.kLblThermometers)
//                                                .font(FontScheme
//                                                    .kNunitoLight(size: getRelativeHeight(10.0)))
//                                                .fontWeight(.light)
//                                                .padding(.horizontal, getRelativeWidth(7.0))
//                                                .padding(.vertical, getRelativeHeight(4.0))
//                                                .foregroundColor(ColorConstants.Black90001)
//                                                .minimumScaleFactor(0.5)
//                                                .multilineTextAlignment(.center)
//                                                .frame(width: getRelativeWidth(81.0),
//                                                       height: getRelativeHeight(24.0),
//                                                       alignment: .center)
//                                                .background(ColorConstants.Blue6003a)
//                                                .padding(.leading, getRelativeWidth(8.0))
//                                            Text(StringConstants.kLblWeightLoss)
//                                                .font(FontScheme
//                                                    .kNunitoLight(size: getRelativeHeight(10.0)))
//                                                .fontWeight(.light)
//                                                .padding(.horizontal, getRelativeWidth(8.0))
//                                                .padding(.vertical, getRelativeHeight(4.0))
//                                                .foregroundColor(ColorConstants.Black90001)
//                                                .minimumScaleFactor(0.5)
//                                                .multilineTextAlignment(.center)
//                                                .frame(width: getRelativeWidth(72.0),
//                                                       height: getRelativeHeight(24.0),
//                                                       alignment: .center)
//                                                .background(ColorConstants.Blue6003a)
//                                                .padding(.leading, getRelativeWidth(8.0))
//                                        }
//                                        .frame(width: getRelativeWidth(266.0),
//                                               height: getRelativeHeight(24.0), alignment: .leading)
//                                        .padding(.vertical, getRelativeHeight(4.0))
//                                        .padding(.trailing, getRelativeWidth(68.0))
//                                    }
//                                    .frame(width: getRelativeWidth(357.0),
//                                           height: getRelativeHeight(182.0), alignment: .center)
//                                    .background(ColorConstants.Gray20001)
//                                    HStack {
//                                        Text(StringConstants.kLblPregnancyCare)
//                                            .font(FontScheme
//                                                .kNunitoBold(size: getRelativeHeight(14.0)))
//                                            .fontWeight(.bold)
//                                            .foregroundColor(ColorConstants.Black90001)
//                                            .minimumScaleFactor(0.5)
//                                            .multilineTextAlignment(.leading)
//                                            .frame(width: getRelativeWidth(103.0),
//                                                   height: getRelativeHeight(20.0),
//                                                   alignment: .topLeading)
//                                            .padding(.top, getRelativeHeight(5.0))
//                                            .padding(.bottom, getRelativeHeight(7.0))
//                                        Button(action: {}, label: {
//                                            Image("img_expand_down_light")
//                                        })
//                                        .frame(width: getRelativeWidth(24.0),
//                                               height: getRelativeWidth(24.0), alignment: .center)
//                                        .padding(.bottom, getRelativeHeight(7.0))
//                                        .padding(.leading, getRelativeWidth(207.0))
//                                    }
//                                    .frame(width: getRelativeWidth(357.0),
//                                           height: getRelativeHeight(47.0), alignment: .center)
//                                    .background(ColorConstants.Gray20001)
//                                    .padding(.top, getRelativeHeight(6.0))
//                                    HStack {
//                                        Text(StringConstants.kLblSkinCare)
//                                            .font(FontScheme
//                                                .kNunitoBold(size: getRelativeHeight(14.0)))
//                                            .fontWeight(.bold)
//                                            .foregroundColor(ColorConstants.Black90001)
//                                            .minimumScaleFactor(0.5)
//                                            .multilineTextAlignment(.leading)
//                                            .frame(width: getRelativeWidth(62.0),
//                                                   height: getRelativeHeight(20.0),
//                                                   alignment: .topLeading)
//                                            .padding(.vertical, getRelativeHeight(4.0))
//                                        Button(action: {}, label: {
//                                            Image("img_expand_down_light")
//                                        })
//                                        .frame(width: getRelativeWidth(24.0),
//                                               height: getRelativeWidth(24.0), alignment: .center)
//                                        .padding(.bottom, getRelativeHeight(8.0))
//                                        .padding(.leading, getRelativeWidth(248.0))
//                                    }
//                                    .frame(width: getRelativeWidth(357.0),
//                                           height: getRelativeHeight(47.0), alignment: .center)
//                                    .background(ColorConstants.Gray20001)
//                                    .padding(.top, getRelativeHeight(6.0))
//                                    HStack {
//                                        Text(StringConstants.kLblHandFootCare)
//                                            .font(FontScheme
//                                                .kNunitoBold(size: getRelativeHeight(14.0)))
//                                            .fontWeight(.bold)
//                                            .foregroundColor(ColorConstants.Black90001)
//                                            .minimumScaleFactor(0.5)
//                                            .multilineTextAlignment(.leading)
//                                            .frame(width: getRelativeWidth(102.0),
//                                                   height: getRelativeHeight(20.0),
//                                                   alignment: .topLeading)
//                                            .padding(.vertical, getRelativeHeight(4.0))
//                                        Button(action: {}, label: {
//                                            Image("img_expand_down_light")
//                                        })
//                                        .frame(width: getRelativeWidth(24.0),
//                                               height: getRelativeWidth(24.0), alignment: .center)
//                                        .padding(.bottom, getRelativeHeight(8.0))
//                                        .padding(.leading, getRelativeWidth(208.0))
//                                    }
//                                    .frame(width: getRelativeWidth(357.0),
//                                           height: getRelativeHeight(47.0), alignment: .center)
//                                    .background(ColorConstants.Gray20001)
//                                    .padding(.top, getRelativeHeight(6.0))
//                                    HStack {
//                                        Text(StringConstants.kLblNailCare)
//                                            .font(FontScheme
//                                                .kNunitoBold(size: getRelativeHeight(14.0)))
//                                            .fontWeight(.bold)
//                                            .foregroundColor(ColorConstants.Black90001)
//                                            .minimumScaleFactor(0.5)
//                                            .multilineTextAlignment(.leading)
//                                            .frame(width: getRelativeWidth(60.0),
//                                                   height: getRelativeHeight(20.0),
//                                                   alignment: .topLeading)
//                                            .padding(.vertical, getRelativeHeight(4.0))
//                                        Button(action: {}, label: {
//                                            Image("img_expand_down_light")
//                                        })
//                                        .frame(width: getRelativeWidth(24.0),
//                                               height: getRelativeWidth(24.0), alignment: .center)
//                                        .padding(.bottom, getRelativeHeight(8.0))
//                                        .padding(.leading, getRelativeWidth(250.0))
//                                    }
//                                    .frame(width: getRelativeWidth(357.0),
//                                           height: getRelativeHeight(47.0), alignment: .center)
//                                    .background(ColorConstants.Gray20001)
//                                    .padding(.top, getRelativeHeight(6.0))
//                                    HStack {
//                                        Text(StringConstants.kLblMenCare)
//                                            .font(FontScheme
//                                                .kNunitoBold(size: getRelativeHeight(14.0)))
//                                            .fontWeight(.bold)
//                                            .foregroundColor(ColorConstants.Black90001)
//                                            .minimumScaleFactor(0.5)
//                                            .multilineTextAlignment(.leading)
//                                            .frame(width: getRelativeWidth(62.0),
//                                                   height: getRelativeHeight(20.0),
//                                                   alignment: .topLeading)
//                                            .padding(.vertical, getRelativeHeight(4.0))
//                                        Button(action: {}, label: {
//                                            Image("img_expand_down_light")
//                                        })
//                                        .frame(width: getRelativeWidth(24.0),
//                                               height: getRelativeWidth(24.0), alignment: .center)
//                                        .padding(.bottom, getRelativeHeight(8.0))
//                                        .padding(.leading, getRelativeWidth(248.0))
//                                    }
//                                    .frame(width: getRelativeWidth(357.0),
//                                           height: getRelativeHeight(47.0), alignment: .center)
//                                    .background(ColorConstants.Gray20001)
//                                    .padding(.top, getRelativeHeight(6.0))
//                                    HStack {
//                                        Text(StringConstants.kLblWomenCare)
//                                            .font(FontScheme
//                                                .kNunitoBold(size: getRelativeHeight(14.0)))
//                                            .fontWeight(.bold)
//                                            .foregroundColor(ColorConstants.Black90001)
//                                            .minimumScaleFactor(0.5)
//                                            .multilineTextAlignment(.leading)
//                                            .frame(width: getRelativeWidth(85.0),
//                                                   height: getRelativeHeight(20.0),
//                                                   alignment: .topLeading)
//                                            .padding(.vertical, getRelativeHeight(4.0))
//                                        Button(action: {}, label: {
//                                            Image("img_expand_down_light")
//                                        })
//                                        .frame(width: getRelativeWidth(24.0),
//                                               height: getRelativeWidth(24.0), alignment: .center)
//                                        .padding(.bottom, getRelativeHeight(8.0))
//                                        .padding(.leading, getRelativeWidth(225.0))
//                                    }
//                                    .frame(width: getRelativeWidth(357.0),
//                                           height: getRelativeHeight(47.0), alignment: .center)
//                                    .background(ColorConstants.Gray20001)
//                                    .padding(.top, getRelativeHeight(6.0))
//                                    HStack {
//                                        Text(StringConstants.kLblHairCare)
//                                            .font(FontScheme
//                                                .kNunitoBold(size: getRelativeHeight(14.0)))
//                                            .fontWeight(.bold)
//                                            .foregroundColor(ColorConstants.Black90001)
//                                            .minimumScaleFactor(0.5)
//                                            .multilineTextAlignment(.leading)
//                                            .frame(width: getRelativeWidth(62.0),
//                                                   height: getRelativeHeight(20.0),
//                                                   alignment: .topLeading)
//                                            .padding(.vertical, getRelativeHeight(4.0))
//                                        Button(action: {}, label: {
//                                            Image("img_expand_down_light")
//                                        })
//                                        .frame(width: getRelativeWidth(24.0),
//                                               height: getRelativeWidth(24.0), alignment: .center)
//                                        .padding(.bottom, getRelativeHeight(8.0))
//                                        .padding(.leading, getRelativeWidth(248.0))
//                                    }
//                                    .frame(width: getRelativeWidth(357.0),
//                                           height: getRelativeHeight(47.0), alignment: .center)
//                                    .background(ColorConstants.Gray20001)
//                                    .padding(.top, getRelativeHeight(6.0))
//                                }
//                                Group {
//                                    HStack {
//                                        Text(StringConstants.kLblOralCare)
//                                            .font(FontScheme
//                                                .kNunitoBold(size: getRelativeHeight(14.0)))
//                                            .fontWeight(.bold)
//                                            .foregroundColor(ColorConstants.Black90001)
//                                            .minimumScaleFactor(0.5)
//                                            .multilineTextAlignment(.leading)
//                                            .frame(width: getRelativeWidth(63.0),
//                                                   height: getRelativeHeight(20.0),
//                                                   alignment: .topLeading)
//                                            .padding(.vertical, getRelativeHeight(4.0))
//                                        Button(action: {}, label: {
//                                            Image("img_expand_down_light")
//                                        })
//                                        .frame(width: getRelativeWidth(24.0),
//                                               height: getRelativeWidth(24.0), alignment: .center)
//                                        .padding(.bottom, getRelativeHeight(8.0))
//                                        .padding(.leading, getRelativeWidth(247.0))
//                                    }
//                                    .frame(width: getRelativeWidth(357.0),
//                                           height: getRelativeHeight(47.0), alignment: .center)
//                                    .background(ColorConstants.Gray20001)
//                                    .padding(.top, getRelativeHeight(6.0))
//                                    HStack {
//                                        Text(StringConstants.kLblBabyCare)
//                                            .font(FontScheme
//                                                .kNunitoBold(size: getRelativeHeight(14.0)))
//                                            .fontWeight(.bold)
//                                            .foregroundColor(ColorConstants.Black90001)
//                                            .minimumScaleFactor(0.5)
//                                            .multilineTextAlignment(.leading)
//                                            .frame(width: getRelativeWidth(67.0),
//                                                   height: getRelativeHeight(20.0),
//                                                   alignment: .topLeading)
//                                            .padding(.top, getRelativeHeight(5.0))
//                                            .padding(.bottom, getRelativeHeight(7.0))
//                                        Button(action: {}, label: {
//                                            Image("img_expand_down_light")
//                                        })
//                                        .frame(width: getRelativeWidth(24.0),
//                                               height: getRelativeWidth(24.0), alignment: .center)
//                                        .padding(.bottom, getRelativeHeight(7.0))
//                                        .padding(.leading, getRelativeWidth(243.0))
//                                    }
//                                    .frame(width: getRelativeWidth(357.0),
//                                           height: getRelativeHeight(47.0), alignment: .center)
//                                    .background(ColorConstants.Gray20001)
//                                    .padding(.top, getRelativeHeight(6.0))
//                                    .padding(.bottom, getRelativeHeight(9.0))
//                                }
//                            }
//                            .frame(width: getRelativeWidth(358.0), height: getRelativeHeight(756.0),
//                                   alignment: .center)
//                            .background(RoundedCorners(bottomLeft: 24.0, bottomRight: 24.0)
//                                .fill(ColorConstants.Gray100))
//                            .padding(.top, getRelativeHeight(70.0))
//                            ZStack(alignment: .center) {
//                                Image("img_rectangle_22490")
//                                    .resizable()
//                                    .frame(width: getRelativeWidth(358.0),
//                                           height: getRelativeHeight(88.0), alignment: .center)
//                                    .scaledToFit()
//                                    .clipped()
//                                    .background(RoundedCorners(topLeft: 13.0, topRight: 13.0,
//                                                               bottomLeft: 13.0, bottomRight: 13.0))
//                                HStack {
//                                    Text(StringConstants.kLblDevices)
//                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
//                                        .fontWeight(.bold)
//                                        .foregroundColor(ColorConstants.WhiteA700)
//                                        .minimumScaleFactor(0.5)
//                                        .multilineTextAlignment(.leading)
//                                        .frame(width: getRelativeWidth(65.0),
//                                               height: getRelativeHeight(25.0),
//                                               alignment: .topLeading)
//                                    Button(action: {}, label: {
//                                        Image("img_add_ring")
//                                    })
//                                    .frame(width: getRelativeWidth(24.0),
//                                           height: getRelativeWidth(24.0), alignment: .center)
//                                    .padding(.leading, getRelativeWidth(226.0))
//                                }
//                                .frame(width: getRelativeWidth(358.0),
//                                       height: getRelativeHeight(88.0), alignment: .center)
//                                .background(RoundedCorners(topLeft: 13.0, topRight: 13.0,
//                                                           bottomLeft: 13.0, bottomRight: 13.0)
//                                        .fill(ColorConstants.Black90042))
//                                .shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 1)
//                            }
//                            .hideNavigationBar()
//                            .frame(width: getRelativeWidth(358.0), height: getRelativeHeight(88.0),
//                                   alignment: .center)
//                            .padding(.bottom, getRelativeHeight(738.0))
//                        }
//                        .hideNavigationBar()
//                        .frame(width: getRelativeWidth(358.0), height: getRelativeHeight(826.0),
//                               alignment: .center)
//                        VStack(spacing: 0) {
//                            ScrollView(.vertical, showsIndicators: false) {
//                                LazyVStack {
//                                    ForEach(0 ... 7, id: \.self) { index in
//                                        BeautycareCell()
//                                    }
//                                }
//                            }
//                        }
//                        .frame(width: getRelativeWidth(358.0), alignment: .center)
//                        .padding(.top, getRelativeHeight(15.0))
//                    }
//                    .frame(width: UIScreen.main.bounds.width, alignment: .topLeading)
//                }
//                Text("tabbar")
//                    .minimumScaleFactor(0.5)
//                    .multilineTextAlignment(.leading)
//                    .frame(width: UIScreen.main.bounds.width - 20, height: getRelativeHeight(64.0),
//                           alignment: .center)
//                    .background(ColorConstants.WhiteA700)
//                    .shadow(color: ColorConstants.Black90023, radius: 10, x: 0, y: -5)
//            }
//            .frame(width: UIScreen.main.bounds.width, alignment: .topLeading)
//            .background(ColorConstants.WhiteA700)
//            .padding(.top, getRelativeHeight(30.0))
//            .padding(.bottom, getRelativeHeight(10.0))
//        }
//        .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height)
//        .background(ColorConstants.WhiteA700)
//        .ignoresSafeArea()
//        .hideNavigationBar()
//    }
//}
//
//struct CategoryOneView_Previews: PreviewProvider {
//    static var previews: some View {
//        CategoryOneView()
//    }
//}
