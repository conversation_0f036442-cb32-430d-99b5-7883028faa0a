import SwiftUI

struct OrderHistoryView: View {
    let selectedTab: OrderHistoryTabType
    let orderHistoryList: [OrderHistoryModel]
    let onSelect: (Int) -> Void
    let onTrack: (Int) -> Void
    let onCancel: (Int) -> Void
    let onReorder: (Int) -> Void
    @StateObject var orderhistoryViewModel = OrderhistoryViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    var body: some View {
        VStack {
//            ScrollView(.vertical, showsIndicators: false) {
            if orderHistoryList.isEmpty {
                switch selectedTab {
                case .onProgress:
                    ContentUnavailableView(
                        "No Orders Yet",
                        systemImage: "shippingbox",
                        description: Text("You haven't placed any orders yet. Start shopping now!")
                    )
                case .completed:
                    ContentUnavailableView(
                        "No Completed Orders Yet",
                        systemImage: "shippingbox",
                        description: Text("You haven't completed any orders yet.")
                    )
                case .cancelled:
                    ContentUnavailableView(
                        "No Cancelled Orders Yet",
                        systemImage: "shippingbox",
                        description: Text("You haven't cancelled any orders yet.")
                    )
                }
                    
              
                    
            } else {
                LazyVStack(spacing: 14.relativeHeight) {
                    ForEach(orderHistoryList) {
                        OrderHistoryCell(model: $0, onSelect: onSelect, onTrack: onTrack, onCancel: onCancel, onReorder: onReorder)
                    }
                }
            }
//            }
        }
        .background(ColorConstants.WhiteA700)
        .padding(.bottom)
    }
}

// struct OrderhistoryView_Previews: PreviewProvider {
//    static var previews: some View {
//        OrderHistoryView(orderHistoryList: [], onSelect: {_ in})
//    }
// }
