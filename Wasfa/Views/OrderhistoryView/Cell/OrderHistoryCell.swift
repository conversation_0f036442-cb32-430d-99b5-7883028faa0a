import SwiftUI

struct OrderHistoryCell: View {
    let model: OrderHistoryModel
    let onSelect: (Int) -> Void
    let onTrack: (Int) -> Void
    let onCancel: (Int) -> Void
    let onReorder: (Int) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack(alignment: .top) {
                // Product Images Section
               
                VStack(alignment: .leading, spacing: 8) {
                    if let firstImage = model.productImages.first {
                        // Display the first image prominently
                        NetworkImageView(path: firstImage)
                            .blendMode(.multiply)
                            .padding(4)
                            .frame(width: 80, height: 80)
                            .background(Color(hex: "#F5F5F5"))
                            .cornerRadius(8)
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(ColorConstants.Gray200, lineWidth: 1)
                            )
                    }
                    
                    if model.productImages.count > 1 {
                        // Display a smaller section for additional images with a count indicator
                        HStack(alignment: .top, spacing: 4) {
                            let productImages = model.imageList
                            let remainingImages = productImages.count > 3 ? productImages.dropFirst().prefix(1) : productImages.dropFirst().prefix(2)
                          
                            ForEach(remainingImages, id: \.self) { image in
                                NetworkImageView(path: image.imageUrl)
                                    .blendMode(.multiply)
                                    .padding(4)
                                    .frame(width: 40, height: 40)
                                    .background(Color(hex: "#F5F5F5"))
                                    .cornerRadius(5)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 5)
                                            .stroke(ColorConstants.Gray200, lineWidth: 1)
                                    )
                            }
                            
                            if model.productImages.count > 3 {
                                Text("+\(model.productImages.count - 2)")
                                    .font(Font.custom("Poppins", size: 12.relativeFontSize))
                                    .fontWeight(.semibold)
                                    .foregroundColor(ColorConstants.Black90001)
                                    .frame(width: 40, height: 40)
                                    .background(Color(hex: "#F5F5F5"))
                                    .cornerRadius(5)
                            }
                        }
                    }
                }.padding(.vertical, 8)
                
                VStack(alignment: .leading, spacing: 6.relativeHeight) {
                    Text("Order ID: #\(model.code)")
                        .font(Font.custom("Poppins", size: 15.relativeFontSize))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Black90001)
                        .padding(.bottom, 6)
                    
                    HStack(spacing: 16.relativeWidth) {
                        Text("Date")
                            .font(Font.custom("Poppins", size: 12.relativeFontSize))
                            .fontWeight(.regular)
                            .foregroundColor(Color(hex: "#A0A0A0"))
                        
                        Text(model.date)
                            .font(Font.custom("Poppins", size: 12.relativeFontSize))
                            .fontWeight(.semibold)
                            .foregroundColor(Color(hex: "#60655C"))
                    }
                    
                    HStack(spacing: 16.relativeWidth) {
                        Text("Total Products")
                            .font(Font.custom("Poppins", size: 12.relativeFontSize))
                            .fontWeight(.regular)
                            .foregroundColor(Color(hex: "#A0A0A0"))
                       
                        Text(model.totalProductCount.toString)
                            .font(Font.custom("Poppins", size: 12.relativeFontSize))
                            .fontWeight(.semibold)
                            .foregroundColor(Color(hex: "#60655C"))
                    }
                    
                    HStack(spacing: 16.relativeWidth) {
                        Text("Total price paid")
                            .font(Font.custom("Poppins", size: 12.relativeFontSize))
                            .fontWeight(.regular)
                            .foregroundColor(Color(hex: "#A0A0A0"))
                       
                        Text(model.grandTotal)
                            .font(Font.custom("Poppins", size: 12.relativeFontSize))
                            .fontWeight(.semibold)
                            .foregroundColor(Color(hex: "#363A33"))
                    }
                }
            }
            
            let isOnProgress = model.deliveryStatus == "pending"
            
            HStack(spacing: 12) {
                Button(action: {
                    if isOnProgress {
                        onTrack(model.id)
                    } else {
                        onReorder(model.id)
                    }
                    
                }) {
                    Text(isOnProgress ? "Track Order" : "Reorder")
                        .font(Font.custom("Poppins", size: 15.relativeFontSize))
                        .fontWeight(.bold)
                        .foregroundColor(Color(hex: "#363A33"))
                        .frame(maxWidth: .infinity)
                        .frame(height: 40.relativeHeight)
                        .overlay(RoundedRectangle(cornerRadius: 8).stroke(Color(hex: "#E8EBE6"), lineWidth: 1))
                }
                
                Button(action: {
                    if isOnProgress {
                        onCancel(model.id)
                    } else {
                        onSelect(model.id)
                    }
                    
                }) {
                    Text(isOnProgress ? "Cancel Order" : "Order Info")
                        .font(Font.custom("Poppins", size: 15.relativeFontSize))
                        .fontWeight(.bold)
                        .foregroundColor(Color(hex: "#363A33"))
                        .frame(maxWidth: .infinity)
                        .frame(height: 40.relativeHeight)
                        .overlay(RoundedRectangle(cornerRadius: 8).stroke(Color(hex: "#E8EBE6"), lineWidth: 1))
                        .background(ColorConstants.WhiteA700)
                }
            }
            .padding(.top, 8)
        }
        .padding(16)
        .background(RoundedRectangle(cornerRadius: 8).fill(ColorConstants.WhiteA700))
        .overlay(RoundedRectangle(cornerRadius: 8).stroke(Color(hex: "#E8EBE6"), lineWidth: 1))
        .padding(.horizontal, 20.relativeWidth)
    }
}
