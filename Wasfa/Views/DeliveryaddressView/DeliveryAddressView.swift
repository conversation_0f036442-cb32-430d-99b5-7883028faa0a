import SwiftUI

struct DeliveryAddressView: View {
    @StateObject var viewModel: DeliveryAddressViewModel

    init(onSelectFromCheckout: ((AddressModel) -> Void)? = nil) {
        self._viewModel = StateObject(wrappedValue: DeliveryAddressViewModel(onSelectFromCheckout))
    }

    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            MainScrollBody(backButtonWithTitle: StringConstants.kMsgDeliveryAddress) {
                VStack(spacing: 20) {
                    if viewModel.addressModelList.isEmpty {
                        ContentUnavailableView(
                            "No Saved Addresses",
                            systemImage: "list.bullet.clipboard",
                            description: Text("You haven't added any addresses yet. Add one to speed up checkout!")
                        )
                    } else {
                        LazyVStack(spacing: 18.relativeHeight) {
                            ForEach(viewModel.addressModelList) { model in
                                AddressCardView(
                                    isSelected: viewModel.selectedAddress?.addressId == model.addressId,
                                    model: model,
                                    onSelect: viewModel.onSelect,
                                    onEdit: viewModel.onEdit,
                                    onDelete: viewModel.onDelete
                                )
                            }
                        }
                    }

                    Button(action: viewModel.onAddNewAddress) {
                        HStack {
                            Image(systemName: "plus.circle")
                                .resizable()
                                .foregroundColor(Color(hex: "#1B9ED9"))
                                .frame(width: 24.relativeFontSize, height: 24.relativeFontSize)
                            Text("Add New Address")
                                .font(.custom("Poppins", size: 14.relativeFontSize))
                                .fontWeight(.light)
                                .foregroundColor(Color(hex: "#1E222B"))
                        }

                        .frame(maxWidth: .infinity, minHeight: 79.relativeHeight)
                        .overlay(
                            RoundedRectangle(cornerRadius: 20)
                                .stroke(Color(hex: "#EEEEEE"), style: StrokeStyle(lineWidth: 2, dash: [16]))
                        )
                    }
                    .padding(.top)
                    .padding(.horizontal, 20.relativeWidth)
                }
                .padding(.top, 20)
            }
            .background(ColorConstants.WhiteA700)
            .overlay(alignment: .bottom) {
                Group {
                    switch viewModel.popUpType {
                    case let .address(model):
                        self.addressPopUpView(model)
                    case .date, .tapPayment, .none: EmptyView()
                    }

                }.animation(.easeOut, value: self.viewModel.popUpType)
            }
        }
        .injectEnvironmentValues(viewModel)
    }

    @ViewBuilder
    func addressPopUpView(_ model: AddressModel?) -> some View {
        SlideUpAnimationContainerView(perform: { self.viewModel.updatePopUpType() }) {
            AddressPopUpView(model: model, onAddAddress: viewModel.onUpdateAddress)
        }
        .transition(.asymmetric(
            insertion: .move(edge: .bottom),
            removal: .move(edge: .bottom) // Use opacity for removal
        ))
    }
}

#Preview {
    NavigationStack {
        DeliveryAddressView().attachAllEnvironmentObjects()
    }
}

// struct DeliveryAddressView: View {
//    @StateObject var viewModel: DeliveryAddressViewModel
//
//    init(onSelectFromCheckout: ((AddressModel) -> Void)? = nil) {
//        self._viewModel = StateObject(wrappedValue: DeliveryAddressViewModel(onSelectFromCheckout))
//    }
//
//    var body: some View {
//        SuperView(pageState: $viewModel.pageState) {
//            MainScrollBody(backButtonWithTitle: StringConstants.kMsgDeliveryAddress) {
//                VStack(spacing: 0) {
//                    ScrollView(.vertical, showsIndicators: false) {
//                        if viewModel.addressModelList.isEmpty {
//                            // show ContentUnavailableView with system image title and description
//                            ContentUnavailableView(
//                                            "No Saved Addresses",
//                                            systemImage: "list.bullet.clipboard",
//                                            description: Text("You haven't added any addresses yet. Add one to speed up checkout!")
//                                        )
//
//                        } else {
//                            LazyVStack {
//                                ForEach(viewModel.addressModelList) { model in
//                                    AddressCardView(isSelected: viewModel.selectedAddress?.addressId == model.addressId, model: model, onSelect: viewModel.onSelect, onEdit: viewModel.onEdit, onDelete: viewModel.onDelete)
//                                }
//                            }
//                        }
//                    }
//                }
//                .frame(width: getRelativeWidth(355.0), alignment: .center)
//                .padding()
//            }
//            .safeAreaInset(edge: .top, content: {
//                Button(action: viewModel.onAddNewAddress, label: {
//                    HStack(spacing: 0) {
//                        Image(systemName: "plus")
//
//                        Text(StringConstants.kLblAddNewAddress2)
//
//
//                    }
//                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
//                    .fontWeight(.bold)
//                    .padding(.vertical, getRelativeHeight(14.0))
//                    .foregroundColor(ColorConstants.WhiteA700)
//
//                    .multilineTextAlignment(.leading)
//                    .frame(width: getRelativeWidth(348.0),
//                           height: getRelativeHeight(54.0), alignment: .center)
//                    .background(RoundedCorners(topLeft: 8.0, topRight: 8.0,
//                                               bottomLeft: 8.0, bottomRight: 8.0)
//                            .fill(ColorConstants.Blue300))
//                    .shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 0)
//                    .padding(.horizontal, getRelativeWidth(5.0))
//                }).padding(.top)
//            })
//            .overlay(alignment: .bottom) {
//                Group {
//                    switch viewModel.popUpType {
//                    case let .address(model):
//                        self.addressPopUpView(model)
//                    case .date, .none: EmptyView()
//                    }
//
//                }.animation(.easeOut, value: self.viewModel.popUpType)
//            }
//        }
//        .injectEnvironmentValues(viewModel)
//    }
//
//    @ViewBuilder
//    func addressPopUpView(_ model: AddressModel?) -> some View {
//        SlideUpAnimationContainerView(perform: { self.viewModel.updatePopUpType() }) {
//            AddressPopUpView(model: model, onAddAddress: viewModel.onUpdateAddress)
//        }
//        .transition(.asymmetric(
//            insertion: .move(edge: .bottom),
//            removal: .move(edge: .bottom) // Use opacity for removal
//        ))
//    }
// }
