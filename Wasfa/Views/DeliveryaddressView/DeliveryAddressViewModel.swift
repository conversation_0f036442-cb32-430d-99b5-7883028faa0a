import Foundation
import SwiftUI

class DeliveryAddressViewModel: SuperViewModel {
    @Published var nextScreen: String? = nil
    @Published var popUpType: PopUpType?
    @Published var addressModelList: [AddressModel] = []
    @Published var selectedAddress: AddressModel?
    
    let onSelectFromCheckout: ((AddressModel) -> Void)?
    
    init(_ onSelectFromCheckout: ((AddressModel) -> Void)?) {
        self.onSelectFromCheckout = onSelectFromCheckout
        super.init()
        
        getAddressList()
    }
    
    func updatePopUpType(_ value: PopUpType? = nil) { withAnimation(.bouncy) { self.popUpType = value }}
    
    func onUpdateAddress(_ value: AddressModel?, isUpdate: Bool = false) {
        updatePopUpType()
        guard let value = value else { return }
        if isUpdate {
            updateAddress(value)
        } else {
            addNewAddress(value)
        }
    }
    
    func onAddNewAddress() {
        updatePopUpType(.address())
    }
    
    func addNewAddress(_ model: AddressModel) {
        onApiCall(api.addAddress, parameters: model.toDictionary) { _ in
            self.getAddressList()
        }
    }
    
    func getAddressList() {
        onApiCall(api.addressList, parameters: emptyDictionary) {
            self.addressModelList = $0.data ?? []
            self.selectedAddress = self.addressModelList.first(where: { $0.isDefault })
        }
    }
    
    func onSelect(_ model: AddressModel) {
        if let onSelectFromCheckout = onSelectFromCheckout {
            routerManager?.goBack(appState: appState)
            onSelectFromCheckout(model)
        }
        
        guard selectedAddress?.id != model.id else { return }
        
        selectedAddress = model
        DispatchQueue.debounce(delay: 0.5, key: "\(model.id)_quantity") {
            var model = model
            model.setDefault = model.setDefault == 0 ? 1 : 0
            self.updateAddress(model)
        }
    }
    
    func onEdit(_ model: AddressModel) {
        updatePopUpType(.address(model: model))
    }
    
    func updateAddress(_ model: AddressModel) {
        onApiCall(api.updateAddress, parameters: model.toDictionary) { _ in
            self.getAddressList()
        }
    }
    
    func onDelete(_ model: AddressModel) {
        onApiCall(api.deleteAddress, parameters: ["id": model.id]) { _ in
            self.getAddressList()
        }
    }
}
