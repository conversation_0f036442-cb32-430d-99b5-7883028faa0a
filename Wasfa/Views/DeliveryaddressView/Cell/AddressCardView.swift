import SwiftUI

// struct AddressCardView: View {
//    let isSelected: Bool
//    let model: AddressModel
//    let onSelect: TypeCallback<AddressModel>
//    let onEdit: TypeCallback<AddressModel>
//    let onDelete: TypeCallback<AddressModel>
//
//    var body: some View {
//        HStack(alignment: .top) {
//            ZStack {}
//
//                .frame(width: getRelativeWidth(14.0), height: getRelativeWidth(16.0),
//                       alignment: .leading)
//                .overlay(Circle()
//                    .stroke(isSelected ? ColorConstants.Blue600 : .clear,
//                            lineWidth: 1))
//                .background(Circle()
//                    .fill(isSelected ? ColorConstants.Blue600 : .clear))
//                .frame(width: getRelativeWidth(24.0), height: getRelativeWidth(26.0),
//                       alignment: .center)
//                .overlay(Circle()
//                    .stroke(ColorConstants.Blue600,
//                            lineWidth: 1))
//                .background(Circle()
//                    .fill(ColorConstants.WhiteA700))
//
//            VStack(alignment: .leading, spacing: 0) {
//                Text(model.fullName)
//                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
//                    .fontWeight(.bold)
//                    .foregroundColor(ColorConstants.Black90001)
//                    .multilineTextAlignment(.leading)
//                    .padding(.leading, getRelativeWidth(4.0))
//                Text(model.phone)
//                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
//                    .fontWeight(.bold)
//                    .foregroundColor(ColorConstants.Gray90002)
//                    .multilineTextAlignment(.leading)
//                    .frame(height: getRelativeHeight(17.0),
//                           alignment: .leading)
//                    .padding(.top, getRelativeHeight(6.0))
//                    .padding(.leading, getRelativeWidth(4.0))
//                HStack {
//                    Image("img_pin_fill_teal_900")
//                        .resizable()
//                        .frame(width: getRelativeWidth(17.0), height: getRelativeWidth(19.0),
//                               alignment: .leading)
//                        .scaledToFit()
//                    Text(model.deliveryAddress)
//                        .font(FontScheme.kRobotoRomanRegular(size: getRelativeHeight(11.0)))
//                        .fontWeight(.regular)
//                        .foregroundColor(ColorConstants.Black90001)
//                        .multilineTextAlignment(.leading)
//                        .padding(.leading, getRelativeWidth(6.0))
//                }
//                .frame(width: getRelativeWidth(242.0), height: getRelativeHeight(32.0),
//                       alignment: .leading)
//                .padding(.top, getRelativeHeight(11.0))
//            }
//            .frame(width: getRelativeWidth(242.0), height: getRelativeHeight(87.0),
//                   alignment: .leading)
//            .padding(.leading, getRelativeWidth(9.0))
//
//            Button(action: { onEdit(model) }) {
//                Text(StringConstants.kLblEdit)
//                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
//                    .fontWeight(.bold)
//                    .foregroundColor(ColorConstants.Blue300)
//                    .minimumScaleFactor(0.5)
//                    .multilineTextAlignment(.leading)
//                    .frame(width: getRelativeWidth(22.0), height: getRelativeHeight(17.0),
//                           alignment: .leading)
//            }
//        }
//
//        .padding()
//        .frame(width: getRelativeWidth(353.0))
//        .overlay(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0, bottomRight: 8.0)
//            .stroke(ColorConstants.Blue600,
//                    lineWidth: 1))
//        .background(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0, bottomRight: 8.0)
//            .fill(ColorConstants.WhiteA700))
//        .onSlide(deleteAction: {
//            onDelete(model)
//        })
//        .onTapGesture {
//            onSelect(model)
//        }
//    }
// }

struct AddressCardView: View {
    let isSelected: Bool
    let model: AddressModel
    let onSelect: TypeCallback<AddressModel>
    let onEdit: TypeCallback<AddressModel>
    let onDelete: TypeCallback<AddressModel>

    var body: some View {
        HStack(alignment: .center, spacing: 10) {
            Circle()
                .strokeBorder(isSelected ? Color.init(hex: "#1B9ED9") : Color.init(hex: "#EEEEEE"), lineWidth: isSelected ? 5 : 2)
                .frame(width: 20.relativeFontSize, height: 20.relativeFontSize)
                
               
            VStack(alignment: .leading, spacing: 5) {
                Text(model.addressTitle.name)
                    .font(.custom("Poppins", size: 14))
                    .fontWeight(.semibold)
                    .foregroundColor(Color.init(hex: "#1E222B"))
                Text(model.phone)
                    .font(.custom("Poppins", size: 12.relativeFontSize))
                    .fontWeight(.regular)
                    .foregroundColor(.black.opacity(0.4))
                Text(model.deliveryAddress)
                    .font(.custom("Poppins", size: 12.relativeFontSize))
                    .fontWeight(.regular)
                    .foregroundColor(.black.opacity(0.4))
            }

            Spacer()

            Button(action: { onEdit(model) }) {
                Text("Edit")

                    .font(.custom("Poppins", size: 12.relativeFontSize))
                    .fontWeight(.medium)
                    .foregroundColor(Color(hex: "#1B9ED9"))
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white)
//                .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(isSelected ? Color(hex: "#1B9ED9") : Color.init(hex: "#EEEEEE"), lineWidth: 2)
        )
        .padding(.horizontal, 20.relativeWidth)
        .onSlide(deleteAction: {
            onDelete(model)
        })
        .onTapGesture {
            onSelect(model)
        }
    }
}

extension View {
    func onSlide(editAction: (() -> Void)? = nil, deleteAction: (() -> Void)? = nil) -> some View {
        modifier(SlideActions(deleteAction: deleteAction, editAction: editAction))
    }

    func cornerRadius(radius: CGFloat, corners: UIRectCorner) -> some View {
        ModifiedContent(content: self, modifier: CornerRadiusStyle(radius: radius, corners: corners))
    }
}

struct CornerRadiusStyle: ViewModifier {
    var radius: CGFloat
    var corners: UIRectCorner

    func body(content: Content) -> some View {
        content
            .clipShape(CornerRadiusShape(radius: radius, corners: corners))
    }
}

struct CornerRadiusShape: Shape {
    var radius = CGFloat.infinity
    var corners = UIRectCorner.allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(roundedRect: rect, byRoundingCorners: corners, cornerRadii: CGSize(width: radius, height: radius))
        return Path(path.cgPath)
    }
}

struct SlideActions: ViewModifier {
    let deleteAction: (() -> Void)?
    let editAction: (() -> Void)?

    @State var offset: CGSize = .zero
    @State var initialOffset: CGSize = .zero
    @State var contentWidth: CGFloat = 0.0
    @State var willDeleteIfReleased = false

    func body(content: Content) -> some View {
        content

            .background(
                GeometryReader { geometry in
                    ZStack {
                        Rectangle()
                            .cornerRadius(radius: 7, corners: [.topRight, .bottomRight])
                            .foregroundColor(.clear)

                        VStack(alignment: .center, spacing: 16.0.relativeHeight) {
                            if let _ = editAction {
                                Button(action: {
                                    edit()
                                }, label: {
                                    Image("img_basileditoutl")
                                        .frame(width: getRelativeWidth(40.0),
                                               height: getRelativeWidth(40.0), alignment: .center)
                                        .overlay(RoundedCorners(topLeft: 20.0, topRight: 20.0,
                                                                bottomLeft: 20.0, bottomRight: 20.0)
                                                .stroke(ColorConstants.Blue300,
                                                        lineWidth: 2))
                                        .background(RoundedCorners(topLeft: 20.0, topRight: 20.0,
                                                                   bottomLeft: 20.0, bottomRight: 20.0)
                                                .fill(ColorConstants.Blue300))
                                        .shadow(color: ColorConstants.Cyan4000a, radius: 30, x: 0, y: 12)
                                }).layoutPriority(-1)
                            }

                            if let _ = deleteAction {
                                Button(action: {
                                    delete()
                                }, label: {
                                    Image(systemName: "trash")
                                        .foregroundStyle(Color.white)
                                        .frame(width: getRelativeWidth(40.0),
                                               height: getRelativeWidth(40.0),
                                               alignment: .center)
                                        .overlay(Circle()
                                            .stroke(.red,
                                                    lineWidth: 2))
                                        .background(Circle()
                                            .fill(.red))
                                        .shadow(color: ColorConstants.Cyan4000a, radius: 30,
                                                x: 0, y: 12)

                                }).layoutPriority(-1)
                            }
                        }

                        .padding(.top, 8.0.relativeHeight)
                    }
                    .frame(width: -offset.width)
                    .clipShape(Rectangle())
                    .offset(x: geometry.size.width - 16.0.relativeWidth)
                    .onAppear {
                        contentWidth = geometry.size.width
                    }
//                    .gesture(
//                        TapGesture()
//                            .onEnded {
//                                delete()
//                            }
//                    )
                }
                .shadow(radius: 2)
            )
            .offset(x: offset.width, y: 0)
            .gesture(
                DragGesture()
                    .onChanged { gesture in
                        if gesture.translation.width + initialOffset.width <= 0 {
                            self.offset.width = gesture.translation.width + initialOffset.width
                        }
                        if self.offset.width < -deletionDistance, !willDeleteIfReleased {
                            hapticFeedback()
                            willDeleteIfReleased.toggle()
                        } else if offset.width > -deletionDistance, willDeleteIfReleased {
                            hapticFeedback()
                            willDeleteIfReleased.toggle()
                        }
                    }
                    .onEnded { _ in
                        if offset.width < -deletionDistance {
                            delete()
                        } else if offset.width < -halfDeletionDistance {
                            offset.width = -tappableDeletionWidth
                            initialOffset.width = -tappableDeletionWidth
                        } else {
                            offset = .zero
                            initialOffset = .zero
                        }
                    }
            )
            .animation(.interactiveSpring(), value: offset)
    }

    private func edit() {
        offset = .zero
        initialOffset = .zero
        editAction?()
    }

    private func delete() {
        // offset.width = -contentWidth

        offset = .zero
        initialOffset = .zero
        deleteAction?()
    }

    private func hapticFeedback() {
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred()
    }

    // MARK: Constants

    let deletionDistance = CGFloat(200)
    let halfDeletionDistance = CGFloat(50)
    let tappableDeletionWidth = CGFloat(100)
}
