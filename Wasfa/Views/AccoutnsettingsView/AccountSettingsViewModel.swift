import Foundation
import SwiftUI

class AccountSettingsViewModel: SuperViewModel {
    @Published var nameText: String = ""
    @Published var phoneText: String = ""
    @Published var emailText: String = ""
    @Published var selectedCountryCode: CountryCode = .kuwait
    @Published var profileDetailsModel: ProfileDetailsModel?
    
    override init() {
        super.init()
        getUserDetails()
    }
    
    var isSaveButtonDisabled: Bool {
        nameText.isEmpty || phoneText.isEmpty || emailText.isEmpty
    }
    
    func getUserDetails() {
        onApiCall(api.profileDetails, parameters: emptyDictionary) {
            self.profileDetailsModel = $0.data?.first
            if let profileDetailsModel = self.profileDetailsModel {
                self.assignProfileDetails(profileDetailsModel: profileDetailsModel)
            }
        }
    }
    
    func updateProfileDetails() {
        let parameters: [String: Any] = ["name": nameText, "phone": phoneText, "email": emailText]
        onApiCall(api.updateProfile, parameters: parameters) {
            if $0.success {
                self.getUserDetails()
            }
        }
    }
    
    func assignProfileDetails(profileDetailsModel: ProfileDetailsModel) {
        nameText = profileDetailsModel.name ?? ""
        emailText = profileDetailsModel.email ?? ""
        let result = profileDetailsModel.phone.extractCountryCodeAndPhoneNumber(from: CountryCode.allCases)
        phoneText = result.phoneNumber
        selectedCountryCode = result.countryCode
    }
}
