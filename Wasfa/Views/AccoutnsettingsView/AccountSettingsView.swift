import SwiftUI

struct AccountSettingsView: View {
    @StateObject var viewModel = AccountSettingsViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            MainScrollBody(backButtonWithTitle: "Account Settings") {
                VStack(alignment: .leading, spacing: 16.relativeHeight) {
                    // Using InputFieldView for Alternative Phone Number
                    InputFieldView(
                        title: "Name",
                        placeholder: "Enter Your Name",
                        text: self.$viewModel.nameText,
                        isRequired: true
                    )
                    InputFieldView(
                        title: "Phone Number",
                        placeholder: "Enter Phone Number",
                        text: self.$viewModel.phoneText,
                        selectedCountryCode: $viewModel.selectedCountryCode,
                        isRequired: true,
                        isPhoneNumber: true,
                        isDisabled: true
                    )
                    
                    InputFieldView(
                        title: "Email",
                        placeholder: "Enter Your Email",
                        text: self.$viewModel.emailText,
                        isRequired: true
                    )
                }
                .padding(.horizontal, getRelativeWidth(24.0))
                .padding(.top, getRelativeHeight(30.0))
                .padding(.bottom, getRelativeHeight(10.0))

            }.background(ColorConstants.WhiteA700)
                .safeAreaInset(edge: .bottom) {
                    Button(action: viewModel.updateProfileDetails, label: {
                        HStack(spacing: 0) {
                            Text(StringConstants.kLblSave)
                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
                                .fontWeight(.bold)
                                .padding(.horizontal, getRelativeWidth(30.0))
                                .padding(.vertical, getRelativeHeight(14.0))
                                .foregroundColor(ColorConstants.WhiteA700)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.center)
                                .frame(width: getRelativeWidth(348.0), height: getRelativeHeight(54.0),
                                       alignment: .center)
                                .background(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0,
                                                           bottomRight: 8.0)
                                        .fill(ColorConstants.Blue300))
                                .shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 0)
                                .padding(.horizontal, getRelativeWidth(22.0))
                        }
                    })
                    .padding(.bottom)
                    .disableWithOpacity(viewModel.isSaveButtonDisabled)
                }
        }
        .injectEnvironmentValues(viewModel)
    }
}

#Preview {
    NavigationStack {
        AccountSettingsView()
    }
}
