//import SwiftUI
//
//struct Productcard6Cell: View {
//    var body: some View {
//        HStack {
//            ZStack {
//                Image("img_5_2_1")
//                    .resizable()
//                    .frame(width: getRelativeWidth(79.0), height: getRelativeHeight(87.0),
//                           alignment: .leading)
//                    .scaledToFit()
//                    .background(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0,
//                                               bottomRight: 8.0))
//                    .padding(.all, getRelativeWidth(5.0))
//                    .padding(.vertical, getRelativeHeight(5.0))
//                    .padding(.horizontal, getRelativeWidth(5.0))
//            }
//            .hideNavigationBar()
//            .frame(width: getRelativeWidth(89.0), height: getRelativeHeight(97.0),
//                   alignment: .leading)
//            .overlay(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0, bottomRight: 8.0)
//                .stroke(ColorConstants.Black90001,
//                        lineWidth: 1))
//            .background(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0,
//                                       bottomRight: 8.0)
//                    .fill(ColorConstants.WhiteA700))
//            VStack(alignment: .leading, spacing: 0) {
//                Text(StringConstants.kMsgAnivageneVials2)
//                    .font(FontScheme.kRobotoRomanRegular(size: getRelativeHeight(13.0)))
//                    .fontWeight(.regular)
//                    .foregroundColor(ColorConstants.Black90001)
//                    .minimumScaleFactor(0.5)
//                    .multilineTextAlignment(.leading)
//                    .frame(width: getRelativeWidth(198.0), height: getRelativeHeight(37.0),
//                           alignment: .leading)
//                HStack {
//                    Text(StringConstants.kLbl15012024)
//                        .font(FontScheme.kRobotoRomanRegular(size: getRelativeHeight(12.0)))
//                        .fontWeight(.regular)
//                        .foregroundColor(ColorConstants.Black90001)
//                        .minimumScaleFactor(0.5)
//                        .multilineTextAlignment(.leading)
//                        .frame(width: getRelativeWidth(64.0), height: getRelativeHeight(15.0),
//                               alignment: .leading)
//                    Text(StringConstants.kLblKd0000)
//                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
//                        .fontWeight(.bold)
//                        .foregroundColor(ColorConstants.Blue600)
//                        .minimumScaleFactor(0.5)
//                        .multilineTextAlignment(.leading)
//                        .frame(width: getRelativeWidth(63.0), height: getRelativeHeight(20.0),
//                               alignment: .leading)
//                        .padding(.leading, getRelativeWidth(103.0))
//                }
//                .frame(width: getRelativeWidth(232.0), height: getRelativeHeight(20.0),
//                       alignment: .leading)
//                HStack {
//                    Button(action: {}, label: {
//                        HStack(spacing: 0) {
//                            Text(StringConstants.kLblReOrder)
//                                .font(FontScheme.kRobotoRomanRegular(size: getRelativeHeight(11.0)))
//                                .fontWeight(.regular)
//                                .padding(.trailing, getRelativeWidth(19.0))
//                                .padding(.vertical, getRelativeHeight(6.0))
//                                .foregroundColor(ColorConstants.Blue300)
//                                .minimumScaleFactor(0.5)
//                                .multilineTextAlignment(.leading)
//                                .frame(width: getRelativeWidth(105.0),
//                                       height: getRelativeHeight(26.0), alignment: .leading)
//                                .overlay(RoundedCorners(topLeft: 5.0, topRight: 5.0,
//                                                        bottomLeft: 5.0,
//                                                        bottomRight: 5.0)
//                                        .stroke(ColorConstants.Blue300,
//                                                lineWidth: 1))
//                                .background(RoundedCorners(topLeft: 5.0, topRight: 5.0,
//                                                           bottomLeft: 5.0, bottomRight: 5.0)
//                                        .fill(Color.clear.opacity(0.7)))
//                        }
//                    })
//                    .frame(width: getRelativeWidth(105.0), height: getRelativeHeight(26.0),
//                           alignment: .leading)
//                    .overlay(RoundedCorners(topLeft: 5.0, topRight: 5.0, bottomLeft: 5.0,
//                                            bottomRight: 5.0)
//                            .stroke(ColorConstants.Blue300,
//                                    lineWidth: 1))
//                    .background(RoundedCorners(topLeft: 5.0, topRight: 5.0, bottomLeft: 5.0,
//                                               bottomRight: 5.0)
//                            .fill(Color.clear.opacity(0.7)))
//                    Text(StringConstants.kLblOrderInfo)
//                        .font(FontScheme.kRobotoRomanRegular(size: getRelativeHeight(11.0)))
//                        .fontWeight(.regular)
//                        .foregroundColor(ColorConstants.Gray90001)
//                        .minimumScaleFactor(0.5)
//                        .multilineTextAlignment(.leading)
//                        .frame(width: getRelativeWidth(51.0), height: getRelativeHeight(13.0),
//                               alignment: .leading)
//                        .padding(.leading, getRelativeWidth(45.0))
//                }
//                .frame(width: getRelativeWidth(203.0), height: getRelativeHeight(26.0),
//                       alignment: .leading)
//                .padding(.top, getRelativeHeight(10.0))
//            }
//            .frame(width: getRelativeWidth(235.0), height: getRelativeHeight(96.0),
//                   alignment: .leading)
//            .padding(.leading, getRelativeWidth(11.0))
//        }
//        .frame(width: getRelativeWidth(353.0), alignment: .leading)
//        .overlay(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0, bottomRight: 8.0)
//            .stroke(ColorConstants.Black90001,
//                    lineWidth: 1))
//        .background(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0, bottomRight: 8.0)
//            .fill(Color.clear.opacity(0.7)))
//        .hideNavigationBar()
//    }
//}
//
///* struct Productcard6Cell_Previews: PreviewProvider {
//
// static var previews: some View {
// 			Productcard6Cell()
// }
// } */
