import SwiftUI

struct OrderHistoryTabContainerView: View {
    @StateObject var viewModel = OrderHistoryTabContainerViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    
    @Namespace private var animation
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            MainScrollBody(backButtonWithTitle: "Order History") {
                LazyVStack(spacing: 16, pinnedViews: [.sectionHeaders]) {
                    Section {
                        OrderHistoryView(selectedTab: viewModel.selectedTab,orderHistoryList: viewModel.headerBasedList, onSelect: viewModel.onSelectOrder, onTrack: viewModel.onTrackOrder, onCancel: viewModel.onCancelOrder, onReorder: viewModel.onReorder)
                            
                            
                    } header: {
                        HStack(spacing: 0) {
                            ForEach(OrderHistoryTabType.allCases, id: \.self) { type in
                                let isSelectedTab = viewModel.selectedTab == type
                                
                                But<PERSON>(action: {
                                    viewModel.updateHeaderTitle(type)
                                    
                                }) {
                                    VStack(spacing: 0) {
                                        Text(LocalizedStringKey(type.title))
                                            // add custom font named "Poppins" with 16px size and semibold
                                            .font(Font.custom("Poppins", size: 12.relativeFontSize))
                                            .fontWeight(.semibold)
                                            .fixedSize()
                                            .kerning(0.5)
                                            .multilineTextAlignment(.center)
                                            .foregroundColor(Color(red: 0.4, green: 0.39, blue: 0.39))
                                            .padding(.horizontal, 12)
                                            .padding(.top, 10)
                                        
                                        
                                        if isSelectedTab {
                                            Rectangle()
                                                .frame(height: 2.relativeHeight)
                                                .foregroundColor(Color(red: 0.11, green: 0.62, blue: 0.85))
                                                .frame(maxWidth: .infinity)
                                                .offset(x: 0, y: 11.relativeHeight)
                                              
                                                .matchedGeometryEffect(id: "tab.selected", in: animation)
                                        }
                                        
                                       
                                    }
                                        
                                      
                                }
                               
                                .frame(maxWidth: .infinity)
                            }
                        }
                        .animation(.bouncy, value: viewModel.selectedTab)
                        .frame(height: getRelativeHeight(45.0))
//                        .overlay(Capsule().stroke(ColorConstants.Black90001, lineWidth: 1))
                        .padding(.horizontal)
                        .padding(.vertical, 4)
                        .overlay(Divider().frame(height: 1.relativeHeight).background(Color.init(hex: "#F0F0F0")), alignment: .bottom)
                        .background(ColorConstants.WhiteA700)
                       
                       
                    } footer: {}
                }
               
                .padding(.top, getRelativeHeight(16.0))
                .background(ColorConstants.WhiteA700)
            }
        }
        .injectEnvironmentValues(viewModel)
        .alert("Cancel Order", isPresented: $viewModel.showCancelAlert) {
            
            TextField("Enter reason for cancellation", text: $viewModel.cancelReason, axis: .vertical)
                .lineLimit(3...6)
            Button("Cancel", role: .cancel) {
                viewModel.showCancelAlert = false
            }
            Button("Confirm") {
                viewModel.confirmCancelOrder()
            }
            .disableWithOpacity(viewModel.cancelReason.isEmpty)
        } message: {
            Text("Please provide a reason for cancelling this order")
        }
        
    }
}




#Preview {
    NavigationStack {
        OrderHistoryTabContainerView()
    }
}
