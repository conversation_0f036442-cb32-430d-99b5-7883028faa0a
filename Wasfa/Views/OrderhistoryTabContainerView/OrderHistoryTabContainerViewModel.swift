import Foundation
import SwiftUI

enum OrderHistoryTabType: String, CaseIterable {
    case onProgress
    case completed
    case cancelled

    var title: String {
        switch self {
        case .onProgress: return "On Progress"
        case .completed: return "Completed"
        case .cancelled: return "Cancelled"
        }
    }
}

class OrderHistoryTabContainerViewModel: SuperViewModel {
    @Published var deliveryStatusList: [String] = ["pending", "confirmed", "picked_up", "on_the_way", "delivered", "cancelled"]
    @Published var selectedTabviewIndex: Int = 0
    @Published var orderHistoryList: [OrderHistoryModel] = []
    @Published private(set) var selectedTab: OrderHistoryTabType = .onProgress

    // New state variables for cancel alert
    @Published var showCancelAlert = false
    @Published var cancelReason = ""
    @Published private var orderIdToCancel: Int?

    var headerBasedList: [OrderHistoryModel] {
        switch self.selectedTab {
        case .onProgress:
            return self.orderHistoryList.filter { $0.deliveryStatus == "pending" || $0.deliveryStatus == "confirmed" || $0.deliveryStatus == "picked_up" || $0.deliveryStatus == "on_the_way" }
        case .completed:
            return self.orderHistoryList.filter { $0.deliveryStatus == "delivered" }
        case .cancelled:
            return self.orderHistoryList.filter { $0.deliveryStatus == "cancelled" }
        }
    }

    override init() {
        super.init()
        self.getOrderHistoryData()
    }

    func updateHeaderTitle(_ value: OrderHistoryTabType) {
        self.selectedTab = value
    }

    func onSelectOrder(_ id: Int) {
      
            
        routerManager?.push(to: .orderInfo(id), appState: appState)
    }

    func onTrackOrder(_ id: Int) {
        routerManager?.push(to: .trackOrder(id), appState: appState)
    }

    func onReorder(_ id: Int) {
        onApiCall(api.reorder, parameters: ["orderId": id]) { response in
            if response.success {
                self.appState?.showToast(.init(type: .success, message: response.message))
                self.routerManager?.push(to: .cart(), appState: self.appState)
            }
        }
    }

    func onCancelOrder(_ id: Int) {
        self.orderIdToCancel = id
        self.cancelReason = ""
        self.showCancelAlert = true
    }

    func confirmCancelOrder() {
        guard let id = orderIdToCancel, !cancelReason.isEmpty else { return }

        onApiCall(api.cancelOrder, parameters: ["id": id, "reason": self.cancelReason]) { response in
            if response.success {
                self.appState?.showToast(.init(type: .success, message: response.message))
                self.getOrderHistoryData()
            }
            self.showCancelAlert = false
            self.orderIdToCancel = nil
            self.cancelReason = ""
        }
    }

    func getOrderHistoryData() {
        onApiCall(api.orderHistory, parameters: emptyDictionary) { response in
            self.orderHistoryList = response.data ?? []
        }
    }
}
