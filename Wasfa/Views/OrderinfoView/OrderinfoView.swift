import SwiftUI

struct OrderinfoView: View {
    @StateObject var viewModel: OrderinfoViewModel
    
    init(orderID: Int) {
        self._viewModel = StateObject(wrappedValue: OrderinfoViewModel(orderID: orderID))
    }
    
    var body: some View {
        SuperView(pageState: $viewModel.pageState){
            MainScrollBody(backButtonWithTitle: "Order Info") {
                
                if let orderDetailsModel = viewModel.orderDetailsModel {
                    VStack(alignment: .leading, spacing: 0) {
                        Text("Order#: \(orderDetailsModel.code)")
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.Blue600)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(126.0), height: getRelativeHeight(22.0),
                                   alignment: .topLeading)
                            .padding(.leading, getRelativeWidth(18.0))
                        Text(StringConstants.kLblOrderSummery)
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.Black90001)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(108.0), height: getRelativeHeight(20.0),
                                   alignment: .topLeading)
                            .padding(.top, getRelativeHeight(28.0))
                            .padding(.leading, getRelativeWidth(18.0))
                        VStack(spacing: 0) {
                            ScrollView(.vertical, showsIndicators: false) {
                                LazyVStack {
                                    ForEach(orderDetailsModel.itemsList) { 
                                        OrderProductCard(model: $0)
                                    }
                                }
                            }
                        }
                        .frame(width: getRelativeWidth(355.0), alignment: .center)
                        .padding(.top, getRelativeHeight(13.0))
                        .padding(.horizontal, getRelativeWidth(17.0))
                        VStack(spacing: 12) {
                            InfoCardView(image: .imgPinFill, title: "Delivery to", subtitle: orderDetailsModel.address.fullAddress.localize, isAddress: true)
                            InfoCardView(image: .imgGroup148, title: "Order date", subtitle: orderDetailsModel.date.toFormattedDate().localize)
                            InfoCardView(image: .orderInfoPayment, title: "Payment option", subtitle: orderDetailsModel.paymentType.uppercased().localize)
                        }
                        .frame(width: getRelativeWidth(364.0), alignment: .center)
                        .padding(.top, getRelativeHeight(29.0))
                        .padding(.leading, getRelativeWidth(16.0))
                        .padding(.trailing, getRelativeWidth(9.0))
                    }
                    
                    .padding(.top, getRelativeHeight(16.0))
                    
                }
                
               
            }
            .toolbarBackground(.visible, for: .navigationBar)
            .background(ColorConstants.WhiteA700)
            .safeAreaInset(edge: .bottom) {
                SafeAreaViewSection
            }
        }
        
    }
    
    var SafeAreaViewSection: some View {
        VStack(alignment: .leading) {
            if let orderDetailsModel = viewModel.orderDetailsModel {
                Text(StringConstants.kLblOrderInfo)
                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
                    .fontWeight(.bold)
                    .foregroundColor(ColorConstants.Black90001)
                    .multilineTextAlignment(.leading)
                    .padding(.top, getRelativeHeight(23.0))
                    .padding(.leading, getRelativeWidth(19.0))
               
                VStack(spacing: 8) {
                    orderInfoRow(label: StringConstants.kLblSubtotal, value: orderDetailsModel.subTotal, topPadding: 16.relativeHeight)
                    
                    orderInfoRow(label: StringConstants.kLblDiscount, value: orderDetailsModel.discount)
                    
                    orderInfoRow(label: StringConstants.kLblDelivery, value: orderDetailsModel.deliveryCharge)
                }
               
                Divider()
                    .background(ColorConstants.Teal90026)
                    .padding(.top, getRelativeHeight(9.0))
               
                orderInfoRow(label: StringConstants.kLblTotal, value: orderDetailsModel.grandTotal, labelFont: FontScheme.kNunitoBold(size: 16.relativeFontSize).bold(), valueFont: FontScheme.kNunitoBold(size: 16.relativeFontSize).bold(), topPadding: 6.0)
                    .padding(.bottom)
            }
        }
        
        .background(RoundedCorners(topLeft: 23.0, topRight: 23.0)
            .fill(ColorConstants.WhiteA700))
        .shadow(color: ColorConstants.Black90021, radius: 7, x: 3, y: -10)
//        .padding(.top, getRelativeHeight(74.0))
    }
    
    
    func orderInfoRow(label: LocalizedStringKey, value: String, labelFont: Font = FontScheme.kRobotoRomanRegular(size: 14.relativeFontSize).weight(.regular), valueFont: Font = FontScheme.kInterMedium(size: 15.relativeFontSize).weight(.medium), topPadding: CGFloat = 0.0) -> some View {
        HStack {
            Text(label)
                .font(labelFont)
                .foregroundColor(ColorConstants.Black90001)
                .multilineTextAlignment(.leading)
            Spacer()
            Text(value)
                .font(valueFont)
                .foregroundColor(ColorConstants.Blue300)
                .multilineTextAlignment(.leading)
        }
        .padding(.top, topPadding)
        .padding(.horizontal, getRelativeWidth(25.0))
    }
}

#Preview {
    NavigationStack {
        OrderinfoView(orderID: 32).attachAllEnvironmentObjects()
    }
}


extension String {
    func toFormattedDate(inputFormat: String = "dd-MM-yyyy", outputFormat: String = "MMM dd, yyyy") -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = inputFormat
        dateFormatter.locale = Locale(identifier: "en_US") // Ensure English month names

        if let date = dateFormatter.date(from: self) {
            dateFormatter.dateFormat = outputFormat
            return dateFormatter.string(from: date)
        }
        return "Invalid Date"
    }
}
