import SwiftUI

struct InfoCardView: View {
    let image: ImageResource
    let title, subtitle: LocalizedStringKey
    var isAddress: Bool = false
    
    var body: some View {
        HStack {
            ZStack {
                Image(image)
                    .resizable()
                    .scaledToFit()
                    .frame(width: getRelativeWidth(30.0), height: getRelativeWidth(37.0),
                           alignment: .center)
                   
            }
          
            .frame(width: getRelativeWidth(35.0), height: getRelativeWidth(37.0),
                   alignment: .center)
            .background(RoundedCorners(topLeft: 4.0, topRight: 4.0, bottomLeft: 4.0,
                                       bottomRight: 4.0)
                    .fill(ColorConstants.WhiteA700).shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 0))
           
            VStack(alignment: .leading, spacing: 0) {
                Text(title)
                    .font(FontScheme.kRobotoRomanMedium(size: getRelativeHeight(14.0)))
                    .fontWeight(.medium)
                    .foregroundColor(ColorConstants.Blue600)
                    .multilineTextAlignment(.leading)
                    
                Text(subtitle)
                    .font(FontScheme.kRobotoRomanRegular(size: getRelativeHeight(11.0)))
                    .fontWeight(.regular)
                    .foregroundColor(isAddress ?  ColorConstants.Gray70001 : ColorConstants.Gray900)
                    .multilineTextAlignment(.leading)
                    
                    .padding(.top, getRelativeHeight(6.0))
            }
            .frame(width: getRelativeWidth(307.0), height: getRelativeHeight(36.0),
                   alignment: .leading)
            .padding(.leading, getRelativeWidth(8.0))
        }
        .frame(width: getRelativeWidth(352.0), alignment: .leading)
        .hideNavigationBar()
    }
}

//struct Addressitem1Cell_Previews: PreviewProvider {
//    static var previews: some View {
//        InfoCardView(image: .imgPinFill, title: "Delivery to", subtitle: StringConstants.kMsg4thFloorAlZumorrodah)
//    }
//}
