import SwiftUI

struct OrderProductCard: View {
    
    let model:ItemsList
    var body: some View {
        HStack {
            ZStack {
                NetworkImageView(path: model.thumbnailImage)
                    
                    .frame(width: getRelativeWidth(55.0), height: getRelativeHeight(61.0),
                           alignment: .leading)
                    .scaledToFit()
                    .background(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0,
                                               bottomRight: 8.0).fill(.clear))
                    .padding(.vertical, getRelativeHeight(4.0))
                    .padding(.horizontal, getRelativeWidth(3.57))
            }
            .frame(width: getRelativeWidth(63.0), height: getRelativeHeight(69.0),
                   alignment: .leading)
            .overlay(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0, bottomRight: 8.0)
                .stroke(ColorConstants.Black90001,
                        lineWidth: 1))
            .background(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0,
                                       bottomRight: 8.0)
                    .fill(ColorConstants.WhiteA700))
            VStack(alignment: .leading, spacing: 0) {
                Text(model.productName)
                    .font(FontScheme.kRobotoRomanMedium(size: getRelativeHeight(13.0)))
                    .fontWeight(.medium)
                    .foregroundColor(ColorConstants.Black90001)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(width: getRelativeWidth(199.0), height: getRelativeHeight(35.0),
                           alignment: .leading)
                HStack {
                    Text(model.price)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Blue600)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(63.0), height: getRelativeHeight(20.0),
                               alignment: .leading)
                    Text("QTY : \(model.quantity)")
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Gray600)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(42.0), height: getRelativeHeight(17.0),
                               alignment: .leading)
                        .padding(.leading, getRelativeWidth(142.0))
                }
                .frame(width: getRelativeWidth(249.0), height: getRelativeHeight(20.0),
                       alignment: .leading)
                .padding(.top, getRelativeHeight(17.0))
            }
            .frame(width: getRelativeWidth(251.0), height: getRelativeHeight(73.0),
                   alignment: .leading)
            .padding(.leading, getRelativeWidth(11.0))
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .frame(width: getRelativeWidth(353.0), alignment: .leading)
        .overlay(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0, bottomRight: 8.0)
            .stroke(ColorConstants.Black90001,
                    lineWidth: 1))
        .background(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0, bottomRight: 8.0)
            .fill(Color.clear.opacity(0.7)))
        .hideNavigationBar()
    }
}

//struct Productcard7Cell_Previews: PreviewProvider {
//    static var previews: some View {
//        OrderProductCard()
//    }
//}
