import Foundation
import SwiftUI

class OrderinfoViewModel: SuperViewModel {
    @Published var orderDetailsModel: OrderDetailsModel?
    
    let orderID: Int
    init(orderID: Int) {
        self.orderID = orderID
        
        super.init()
        
        getOrderInfo()
    }
    
    func getOrderInfo() {
        let parameters: [String: Any] = ["id": orderID]
        onApiCall(api.orderDetails, parameters: parameters) { response in
            self.orderDetailsModel = response.data?.first
        }
    }
}
