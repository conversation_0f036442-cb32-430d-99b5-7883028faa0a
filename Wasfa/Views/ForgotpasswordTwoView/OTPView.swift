import SwiftUI

struct OTPVerificationView: View {
    let phone: String
    @StateObject var viewModel: SignInViewModel = .init()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            MainScrollBody {
                VStack {
                    VStack(alignment: .leading, spacing: 12.relativeHeight){
                        Text("OTP Verification")
                            .font(FontScheme.kPoppins(size: 30.relativeFontSize))
                            .fontWeight(.bold)
                            .foregroundColor(Color.init(hex: "#1E232C"))
                            .multilineTextAlignment(.leading)
                            .padding(.top, getRelativeHeight(8.0))
                        
                        Text("Enter the verification code we just sent on your phone number \(Text(phone).fontWeight(.bold).foregroundColor(Color.init(hex: "#1E232C")))")
                            .font(FontScheme.kPoppins(size: 16.relativeFontSize))
                            .fontWeight(.medium)
                            .foregroundColor(Color.init(hex: "#838BA1"))
                            .multilineTextAlignment(.leading)
                        
                           
                    }.padding(.horizontal, 16.relativeWidth)
                    
                    let otpViewWidth: CGFloat = UIScreen.main.bounds.width * 1
                    
                    OTPView(text: $viewModel.otpText,
                            width: otpViewWidth, // Adjusted width
                           height: 110.0,  // Further increased height
                           fieldsCount: 5,
                           displayType: .roundedCorner,
                           defaultBorderColor: ColorConstants.Blue600,
                           filledBorderColor: ColorConstants.Blue600,
                           fieldSize: 70, // Further increased field size
                           separatorSpace: 12, // Further increased separator space
                           fieldFont: UIFont(name: "Nunito-Bold", size: 28) ?? UIFont.systemFont(ofSize: 28), // Further increased font size
                           textColor: ColorConstants.Black90001)
                    .frame(width: otpViewWidth,height: 110.0) // Matching container height
                    .onChange(of: viewModel.otpText) { oldValue, newValue in
                        print("OTP: \(newValue)")
                    }
                    Button(action: {viewModel.verifyOtp(phoneText: phone)}, label: {
                        HStack(spacing: 0) {
                            Text(StringConstants.kLblVerify)
                                .font(FontScheme.kPoppins(size: 20.relativeFontSize))
                                .fontWeight(.semibold)
                                .padding(.horizontal, getRelativeWidth(30.0))
                                .padding(.vertical, getRelativeHeight(14.0))
                                .foregroundColor(ColorConstants.WhiteA700)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.center)
                                .frame(width: getRelativeWidth(348.0),
                                       height: getRelativeHeight(54.0), alignment: .center)
                                .background(RoundedCorners(topLeft: 8.0, topRight: 8.0,
                                                           bottomLeft: 8.0, bottomRight: 8.0)
                                        .fill(ColorConstants.Blue300))
//                                .shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 0)
                                .padding(.vertical, getRelativeHeight(16.0))
                        }
                    }).disableWithOpacity(viewModel.disableSendOtp)
                    
                }
//                .padding(.horizontal, 12.relativeWidth)
                .padding(.vertical, 16.relativeHeight)
            }
        }
        .background(ColorConstants.WhiteA700)
        .injectEnvironmentValues(viewModel)
    }
}

#Preview {
    NavigationStack {
        OTPVerificationView(phone: "12345678")
            .attachAllEnvironmentObjects()
    }
}
