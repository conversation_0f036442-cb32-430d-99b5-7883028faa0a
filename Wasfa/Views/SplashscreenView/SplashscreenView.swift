import SwiftUI

struct SplashScreenView: View {
    @StateObject private var viewModel: SplashViewModel = .init()
    @EnvironmentObject private var appState: AppState
    @State private var isLoadingComplete: Bool = false
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            VStack {
                VStack {
                    Image("img_wasfa_logo_1")
                        .resizable()
                        .frame(width: getRelativeWidth(170.0), height: getRelativeHeight(69.0),
                               alignment: .center)
                        .scaledToFit()
                        .clipped()
                        .padding(.bottom, getRelativeHeight(5.0))
                }
                .frame(width: UIScreen.main.bounds.width, alignment: .center)
                .padding(.top, getRelativeHeight(30.0))
                .padding(.bottom, getRelativeHeight(10.0))
            }

            .background(content: {
                Image(.imgSplashScreen)
                    .resizable()
                    .scaledToFill()
                    .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height)
            })
            .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height)
            .background(ColorConstants.WhiteA700)
            .ignoresSafeArea()
            .hideNavigationBar()
            .onLoad {
                if !appState.startUpLanguageShown {
                    Utilities.enQueue(after: .now() + 4) { isLoadingComplete = true }
                }
            }
            .overlay(alignment: .bottom, content: {
                Group {
                  
                        VStack {
                            Text("Choose your Language")
                                .font(FontScheme.kNunitoExtraBold(size: 28.0.relativeFontSize))
                                .fontWeight(.bold)
                                .foregroundColor(ColorConstants.Blue300)

                            VStack(spacing: 16) {
                                Button {
                                    appState.updateAppLocale(to: .english)
                                    viewModel.createGenerateToken(onComplete: {
                                        self.appState.initialScreen = .dashboard
                                    })
                                } label: {
                                    Text("English")
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
                                        .fontWeight(.bold)
                                        .padding(.horizontal, getRelativeWidth(30.0))
                                        .padding(.vertical, getRelativeHeight(14.0))
                                        .foregroundColor(ColorConstants.WhiteA700)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.center)
                                        .frame(width: getRelativeWidth(348.0), height: getRelativeHeight(54.0),
                                               alignment: .center)
                                        .background(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0,
                                                                   bottomRight: 8.0)
                                                .fill(ColorConstants.Blue300))
                                        .shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 0)
                                }

                                Button {
                                    appState.updateAppLocale(to: .arabic)
                                    viewModel.createGenerateToken(onComplete: {
                                        self.appState.initialScreen = .dashboard
                                    })

                                } label: {
                                    Text("عربي")
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
                                        .fontWeight(.bold)
                                        .padding(.horizontal, getRelativeWidth(30.0))
                                        .padding(.vertical, getRelativeHeight(14.0))
                                        .foregroundColor(ColorConstants.WhiteA700)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.center)
                                        .frame(width: getRelativeWidth(348.0), height: getRelativeHeight(54.0),
                                               alignment: .center)
                                        .background(RoundedCorners(topLeft: 8.0, topRight: 8.0, bottomLeft: 8.0,
                                                                   bottomRight: 8.0)
                                                .fill(ColorConstants.Blue300))
                                        .shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 0)
                                }
                            }
                        }
                        .padding(.bottom, 64.relativeHeight)
                        .transition(.move(edge: .bottom))
                        .visibility(isLoadingComplete ? .visible : .gone)
                    
                }
                .animation(.bouncy, value: isLoadingComplete)
            })
        }
    }
}

struct SplashScreenView_Previews: PreviewProvider {
    static var previews: some View {
        SplashScreenView()
    }
}
