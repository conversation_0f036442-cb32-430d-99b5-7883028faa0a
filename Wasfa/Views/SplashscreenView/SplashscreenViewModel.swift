import Foundation
import SwiftUI

class SplashViewModel: SuperViewModel {
    @Published var nextScreen: String? = nil

    func createGenerateToken(onComplete: @escaping () -> Void) {
        guard UserDefaultsSecure.sharedInstance.getGeneratedToken() == nil else { onComplete(); return }
//        let parameters: [String: Any] = GenerateTokenRequest(deviceID: !AppState.fcmToken.isEmpty ?  AppState.fcmToken : AppState.deviceID).toDictionary

        let parameters: [String: Any] = GenerateTokenRequest(deviceID: AppState.fcmToken).toDictionary
        onApiCall(api.generateToken, parameters: parameters, onSuccess: {
            if $0.success {
                UserDefaultsSecure.sharedInstance.setGeneratedTokenStringValue(value: $0.data?.token)
                self.saveFCMTokenToBackend(onComplete: onComplete)
            }

        })
    }

    func saveFCMTokenToBackend(onComplete: @escaping () -> Void) {
        onApiCall(api.saveFcmToken, parameters: ["deviceToken": AppState.fcmToken]) {
            if $0.success {
                onComplete()
            }
        }
    }
}
