//
//  FilterSortView.swift
//  Wasfa
//
//  Created by Apple on 08/02/2025.
//
import Sliders
import SwiftUI
import WrappingHStack

// MARK: - View

struct FilterSortView: View {
    @StateObject private var viewModel: FilterSortViewModel
    @State private var priceRange: ClosedRange<Double> = 0 ... 1000
    let categories: [CategoryModel.Category]
    let onApply: (ProductListingRequest?) -> Void
    @Environment(\.dismiss) var dismiss
    
    init(
        filter: ProductListingRequest?,
        categories: [CategoryModel.Category],
        onApply: @escaping (ProductListingRequest?) -> Void,
        config: FilterSortViewModel.FilterConfiguration = .default
    ) {
        _viewModel = StateObject(wrappedValue: FilterSortViewModel( filter: filter,config: config))
        self.categories = categories
        self.onApply = onApply
    }
    
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            NavigationView {
                VStack {
                    ScrollView {
                        LazyVStack(alignment: .leading, spacing: 20) {
                            sortOptionsSection
                            priceFilterSection
                            Divider()
                            categoriesSection
                            Divider()
                            brandsSection
                            Divider()
                        }
                        .padding()
                    }
                    .scrollIndicators(.hidden)
                    .animation(.easeInOut(duration: 0.2), value: viewModel.isCategoryExpanded)
                    .animation(.easeInOut(duration: 0.2), value: viewModel.isBrandExpanded)
                    actionButtons
                }
                .background(ColorConstants.WhiteA700)
                .tint(ColorConstants.Blue300)
                .navigationTitle("Filter & Sorts")
                .navigationBarItems(leading: closeButton)
                .task {
                    viewModel.setCategories(categories)
                }
            }.environment(\.colorScheme, .light)
        }
    }
    
    private var sortOptionsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Sort")
                .font(.custom("Nunito", size: 18).bold())
            
            WrappingHStack(SortKey.allCases, alignment: .leading, spacing: .constant(12.relativeWidth), lineSpacing: 12.relativeHeight) { option in
                Button(action: {
                    viewModel.filter?.sortKey = viewModel.filter?.sortKey == option ? nil : option
                }) {
                    Text(option.title)
                        .font(.custom("Nunito", size: 14))
                        .foregroundColor(viewModel.filter?.sortKey == option ? .white : ColorConstants.Blue300)
                        .padding(.horizontal, 20)
                        .lineLimit(1)
                        .padding(.vertical, 10)
                        .background(viewModel.filter?.sortKey == option ? ColorConstants.Blue300 : Color.clear)
                        .overlay(
                            Capsule()
                                .stroke(ColorConstants.Blue300, lineWidth: 1)
                        )
                        .fixedSize()
                        .clipShape(Capsule())
                }
            }
        }
    }
    
    private func sortOptionRow(_ option: SortKey) -> some View {
        HStack {
            Text(option.title)
                .font(.custom("Nunito", size: 16))
            Spacer()
            radioButton(isSelected: viewModel.filter?.sortKey == option)
        }
        .padding(.vertical, 5)
        .onTapGesture {
            viewModel.filter?.sortKey = viewModel.filter?.sortKey == option ? nil : option
        }
    }
    
    private var priceFilterSection: some View {
        VStack(alignment: .leading, spacing: 0) {
            Text("Price Range")
                .font(.custom("Nunito", size: 18).bold())
            
            RangeSlider(range: $priceRange, in: viewModel.priceRange)
                .rangeSliderStyle(
                    HorizontalRangeSliderStyle(track:
                        HorizontalRangeTrack(view: Capsule().foregroundColor(Color(hex: "#1B9ED9")))
                            .background(Capsule().foregroundColor(Color(hex: "#F3F3F3")))
                            .frame(height: 5.relativeHeight),
                        lowerThumb: Circle().foregroundColor(Color(hex: "#1B9ED9")),
                        upperThumb: Circle().foregroundColor(Color(hex: "#1B9ED9")),
                        lowerThumbSize: CGSize(width: 24.relativeFontSize, height: 24.relativeFontSize),
                        upperThumbSize: CGSize(width: 24.relativeFontSize, height: 24.relativeFontSize),
                        options: .defaultOptions))
            
            HStack {
                Text(viewModel.formatPrice(priceRange.lowerBound))
                    .font(.custom("Poppins", size: 14.relativeFontSize).weight(.semibold))
                    .contentTransition(.numericText(value: priceRange.lowerBound))
                    .animation(.bouncy, value: priceRange)
                Spacer()
                Text(viewModel.formatPrice(priceRange.upperBound))
                    .font(.custom("Poppins", size: 14.relativeFontSize).weight(.semibold))
                    .contentTransition(.numericText(value: priceRange.lowerBound))
                    .animation(.bouncy, value: priceRange)
            }
        }
    }
    
    private var categoriesSection: some View {
        VStack(alignment: .leading) {
            sectionHeader(title: "Product Category", isExpanded: viewModel.isCategoryExpanded) {
                withAnimation {
                    viewModel.isCategoryExpanded.toggle()
                }
            }
            if viewModel.isCategoryExpanded {
                LazyVStack(alignment: .leading) {
                    ForEach(viewModel.visibleCategories) { category in
                        ProductCategoryCell(
                            filter: $viewModel.filter,
                            category: category,
                            categories: categories,
                            currentLevel: 0,
                            maxLevel: 2,
                            onCheckmark: { _ in
                                viewModel.filter = viewModel.filter
                            }
                        )
                        .padding(.vertical, 4)
                        .onAppear {
                            viewModel.loadMoreCategoriesIfNeeded(currentCategory: category)
                        }
                    }
                }
                .padding()
            }
        }
        .foregroundStyle(.black)
    }
    
    private var brandsSection: some View {
        VStack(alignment: .leading) {
            sectionHeader(title: "Brands", isExpanded: viewModel.isBrandExpanded) {
                viewModel.isBrandExpanded.toggle()
            }
            
            if viewModel.isBrandExpanded {
                VStack(alignment: .leading) {
                    ForEach(viewModel.brandsList) {
                        brandRow($0)
                    }
                }
                .padding()
            }
        }
        .foregroundStyle(.black)
    }
    
    private func brandRow(_ brand: Brand) -> some View {
        HStack {
            Button(action: {
                viewModel.toggleBrand(brand)
            }) {
                Image(systemName: viewModel.filter?.brand == brand.id ?
                    "checkmark.square.fill" : "square")
                    .resizable()
                    .frame(width: 20, height: 20)
                    .foregroundColor(ColorConstants.Blue300)
            }
            Text(brand.name)
        }
        .padding(.vertical, 5)
    }
    
    private var actionButtons: some View {
        HStack(spacing: 8.relativeWidth) {
            Button {
                viewModel.clearFilter()
                onApply(viewModel.filter)
            } label: {
                Text("Remove All")
                    .font(.custom("Poppins", size: 16).bold())
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color.white)
                    .foregroundColor(.init(hex: "#363A33"))
                    .overlay(RoundedRectangle(cornerRadius: 10)
                        .stroke(Color(hex: "#E8EBE6"), lineWidth: 1))
            }
           
            Button {
                let filter = viewModel.filter
                onApply(filter)
//                print(viewModel.filter)
            } label: {
                Text("Apply Filter")
                    .font(.custom("Poppins", size: 16).bold())
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(ColorConstants.Blue300)
                    .foregroundColor(.white)
                    .cornerRadius(10)
            }
        }
        .padding(.horizontal)
    }
    
    private var closeButton: some View {
        Button(action: { dismiss() }) {
            Image(systemName: "xmark")
                .resizable()
                .frame(width: 20, height: 20)
                .clipShape(Circle())
                .foregroundColor(.black)
        }
    }
    
    private func sectionHeader(title: String, isExpanded: Bool, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            HStack {
                Text(title)
                    .font(.custom("Nunito", size: 18).bold())
                Spacer()
                Image(systemName: isExpanded ? "minus" : "plus")
            }
            .padding(.vertical, 5)
        }
    }
    
    private func radioButton(isSelected: Bool) -> some View {
        VStack {
            ZStack {}
                .frame(width: getRelativeWidth(13.0),
                       height: getRelativeWidth(13.0),
                       alignment: .center)
                .background(Circle().fill(isSelected ? ColorConstants.Blue300 : .clear))
                .padding(.all, getRelativeWidth(4.0))
        }
        .frame(width: getRelativeWidth(21.0),
               height: getRelativeWidth(21.0), alignment: .center)
        .overlay(Circle()
            .stroke(ColorConstants.Blue300,
                    lineWidth: 1))
        .background(Circle()
            .fill(Color.clear.opacity(0.7)))
        .padding(.leading, getRelativeWidth(9.0))
    }
}

#Preview {
    FilterSortView(
        filter: nil,
        categories: [],
        onApply: { _ in },
        config: .default
    )
}
