//
//  FilterSortViewModel.swift
//  Wasfa
//
//  Created by Apple on 08/02/2025.
//

import Combine
import SwiftUI

// MARK: - View Model

class FilterSortViewModel: SuperViewModel {
    @Published var filter: ProductListingRequest?
    @Published var priceValue: Double = 0
    @Published var isCategoryExpanded: Bool = false
    @Published var isBrandExpanded: Bool = false
    @Published private(set) var visibleCategories: [CategoryModel.Category] = []
    
    @Published private(set) var brandsList: [Brand] = []
    
    private let priceFormatter: PriceFormatterProtocol
    private let filterService: FilterSortServiceProtocol
    private let config: FilterConfiguration
    private var allCategories: [CategoryModel.Category] = []
    private var currentPage = 0
    
    struct FilterConfiguration {
        let minPrice: Double
        let maxPrice: Double
        static let `default` = FilterConfiguration(minPrice: 0, maxPrice: 1000)
    }
    
    init(
        filter: ProductListingRequest?,
        priceFormatter: PriceFormatterProtocol = DefaultPriceFormatter(),
        filterService: FilterSortServiceProtocol = DefaultFilterSortService(),
        config: FilterConfiguration = .default
    ) {
        self.filter = filter
        self.priceFormatter = priceFormatter
        self.filterService = filterService
        self.config = config
        super.init()
        setupPriceDebounce()
        initializeValues()
        fetchBrands()
    }
    
    private func setupPriceDebounce() {
        $priceValue
            .debounce(for: .seconds(Constants.debounceInterval), scheduler: RunLoop.main)
            .sink { [weak self] value in
                guard let self = self else { return }
                self.filterService.updatePrice(value: value, min: self.config.minPrice, max: self.config.maxPrice, request: &self.filter)
            }
            .store(in: &cancellables)
    }
    
    func initializeValues() {
        if let priceValue = filter?.min {
            self.priceValue = priceValue.toDouble
        }
        if let _ = filter?.categoryID {
            isCategoryExpanded = true
        }
    }
    
    func loadMoreCategoriesIfNeeded(currentCategory category: CategoryModel.Category) {
        guard let index = visibleCategories.firstIndex(where: { $0.id == category.id }),
              index == visibleCategories.count - 5 else { return }
        loadNextPage()
    }
    
    func setCategories(_ categories: [CategoryModel.Category]) {
        allCategories = categories
        loadNextPage()
    }
    
    private func loadNextPage() {
        let startIndex = currentPage * Constants.categoriesPerPage
        guard startIndex < allCategories.count else { return }
        
        let endIndex = min(startIndex + Constants.categoriesPerPage, allCategories.count)
        let newCategories = allCategories[startIndex ..< endIndex]
        
        DispatchQueue.main.async {
            self.visibleCategories.append(contentsOf: newCategories)
            self.currentPage += 1
        }
    }
    
    func fetchBrands() {
        // Implement brand fetching logic here
        let parameters: [String: Any] = ["pageNo": 1, "perPage": 20]
        
        onApiCall(api.brandList, parameters: parameters) {
            self.brandsList = $0.data?.brands ?? []
        }
    }
    
    func clearFilter() {
        filterService.clearFilters(request: &filter)
        priceValue = config.minPrice
    }
    
    func updatePrice(_ value: Double) {
        priceValue = value
        // Actual update is handled by the debounced publisher
    }
    
    func toggleCategory(_ category: CategoryModel.Category) {
        filterService.toggleCategory(category: category, request: &filter, allCategories: allCategories)
    }
    
    func toggleBrand(_ brand: Brand) {
        filterService.toggleBrand(brand: brand, request: &filter)
    }
    
    func formatPrice(_ value: Double) -> String {
        priceFormatter.formatPrice(value)
    }
    
    var priceRange: ClosedRange<Double> {
        config.minPrice ... config.maxPrice
    }
}

// MARK: - Protocols and Constants

private enum Constants {
    static let debounceInterval: TimeInterval = 0.3
    static let categoriesPerPage = 20
}

protocol PriceFormatterProtocol {
    func formatPrice(_ value: Double) -> String
}

protocol FilterSortServiceProtocol {
    func clearFilters(request: inout ProductListingRequest?)
    func updatePrice(value: Double, min: Double, max: Double, request: inout ProductListingRequest?)
    func toggleCategory(category: CategoryModel.Category, request: inout ProductListingRequest?, allCategories: [CategoryModel.Category])
    func toggleBrand(brand: Brand, request: inout ProductListingRequest?)
}

// MARK: - Price Formatter Implementation

struct DefaultPriceFormatter: PriceFormatterProtocol {
    private let currencySymbol: String
    private let decimalPlaces: Int
    
    init(currencySymbol: String = "KD", decimalPlaces: Int = 3) {
        self.currencySymbol = currencySymbol
        self.decimalPlaces = decimalPlaces
    }
    
    func formatPrice(_ value: Double) -> String {
        "\(currencySymbol) \(value.toDecimalPlaces(decimalPlaces))"
    }
}

// MARK: - Filter Service Implementation

struct DefaultFilterSortService: FilterSortServiceProtocol {
    func clearFilters(request: inout ProductListingRequest?) {
        request?.sortKey = nil
        request?.min = nil
        request?.categoryID = nil
        request?.subCategoryID = nil
        request?.superSubCategoryID = nil
        request?.brand = nil
    }
    
    func updatePrice(value: Double, min: Double, max: Double, request: inout ProductListingRequest?) {
        request?.min = value.toDecimalPlaces(3)
        request?.max = String(max)
    }
    
    func toggleBrand(brand: Brand, request: inout ProductListingRequest?) {
        request?.brand = brand.id
    }
    
    func toggleCategory(category: CategoryModel.Category, request: inout ProductListingRequest?, allCategories: [CategoryModel.Category]) {
        switch category.level {
        case 0:
            request?.categoryID = category.categoryID
            request?.subCategoryID = nil
            request?.superSubCategoryID = nil
        case 1:
            request?.subCategoryID = category.categoryID
            request?.superSubCategoryID = nil
            if let parentID = category.parentID {
                request?.categoryID = parentID
            }
        case 2:
            request?.superSubCategoryID = category.categoryID
            if let parentID = category.parentID {
                request?.subCategoryID = parentID
                if let topParentID = getTopLevelParentID(for: parentID, allCategories: allCategories) {
                    request?.categoryID = topParentID
                }
            }
        default:
            break
        }
    }

    private func getTopLevelParentID(for categoryID: Int, allCategories: [CategoryModel.Category]) -> Int? {
        var currentID = categoryID
        var visited: Set<Int> = []

        while let parent = allCategories.first(where: { $0.categoryID == currentID })?.parentID,
              !visited.contains(parent)
        {
            visited.insert(parent)
            currentID = parent
        }

        return currentID
    }
}
