//
//  AboutUsViewModel.swift
//  Wasfa
//
//  Created by Apple on 18/02/2025.
//

import SwiftUI

class AboutUsViewModel: SuperViewModel {
    @Published var aboutUs: AboutUsModel?
    @Published var faqModelList: [FAQModel] = []


    func getAboutUs() {
        onApiCall(api.aboutUs, parameters: emptyDictionary) {
            self.aboutUs = $0.data
        }
    }
    
    func getFAQ() {
        onApiCall(api.faq, parameters: emptyDictionary) {_ in 
//            self.faqModelList = $0.data ?? []
            self.faqModelList = [.init(id: 1, quesion: "Test Question 1", answer: "Test Answer 1"), .init(id: 2, quesion: "Test Question 2", answer: "Test Answer 2"),  .init(id: 3, quesion: "Test Question 3", answer: "Test Answer 3")]
        }
    }
    
    
    func getTermsAndConditions() {
        onApiCall(api.termsAndConditions, parameters: emptyDictionary) {
            self.aboutUs = $0.data
        }
    }
    
    func getPrivacyPolicy() {
        onApiCall(api.privacyPolicy, parameters: emptyDictionary) {
            self.aboutUs = $0.data
        }
    }
}
