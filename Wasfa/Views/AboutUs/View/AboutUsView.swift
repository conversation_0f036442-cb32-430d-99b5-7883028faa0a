//
//  AboutUsView.swift
//  Wasfa
//
//  Created by Apple on 18/02/2025.
//

import SwiftUI

struct AboutUsView: View {
    @StateObject private var viewModel = AboutUsViewModel()
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            MainScrollBody(backButtonWithTitle: "About Us", hideBackButton: false) {
                VStack {
                    if let content = viewModel.aboutUs?.content {
                        CustomRichText(html: content, fontFamily: "NunitoLight", fontSrc: "NunitoLight.ttf", sizeAdjust: "100", fontColor: ColorConstants.Black90001, lineHeight: 110.0.relativeFontSize)
                    }
                }
                .padding()
            }
        }.onLoad(perform: viewModel.getAboutUs)
    }
}

struct FAQView: View {
    @StateObject private var viewModel = AboutUsViewModel()
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            MainScrollBody(backButtonWithTitle: "FAQ", hideBackButton: false) {
                VStack {
                    if viewModel.faqModelList.isEmpty {
                        ContentUnavailableView("Your Favorite list is empty!", systemImage: "heart", description: Text("Add items to your wishlist by tapping the heart icon on the product page."))
                    } else {
                        
                    }
                }
                .padding()
            }
        }.onLoad(perform: viewModel.getFAQ)
    }
}

struct TermsAndConditionsView: View {
    @StateObject private var viewModel = AboutUsViewModel()
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            MainScrollBody(backButtonWithTitle: "Terms And Conditions", hideBackButton: false) {
                VStack {
                    if let content = viewModel.aboutUs?.content {
                        CustomRichText(html: content, fontFamily: "NunitoLight", fontSrc: "NunitoLight.ttf", sizeAdjust: "100", fontColor: ColorConstants.Black90001, lineHeight: 110.0.relativeFontSize)
                    }
                }
                .padding()
            }
        }.onLoad(perform: viewModel.getTermsAndConditions)
    }
}

struct PrivacyPolicyView: View {
    @StateObject private var viewModel = AboutUsViewModel()
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            MainScrollBody(backButtonWithTitle: "Privacy Policy", hideBackButton: false) {
                VStack {
                    if let content = viewModel.aboutUs?.content {
                        CustomRichText(html: content, fontFamily: "NunitoLight", fontSrc: "NunitoLight.ttf", sizeAdjust: "100", fontColor: ColorConstants.Black90001, lineHeight: 110.0.relativeFontSize)
                    }
                }
                .padding()
            }
        }.onLoad(perform: viewModel.getPrivacyPolicy)
    }
}
