//
//  WishlistViewModel.swift
//  Wasfa
//
//  Created by Apple on 16/02/2025.
//

import SwiftUI

class WishlistViewModel: SuperViewModel {
    @Published var wishlist: [WishlistModel] = []

    override init() {
        super.init()
    }

    func getWishlist() {
        onApiCall(api.wishlist, parameters: emptyDictionary) { response in
            self.wishlist = response.data ?? []
            self.appState?.updateWishlistQuantity(self.wishlist.count)
        }
    }

    func onRemoveWishlistItem(_ productID: Int) {
        let parameters: [String: Any] = ["productId": productID]
        onApiCall(api.removeWishlist, parameters: parameters) {
            if $0.success { self.getWishlist() }
        }
    }

    func onAddToCart(_ productID: Int, quantity: Int) {
        addToCart(productID: productID, quantity: quantity) {
            self.appState?.updateCartQuantity($0?.cartCount)
            self.onRemoveWishlistItem(productID)
        } onFailure: { _ in
        }
    }
    
    
    func onSelectProduct(_ productID: Int) {
        routerManager?.push(to: .productDetails(id: productID), appState: appState)
    }

    func addToCart(productID: Int, quantity: Int = 1, onComplete: @escaping (CartAddModel?) -> Void, onFailure: @escaping (String) -> Void) {
        let parameters: [String: Any] = ["productId": productID, "quantity": quantity]
        onApiCall(api.addToCart, parameters: parameters) {
            onComplete($0.data)
        } onFailure: { onFailure($0) }
    }

    func onUpdateCartQuantity(_ quantity: Int, _ cartID: Int) {
//        let parameters: [String: Any] = ["quantity": quantity, "id": cartID]
//        onApiCall(api.changeQuantity, parameters: parameters) {
//            if $0.success { self.getWishlist() }
//        }
    }
}
