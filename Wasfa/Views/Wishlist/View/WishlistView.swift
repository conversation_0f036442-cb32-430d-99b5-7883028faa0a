//
//  WishlistView.swift
//  Wasfa
//
//  Created by Apple on 22/09/2024.
//

import SwiftUI

struct WishlistView: View {
    @StateObject private var viewModel = WishlistViewModel()
    private let columns: [GridItem] = [GridItem(), GridItem()]
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            MainScrollBody(backButtonWithTitle: "Favorites", hideBackButton: true) {
                LazyVStack {
                    if viewModel.wishlist.isEmpty {
                        ContentUnavailableView("Your Favorite list is empty!", systemImage: "heart", description: Text("Add items to your wishlist by tapping the heart icon on the product page."))
                        
                    } else {
                        LazyVGrid(columns: self.columns, spacing: 15.relativeHeight) {
                            ForEach(self.viewModel.wishlist) {
                                WishlistCellView(model: $0, onSelect: viewModel.onSelectProduct, onAddToCart: viewModel.onAddToCart, onRemove: viewModel.onRemoveWishlistItem)
                            }
                        }
                    }
                }
                .padding()
            }
            .onAppear(perform: viewModel.getWishlist)
        }
        .injectEnvironmentValues(viewModel)
    }
}

#Preview {
    NavigationStack {
        WishlistView()
    }
}
