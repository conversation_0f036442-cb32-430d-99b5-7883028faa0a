//
//  WishlistCellView.swift
//  Wasfa
//
//  Created by Apple on 16/02/2025.
//

import SwiftUI

//struct WishlistCellView: View {
//    let model: WishlistModel
//    let onAddToCart: (_ productID: Int, _ quantity: Int) -> Void
//    let onRemove: (_ cartID: Int) -> Void
//    @State private var quantity: Int = 1
//    private let maxQuantity: Int = 10
//
//    var increment: Void {
//        if maxQuantity > quantity {
//            quantity += 1
//        }
//    }
//
//    var decrement: Void {
//        if quantity > 1 {
//            quantity -= 1
//        }
//    }
//
//    var body: some View {
//        HStack(spacing: 16) {
//            NetworkImageView(path: model.thumbnailImage)
//                .frame(width: getRelativeWidth(86.0), height: getRelativeHeight(105.0),
//                       alignment: .leading)
//                .scaledToFit()
//                .background(RoundedCorners(topLeft: 12.0, topRight: 12.0, bottomLeft: 12.0,
//                                           bottomRight: 12.0).fill(.white))
//                .frame(width: getRelativeWidth(86.0), height: getRelativeHeight(105.0),
//                       alignment: .leading)
//
//                .clipShape(RoundedCorners(topLeft: 12.0, topRight: 12.0, bottomLeft: 12.0,
//                                          bottomRight: 12.0))
//                .padding([.vertical, .leading], getRelativeHeight(8.0))
//
//            VStack(alignment: .leading) {
//                Text(model.name)
//                    .font(FontScheme.kRobotoRomanRegular(size: getRelativeHeight(13.0)))
//                    .fontWeight(.regular)
//                    .foregroundColor(ColorConstants.Black90001)
//                    .minimumScaleFactor(0.5)
//                    .multilineTextAlignment(.leading)
//                    .frame(width: getRelativeWidth(203.0), height: getRelativeHeight(54.0),
//                           alignment: .leading)
//
//                HStack {
//                    Text(model.unitPrice)
//                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
//                        .fontWeight(.bold)
//                        .foregroundColor(ColorConstants.Black90001)
//                        .minimumScaleFactor(0.5)
//                        .multilineTextAlignment(.leading)
//                        .frame(height: getRelativeHeight(18.0),
//                               alignment: .leading)
//                        .frame(maxWidth: .infinity, alignment: .leading)
//
//                    Spacer()
//
//                    HStack(spacing: 16) {
//                        Button(action: {
//                            decrement
//                        }) {
//                            Image("img_add_round")
//                                .resizable()
//                                .frame(width: getRelativeWidth(16.0), height: getRelativeWidth(18.0),
//                                       alignment: .leading)
//                                .scaledToFit()
//                        }
//                        Text("\(quantity)")
//                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
//                            .fontWeight(.bold)
//                            .lineLimit(1)
//                            .foregroundColor(ColorConstants.Black90001)
//                            .multilineTextAlignment(.leading)
//                            .frame(width: 20)
//                            .contentTransition(.numericText())
//                            .animation(.bouncy, value: quantity)
//                        Button {
//                            increment
//                        } label: {
//                            Image("img_add_round_gray_700_02")
//                                .resizable()
//                                .frame(width: getRelativeWidth(16.0), height: getRelativeWidth(18.0),
//                                       alignment: .leading)
//                                .scaledToFit()
//                        }
//                    }
//                    .padding(.horizontal, 8)
//                    .padding(.vertical, 6)
//                    .background(RoundedCorners(topLeft: 12.0, topRight: 12.0, bottomLeft: 12.0,
//                                               bottomRight: 12.0)
//                            .fill(ColorConstants.Gray200))
//                }
//
//                VStack(alignment: .leading) {
//                    HStack {
//                        Button {
//                            onAddToCart(model.id, quantity)
//                        } label: {
//                            HStack {
//                                Image(systemName: "cart.badge.plus")
//
//                                Text("Add to Cart")
//                            }
//                            .font(FontScheme.kRobotoRomanRegular(size: getRelativeHeight(12.0)))
//                            .fontWeight(.regular)
//                            .multilineTextAlignment(.leading)
//                            .padding(.horizontal, 8)
//                            .padding(.vertical, 6)
//                            .overlay(RoundedRectangle(cornerRadius: 6).stroke(ColorConstants.Blue300, lineWidth: 1))
//                        }
//                        .foregroundColor(ColorConstants.Blue300)
//
//                        Spacer()
//                        Button(action: { onRemove(model.id) }, label: {
//                            Image("trash.bin")
//                        })
//                        .frame(width: getRelativeWidth(22.0), height: getRelativeWidth(24.0),
//                               alignment: .center)
//                    }
//                }
//
//                .padding([.top, .trailing], getRelativeHeight(8.0))
//            }
//        }
//        .padding(.horizontal, 8)
//        .padding([.vertical, .trailing], 8)
//        .background(RoundedCorners(topLeft: 22.0, topRight: 22.0, bottomLeft: 22.0,
//                                   bottomRight: 22.0)
//                .fill(ColorConstants.WhiteA700).shadow(color: ColorConstants.Black9001e, radius: 5, x: 0, y: 0))
//        .padding(.horizontal, 8)
//        .onAppear {
////            quantity = model.cartQty
//        }
//    }
//}

struct WishlistCellView: View {
    let model: WishlistModel
    let onSelect: (Int) -> Void
    let onAddToCart: (_ productID: Int, _ quantity: Int) -> Void
    let onRemove: (_ productID: Int) -> Void
   
    @State private var quantity: Int = 1
    private let maxQuantity: Int = 10
    
    var increment: Void {
        if maxQuantity > quantity {
            quantity += 1
        }
    }

    var decrement: Void {
        if quantity > 1 {
            quantity -= 1
        }
    }
    
    var isInCart: Bool {
        quantity > 0
    }
    
    var body: some View {
        ZStack(alignment: .top) {
            VStack(spacing: 0) {
                VStack(alignment: .leading, spacing: 0) {}
//                .frame(width: getRelativeWidth(137.0), height: getRelativeHeight(52.0),
//                       alignment: .center)
                    .padding(.bottom, getRelativeHeight(57.0))
                    .padding(.horizontal, getRelativeWidth(6.0))
                
//                .frame(width: getRelativeWidth(149.0), height: getRelativeHeight(114.0),
//                       alignment: .center)
                    .background(RoundedCorners(topLeft: 11.0, topRight: 11.0).fill(ColorConstants.Gray50))
                    .padding([.top, .leading, .trailing], 2)
                
                HStack {
                    if model.hasDiscount {
                        Text("\(model.discount) OFF")
                            .font(FontScheme.kNunitoLight(size: getRelativeHeight(9.0)))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.Teal400)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, getRelativeWidth(8))
                            .padding(.vertical, 4)
                            .background(Color.clear)
                            .overlay(
                                Capsule()
                                    .stroke(ColorConstants.Teal400, lineWidth: 1)
                            )
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }
                    Spacer()
                    if model.currentStock < 1 {
                        Text(StringConstants.kLblSoldOut)
                            .font(FontScheme.kNunitoLight(size: getRelativeHeight(9.0)))
                            .fontWeight(.light)
                            .foregroundColor(ColorConstants.WhiteA700)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, getRelativeWidth(8))
                            .padding(.vertical, 4)
                            .background(Capsule()
                                .fill(ColorConstants.Red600))
                    }
                }
                
                Text(model.name)
                    .font(FontScheme.kNunitoLight(size: getRelativeHeight(11.0)))
                    .fontWeight(.light)
                    .foregroundColor(ColorConstants.Black90001)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.center)
                    .frame(width: getRelativeWidth(136.0), height: getRelativeHeight(45.0),
                           alignment: .center)
                    .padding(.top, getRelativeHeight(4.0))
                HStack {
                    Text(model.unitPrice)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Black90001)
                        .multilineTextAlignment(.leading)
                        
                    if let strokedPrice = model.strokedPrice {
                        Text(strokedPrice)
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
                            .strikethrough()
                            .fontWeight(.regular)
                            .foregroundColor(ColorConstants.Gray500)
                            //                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                    }
                }
                .padding(.top, getRelativeHeight(6.0))
                
                Button(action: {
                    onAddToCart(model.id, quantity)
                }, label: {
                    Image(systemName: "plus")
                        .fontWeight(.bold)
                        .foregroundStyle(ColorConstants.WhiteA700)
                        .frame(width: 33, height: 33)
                        .background(.primary)
                        .clipShape(.rect(cornerRadius: 8))
                        .scaleEffect(0.8, anchor: .trailing)
                })
                .frame(maxWidth: .infinity, alignment: .trailing)
                    .disableWithOpacity(model.currentStock < 1)
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .frame(width: getRelativeWidth(161.0), alignment: .center)
            .background(RoundedCorners(topLeft: 11.0, topRight: 11.0, bottomLeft: 11.0,
                                       bottomRight: 11.0)
                    .fill(ColorConstants.ProductBG))
            .padding(.top, getRelativeHeight(57.0))
            ZStack(alignment: .trailing) {
                NetworkImageView(path: model.thumbnailImage)
                    .frame(width: getRelativeWidth(143.0), height: getRelativeHeight(114.0),
                           alignment: .center)
                    .scaledToFit()
                    .clipShape(UnevenRoundedRectangle(topLeadingRadius: 11.0, bottomLeadingRadius: 0, bottomTrailingRadius: 0, topTrailingRadius: 11, style: .continuous))
                    .padding(.horizontal, getRelativeWidth(3.0))
                    .blendMode(.multiply)
               
                Button(action: {
                    onRemove(model.id)
                    
                }, label: {
                    Image("wishlist.fill")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: getRelativeWidth(20), height: getRelativeWidth(20),
                               alignment: .center)
                })
                .padding(4)
                .padding(.top, 32)
            }
        }
        .onTapGesture(perform: { onSelect(model.id) })
    }
}
