import SwiftUI

struct HomeView: View {
    @StateObject var viewModel = HomeViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            MainScrollBody(isHome: true) {
                VStack(spacing: 16) {
                    SearchBar
                    PagerSection
                    CategorySection
                    BestSellersSection
                    SecondaryBanners
                    SpecialOffersSection
                    NewArrivalsSection
                    BrandsSection
                }
                .background(ColorConstants.WhiteA700)
                .padding(.top, getRelativeHeight(4.0))
                .padding(.bottom, getRelativeHeight(10.0))
                .ignoresSafeArea()
//                .toolbarBackground(.hidden, for: .navigationBar)
            }
            .safeAreaInset(edge: .bottom) {
                Button {openWhatsApp(phoneNumber: "96522287833")} label: {
                    Image(.whatsapp)
                        .resizable()
                        .frame(width: 50, height: 50)
                        .clipShape(Circle())
                        .shadow(radius: 5)
                }
                .padding()
                .frame(maxWidth: .infinity, alignment: .trailing)
            }
//            .safeAreaInset(edge: .top) {
//                SearchBar
//            }
            
        }
        .onAppear(perform:{ viewModel.getHomePageData()})
        .injectEnvironmentValues(viewModel)
        
        .onReceive(NotificationCenter.default.publisher(for: .notificationAppeared)) { notification in
            if let userInfo = notification.userInfo {
                // Do something when notification is received (foreground or background)
                viewModel.getHomePageData(withLoadingIndicator: false)
                
            }
        }

        .onReceive(NotificationCenter.default.publisher(for: .notificationTapped)) { notification in
            if let userInfo = notification.userInfo {
                // Do something when user taps notification
            }
        }
    }
    
    func openWhatsApp(phoneNumber: String) {
        let formattedNumber = phoneNumber.replacingOccurrences(of: "+", with: "").replacingOccurrences(of: " ", with: "")
        let urlString = "https://wa.me/\(formattedNumber)"
        if let url = URL(string: urlString), UIApplication.shared.canOpenURL(url) {
            UIApplication.shared.open(url)
        } else {
            print("WhatsApp is not installed or the URL is invalid")
        }
    }
}

extension HomeView {
    // Search Bar
    var SearchBar: some View {
        VStack {
            HStack(spacing: 12) {
                Image("img_search_light")
                    .frame(width: 23, height: 24)
                
                TextField(StringConstants.kMsgWhatAreYouLooking, text: $viewModel.search)
                    .font(FontScheme.kNunitoLight(size: getRelativeHeight(12.0)))
                    .foregroundColor(ColorConstants.Gray50004)
                    .disabled(true)
            }
            .padding(.horizontal)
            .frame(width: getRelativeWidth(315.0), height: getRelativeHeight(50.0), alignment: .center)
            .overlay(Capsule().stroke(ColorConstants.Blue6009e, lineWidth: 1))
            .background(
                Capsule().fill(ColorConstants.Cyan4000a)
            )
            .onTapGesture(perform: viewModel.onSearch)
            .padding(.horizontal, getRelativeWidth(38.0))
        }
       
        .padding(.vertical, 6)
        .padding(.top)
        .background(ColorConstants.WhiteA700)
        .shadow(color: .black.opacity(0.08), radius: 5.3, x: 0, y: 4)
    }
    
    // Pager Section
    var PagerSection: some View {
        VStack(alignment: .leading, spacing: 0) {
            if let homeModel = viewModel.homeModel {
                PageView(homeModel.bannerList, selection: $viewModel.currentBannerPage.animation()) { banner in
                    ZStack(alignment: .bottomLeading) {
                        NetworkImageView(path: banner.image, contentMode: .fill)
                            .frame(width: getRelativeWidth(369.0), height: getRelativeHeight(181.0))
                            .clipped()
                            .background(RoundedCorners(topLeft: 12.0, topRight: 12.0, bottomLeft: 12.0, bottomRight: 12.0).fill(.clear))
                        VStack(alignment: .leading, spacing: 8) {
                            Text(banner.text ?? "")
                                .font(FontScheme.kNunitoMedium(size: getRelativeHeight(12.0)))
                                .fontWeight(.medium)
                                .foregroundColor(ColorConstants.WhiteA700)
                                .padding(.vertical, 6)
                                .background(Color(red: 0.71, green: 0.85, blue: 0.45))
        
                            Text(banner.description ?? "")
                                .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(14.0)).weight(.heavy))
                                .foregroundColor(ColorConstants.WhiteA700)
                                .padding(.vertical, 4)
                                .background(Color(red: 0.13, green: 0.37, blue: 0.25))
                            if let linkTo = banner.linkTo, let categoryID = Int(linkTo) {
                                Button(action:{ viewModel.onBannerSelect(categoryID: categoryID)}) {
                                    Text(StringConstants.kLblShopNow)
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                        .padding(.horizontal, getRelativeWidth(10.0))
                                        .padding(.vertical, getRelativeHeight(5.0))
                                        .foregroundColor(ColorConstants.Blue300)
                                        .frame(width: getRelativeWidth(97.0), height: getRelativeHeight(30.0), alignment: .center)
                                        .background(Capsule().fill(ColorConstants.WhiteA700)
    //                                        .shadow(color: ColorConstants.Black9000f, radius: 2, x: 0, y: 2)
                                        )
                                    // add a light drop shadow
                                        
                                }
                                .padding(.top, getRelativeHeight(15.0))
                            }
                        }
                        .padding()
                      
                        //                    .padding(.trailing, getRelativeWidth(119.0))
                    }
                    .background(RoundedCorners().fill(ColorConstants.BlueGray10002))
                    .cornerRadius(12)
                    .shadow(color: ColorConstants.Black9002b, radius: 4, x: 0, y: 2)
                }
                PageIndicator(numPages: homeModel.bannerList.count, currentPage: $viewModel.currentBannerPage, selectedColor: ColorConstants.Blue300, unSelectedColor: ColorConstants.Pink30054, spacing: 8.0)
                    .frame(maxWidth: .infinity)
            }
        }
        .animation(.bouncy, value: viewModel.currentBannerPage)
        .frame(height: getRelativeHeight(206.0))
    }
    
    // Category Section
    var CategorySection: some View {
        VStack(spacing: 16) {
            if let homeModel = viewModel.homeModel {
                SectionHeader(title: StringConstants.kLblCategories, seeAllAction: viewModel.onCategorySeeAll)
                    .padding(.horizontal, getRelativeWidth(12.0))
                
                HorizontalScrollView(data: homeModel.topCategories) { model in
                    CategoryItemCell(model: model, action: viewModel.onCategorySelect)
                }
            }
        }
        .padding(.top, getRelativeHeight(16))
        .padding(.leading, getRelativeWidth(12.0))
    }
    
    // Special Offers Section
    var SpecialOffersSection: some View {
        VStack {
            if let homeModel = viewModel.homeModel {
                SectionHeader(title: StringConstants.kLblSpecialOffers, seeAllAction: { viewModel.onSeeAll(type: .offers) })
                    .padding(.horizontal, getRelativeWidth(12.0))
                HorizontalScrollView(data: homeModel.offerProducts) { model in
                    ProductCardCell(model: model, onSelect: viewModel.onSelectProduct, onAddToCart: viewModel.onAddToCart, onAddToWishlist: viewModel.onAddToWishlist)
                }
            }
        }
        .padding(.top, getRelativeHeight(16))
        .padding(.leading, getRelativeWidth(11.0))
    }

    // Best Sellers Section
    var BestSellersSection: some View {
        VStack(spacing: 16) {
            if let homeModel = viewModel.homeModel {
                SectionHeader(title: StringConstants.kLblBestSellers, seeAllAction: { viewModel.onSeeAll(type: .bestSellers) })
                    .padding(.horizontal, getRelativeWidth(12.0))
                HorizontalScrollView(data: homeModel.topSellingProducts, spacing: 12) { model in
                    ProductCardCell(model: model, onSelect: viewModel.onSelectProduct, onAddToCart: viewModel.onAddToCart, onAddToWishlist: viewModel.onAddToWishlist)
                }
            }
        }
        .padding(.top, getRelativeHeight(16))
        .padding(.leading, getRelativeWidth(12.0))
    }
    
    // Secondary Banners Section
    var SecondaryBanners: some View {
        HStack(spacing: 0) {
            if let homeModel = viewModel.homeModel {
                HorizontalScrollView(data: homeModel.offerBannerList, spacing: 15.0.relativeWidth) { model in
                    StackCell(model: model, onShopNow: viewModel.onBannerSelect)
                }
            }
        }
        
        .padding(.top, getRelativeHeight(38.0))
        .padding(.horizontal, getRelativeWidth(8.0))
    }

    // New Arrivals Section
    var NewArrivalsSection: some View {
        VStack {
            if let homeModel = viewModel.homeModel {
                SectionHeader(title: StringConstants.kLblNewArrivals, seeAllAction: { viewModel.onSeeAll(type: .latest) })
                    .padding(.horizontal, getRelativeWidth(12.0))
                HorizontalScrollView(data: homeModel.latestProducts) { model in
                    ProductCardCell(model: model, onSelect: viewModel.onSelectProduct, onAddToCart: viewModel.onAddToCart, onAddToWishlist: viewModel.onAddToWishlist)
                }
            }
        }
        .padding(.top, getRelativeHeight(16))
        .padding(.leading, getRelativeWidth(11.0))
    }

    // Brands Section
    var BrandsSection: some View {
        VStack {
            if let homeModel = viewModel.homeModel {
                SectionHeader(title: StringConstants.kLblBrands, seeAllAction: viewModel.onSeeAllBrands)
                    .padding(.horizontal, getRelativeWidth(12.0))
                HorizontalScrollView(data: homeModel.brandsList, spacing: 12.relativeWidth) { model in
                    ProductBrandCell(model: model, onSelect: viewModel.onBrandSelect)
                }
            }
        }
        .padding(.vertical, getRelativeHeight(16))
        .padding(.leading, getRelativeWidth(11.0))
    }
}

// MARK: - Reusable Components

struct SectionHeader: View {
    let title: LocalizedStringKey
    let seeAllAction: () -> Void
    
    var body: some View {
        HStack {
            Text(title)
                .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(16.0)))
                .fontWeight(.heavy)
                .foregroundColor(ColorConstants.Black90001)
            Spacer()
            Button(action: seeAllAction) {
                Text(StringConstants.kLblSeeAll)
                    .font(FontScheme.kNunitoLight(size: getRelativeHeight(12.0)))
                    .foregroundColor(ColorConstants.Pink300C1)
            }
            .padding(.trailing, 10)
        }
        .frame(width: getRelativeWidth(368.0))
        .padding(.horizontal, getRelativeWidth(12.0))
    }
}

struct HorizontalScrollView<Data: Identifiable, Content: View>: View {
    let data: [Data]
    var spacing: CGFloat?
    let content: (Data) -> Content
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            LazyHStack(alignment: .top, spacing: spacing ?? 8) {
                ForEach(data) {
                    content($0)
                }
            }
        }
        .safeAreaPadding(.horizontal)
        .padding(.horizontal, getRelativeWidth(12.0))
        .scrollClipDisabled()
    }
}

#Preview {
    NavigationStack {
        HomeView().attachAllEnvironmentObjects()
    }
}

struct HorizontalScrollDummyView<Content: View>: View {
    let itemCount: Int
    var spacing: CGFloat?
    let content: () -> Content
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            LazyHStack(spacing: spacing ?? 8) {
                ForEach(0 ..< itemCount, id: \.self) { _ in
                    content()
                }
            }
        }
        .safeAreaPadding(.horizontal)
        .padding(.horizontal, getRelativeWidth(12.0))
        .scrollClipDisabled()
    }
}

struct PageView<Item: Identifiable, Content: View>: View {
    let items: [Item]
    let content: (Item) -> Content

    @Binding var selection: Int?
    @State private var position: Int? = 0
    init(_ items: [Item], selection: Binding<Int?>, @ViewBuilder content: @escaping (Item) -> Content) {
        self.items = items
        self.content = content
        self._selection = selection
    }

    var body: some View {
        GeometryReader { geometry in
         
            ScrollView(.horizontal) {
                LazyHStack(spacing: 0) {
                    ForEach(Array(items.enumerated()), id: \.element.id) { index, item in
                        content(item)
                            .frame(width: geometry.size.width, height: geometry.size.height)
                            .id(index) // Assign an ID for each item to target in scroll
                    }
                }
                .scrollTargetLayout()
            }
            .scrollPosition(id: $selection)
            .scrollTargetBehavior(.viewAligned)
            .scrollIndicators(.hidden)
        }
    }
}

extension View {
    func sync<T: Equatable>(_ published: Binding<T>, with binding: Binding<T>) -> some View {
        onChange(of: published.wrappedValue) { _, published in
            binding.wrappedValue = published
        }
        .onChange(of: binding.wrappedValue) { _, binding in
            published.wrappedValue = binding
        }
    }
}
