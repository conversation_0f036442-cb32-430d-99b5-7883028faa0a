import Foundation
import SwiftUI

class HomeViewModel: SuperViewModel {
    struct TutorialItem: Identifiable {
        let id: UUID = .init()
        let index: Int
    }
    
    @Published var homeModel: HomePageModel?

    @Published var search: String = ""
    @Published var currentBannerPage: Int? = 0
    
    override init() {
        super.init()
//        fetchUserData()
    }
    
//    func fetchUserData() {
//        onApiCall(api.profileDetails, parameters: emptyDictionary, withStateChange: false) {
//            AppState.user = $0.data?.first
//        }
//    }
        
    func getHomePageData(withLoadingIndicator: Bool = true) {
        if withLoadingIndicator {  homeModel = nil }
        onApiCall(api.homepage, parameters: emptyDictionary, withLoadingIndicator: withLoadingIndicator) {
            self.homeModel = $0.data
            guard let appState = self.appState else { return }
            appState.updateCartQuantity($0.data?.cartCount)
            appState.updateWishlistQuantity($0.data?.wishListCount)
            appState.updateNotificationCount($0.data?.notificationCount)
        }
    }
    
    func onSearch() {
        routerManager?.push(to: .productListing(reuestModel: .init(title: ""), isSearchFocused: true), appState: appState)
    }
    
    func onBrandSelect(model: Brand) {
        routerManager?.push(to: .productListing(reuestModel: .init(brand: model.id, title: model.name.capitalized), isSearchFocused: false), appState: appState)
    }
    
    func onBannerSelect(categoryID: Int) {
        routerManager?.push(to: .productListing(reuestModel: .init(categoryID: categoryID, title: ""), isSearchFocused: false), appState: appState)
    }
    
    func onCategorySeeAll() {
        appState?.updateSelectedTab(.category)
    }
    
    func onSeeAll(type: ViewAllType) {
        routerManager?.push(to: .productListing(reuestModel: .init(viewAll: type, title: type.title)), appState: appState)
    }
    
    func onSeeAllBrands() {
        routerManager?.push(to: .brandListing, appState: appState)
    }
    
    func onCategorySelect(model: HomePageModel.TopCategory) {
//        routerManager?.push(to: .productListing(reuestModel: .init(category: model.id, title: model.name)), appState: appState)
        appState?.updateSelectedTab(.category)
        appState?.updateSelectedCategoryID(model.id)
    }
    
    func onSelectProduct(product: ProductModel) {
        routerManager?.push(to: .productDetails(id: product.id), appState: appState)
    }
    
    func onAddToCart(product: Binding<ProductModel>, quantity: Int, onSuccess: @escaping (Bool) -> Void) {
        addToCart(productID: product.wrappedValue.id, quantity: quantity) {
            product.wrappedValue.cartQty = $0?.productQuantity ?? 0
            self.appState?.updateCartQuantity($0?.cartCount)
            onSuccess(true)
        } onFailure: { _ in
            onSuccess(false)
        }
    }
    
    func onAddToWishlist(product: ProductModel, isAdd: Bool) {
        addToWishlist(productID: product.id, isAdd: isAdd) { [weak self] model in
            self?.appState?.updateWishlistQuantity(model?.wishListCount)
        }
    }
    
    func addToWishlist(productID: Int, isAdd: Bool, onComplete: @escaping (WishlistAddModel?) -> Void) {
        let parameters: [String: Any] = ["productId": productID]
        
        if isAdd {
            onApiCall(api.addWishlist, parameters: parameters, withLoadingIndicator: false) {
                if $0.success {
                    onComplete($0.data)
                }
            }
        } else {
            onApiCall(api.removeWishlist, parameters: parameters, withLoadingIndicator: false) {
                if $0.success {
                    onComplete($0.data)
                }
            }
        }
    }
    
    func addToCart(productID: Int, quantity: Int = 1, onComplete: @escaping (CartAddModel?) -> Void, onFailure: @escaping (String) -> Void) {
        let parameters: [String: Any] = ["productId": productID, "quantity": quantity]
        onApiCall(api.addToCart, parameters: parameters, withLoadingIndicator: false) {
            onComplete($0.data)
        } onFailure: { onFailure($0) }
    }
}
