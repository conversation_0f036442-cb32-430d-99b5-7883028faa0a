import SwiftUI

struct CategoryItemCell: View {
    let model: HomePageModel.TopCategory
    let action: (HomePageModel.TopCategory) -> Void
    var body: some View {
        Button(action:{ action(model) }) {
            VStack(alignment: .center, spacing: 12) {
                NetworkImageView(path: model.icon)
                    .padding(8)
                    .frame(width: getRelativeWidth(57.0), height: getRelativeWidth(59.0),
                           alignment: .center)
                    .background(Circle()
                            .fill(ColorConstants.BlueLight))
                Text(model.name)
                    .font(FontScheme.kNunitoLight(size: getRelativeHeight(12.0)))
                    .fontWeight(.light)
                    .foregroundColor(ColorConstants.Black90001)
                    .lineLimit(2, reservesSpace: true)
                    .multilineTextAlignment(.center)
                    .frame(height: getRelativeHeight(17.0),
                           alignment: .center)
                    .padding(.top, getRelativeHeight(4.0))
                    .padding(.leading, getRelativeWidth(5.0))
            }
            .frame(width: getRelativeWidth(65.0), alignment: .center)
        }
    }
}

// struct CategoryitemCell_Previews: PreviewProvider {
//    static var previews: some View {
//        CategoryItemCell()
//    }
// }
