import SwiftUI

struct ProductBrandCell: View {
    let model: Brand
    var isBigger: Bool = false
    let onSelect: (Brand) -> Void
    var body: some View {
        Button(action: { onSelect(model) }) {
            NetworkImageView(path: self.model.logo)
                .padding(.horizontal, 4)
                .frame(width: isBigger ? (93.relativeWidth * 1.8) : 93.relativeWidth, height: isBigger ? (77.5.relativeHeight * 1.8) : 77.relativeHeight)
                .background(.white)
                .cornerRadius(11)
                .shadow(color: .black.opacity(0.14), radius: 2.9, x: 0, y: 0)
        }
    }
}

/* struct ProductitemoneCell_Previews: PreviewProvider {

 static var previews: some View {
 			ProductitemoneCell()
 }
 } */
