import SwiftUI

struct StackCell: View {
    // MARK: - Properties
    let model: HomePageModel.BannerList
    let onShopNow: (Int) -> Void
    
    // MARK: - Body
    var body: some View {
        NetworkImageView(path: model.image, contentMode: .fill)
            .aspectRatio(contentMode: .fill)
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(ColorConstants.Teal400)
            )
            .frame(width: getRelativeWidth(263.0), height: getRelativeHeight(169.0))
            .clipShape(RoundedRectangle(cornerRadius: 12))
            .shadow(color: ColorConstants.Black9003d, radius: 4)
            .overlay(alignment: .bottomLeading) {
                shopNowButton
            }
    }
    
    // MARK: - Components
    @ViewBuilder
    private var shopNowButton: some View {
        if let linkTo = model.linkTo, let categoryID = Int(linkTo) {
            Button {
                onShopNow(categoryID)
            } label: {
                Text(StringConstants.kLblShopNow)
                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                    .fontWeight(.black)
                    .padding(.horizontal, getRelativeWidth(10.0))
                    .padding(.vertical, getRelativeHeight(5.0))
                    .foregroundColor(ColorConstants.WhiteA700)
                    .frame(width: getRelativeWidth(95.0), height: getRelativeHeight(30.0))
                    .overlay(
                        Capsule()
                            .stroke(ColorConstants.WhiteA700, lineWidth: 2)
                    )
            }
            .padding()
        }
    }
}

// Uncomment to enable previews with appropriate mock data
// struct StackCell_Previews: PreviewProvider {
//     static var previews: some View {
//         StackCell(
//             model: HomePageModel.BannerList(image: "sample_url", linkTo: "1"),
//             onShopNow: { _ in }
//         )
//         .previewLayout(.sizeThatFits)
//     }
// }Z
