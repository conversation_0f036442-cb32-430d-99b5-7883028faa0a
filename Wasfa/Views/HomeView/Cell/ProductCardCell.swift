import SwiftUI

struct ProductCardCell: View {
    @State var model: ProductModel
    let onSelect: (ProductModel) -> Void
    let onAddToCart: (Binding<ProductModel>, Int, @escaping (Bool) -> Void) -> Void
    let onAddToWishlist: (ProductModel, Bool) -> Void
    @Environment(\.routerManager) private var routerManager: RouterManager
    @EnvironmentObject private var appState: AppState
    
    @State private var isFavorite: Bool = false
    
    @State private var quantity: Int = 0
    
    @State private var cartQty: Int = 0
    
    /// Updates the quantity with debounce logic
    func updateQuantity() {
        DispatchQueue.debounce(delay: 0.5, key: "\(model.id)_quantity") {
            onAddToCart($model, quantity - model.cartQty) {
                if $0 { cartQty = quantity }
                else { quantity = cartQty }
            }
        }
    }
    
    var increment: Void {
        if model.currentStock > quantity {
            quantity += 1
            updateQuantity()
        }
    }

    var decrement: Void {
        if quantity > 1 {
            quantity -= 1
            updateQuantity()
        }
    }
    
    var isInCart: Bool {
        quantity > 0
    }
    
    var body: some View {
        ZStack(alignment: .top) {
            VStack(spacing: 0) {
                VStack(alignment: .leading, spacing: 0) {
                   
                }
//                .frame(width: getRelativeWidth(137.0), height: getRelativeHeight(52.0),
//                       alignment: .center)
                .padding(.bottom, getRelativeHeight(57.0))
                .padding(.horizontal, getRelativeWidth(6.0))
                
//                .frame(width: getRelativeWidth(149.0), height: getRelativeHeight(114.0),
//                       alignment: .center)
                .background(RoundedCorners(topLeft: 11.0, topRight: 11.0).fill(ColorConstants.Gray50))
                .padding([.top, .leading, .trailing], 2)
                
                
                

                HStack{
                    if model.hasDiscount {
                        Text("\(model.discount) OFF")
                            .font(FontScheme.kNunitoLight(size: getRelativeHeight(9.0)))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.Teal400)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, getRelativeWidth(8))
                            .padding(.vertical, 4)
                            .background(Color.clear)
                            .overlay(
                                Capsule()
                                    .stroke(ColorConstants.Teal400, lineWidth: 1)
                            )
//                            .frame(maxWidth: .infinity, alignment: .leading)
                    }
                    
                    Spacer()
                    
                    if model.currentStock < 1 {
                        Text(StringConstants.kLblSoldOut)
                            .font(FontScheme.kNunitoLight(size: getRelativeHeight(9.0)))
                            .fontWeight(.light)
                            .foregroundColor(ColorConstants.WhiteA700)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, getRelativeWidth(8))
                            .padding(.vertical, 4)
                            .background(Capsule()
                                .fill(ColorConstants.Red600))
                    }
                }
                
                Text(model.name)
                    .font(FontScheme.kNunitoLight(size: getRelativeHeight(11.0)))
                    .fontWeight(.light)
                    .foregroundColor(ColorConstants.Black90001)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.center)
                    .frame(width: getRelativeWidth(136.0), height: getRelativeHeight(45.0),
                           alignment: .center)
                    .padding(.top, getRelativeHeight(4.0))
                HStack {
                    Text(model.unitPrice)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Black90001)
                        .multilineTextAlignment(.leading)
                        
                    if let strokedPrice = model.strokedPrice {
                        Text(strokedPrice)
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
                            .strikethrough()
                            .fontWeight(.regular)
                            .foregroundColor(ColorConstants.Gray500)
    //                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                    }
                }
                .padding(.top, getRelativeHeight(6.0))
                
                if self.isInCart {
                    quantityPickerView
                       
                    
                } else {
                    Button(action: {
                        quantity = 1
                        onAddToCart($model, quantity) { _ in }
                    }, label: {
                        Image(systemName: "plus")
                            .fontWeight(.bold)
                            .foregroundStyle(ColorConstants.WhiteA700)
                            .frame(width: 33, height: 33)
                            .background(.primary)
                            .clipShape(.rect(cornerRadius: 8))
                            .scaleEffect(0.8,anchor: .trailing)
                    })
                    .frame(maxWidth: .infinity, alignment: .trailing)
                    .disableWithOpacity(model.currentStock < 1)
                }
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .frame(width: getRelativeWidth(161.0), alignment: .center)
            .background(RoundedCorners(topLeft: 11.0, topRight: 11.0, bottomLeft: 11.0,
                                       bottomRight: 11.0)
                .fill(ColorConstants.ProductBG))
            .padding(.top, getRelativeHeight(57.0))
            ZStack(alignment: .trailing) {
                
                NetworkImageView(path: model.thumbnailImage)
                    .frame(width: getRelativeWidth(143.0), height: getRelativeHeight(114.0),
                           alignment: .center)
                    .scaledToFit()
                    .clipShape(UnevenRoundedRectangle(topLeadingRadius: 11.0, bottomLeadingRadius: 0, bottomTrailingRadius: 0, topTrailingRadius: 11, style: .continuous))
                    .padding(.horizontal, getRelativeWidth(3.0))
                    .blendMode(.multiply)
               
                Button(action: {
                    isFavorite.toggle()
                    
                    onAddToWishlist(model, isFavorite)
                    
                }, label: {
                    Image(isFavorite ? "wishlist.fill" : "img_favorite_gray_500")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: getRelativeWidth(isFavorite ? 20 : 15), height: getRelativeWidth(isFavorite ? 20 : 15),
                               
                               alignment: .center)
                })
//                .frame(width: getRelativeWidth(isFavorite ? 30 : 24.0), height: getRelativeWidth(isFavorite ? 30 : 24.0),
//                       alignment: .center)
                .padding(4)
                .padding(.top, 32)
                .onLoad {
                    isFavorite = model.wishListStatus
                }
            }
        }
        .onTapGesture(perform: { onSelect(model) })
        .onChange(of: model.cartQty, initial: true) {
            self.quantity = $1
        }
        .onAppear {
            self.cartQty = self.model.cartQty
        }
    }
    
    private var quantityPickerView: some View {
        HStack(spacing: 15.relativeWidth) {
            Button { self.decrement } label: {
                Image(systemName: "minus")
                    .fontWeight(.bold)
                    .foregroundStyle(ColorConstants.WhiteA700)
                    .frame(width: 30, height: 30)
                    .background(.primary)
                    .clipShape(.rect(cornerRadius: 8))
            }
            
            Text(String(format: "%02d", self.quantity))
                .font(Font.custom("Poppins", size: 18).weight(.medium))
                .foregroundColor(ColorConstants.Black90001)
                .contentTransition(.numericText())
                .animation(.bouncy, value: self.quantity)
//                        .frame(width: 10)
            
            Button { self.increment } label: {
                Image(systemName: "plus")
                    .fontWeight(.bold)
                    .foregroundStyle(ColorConstants.WhiteA700)
                    .frame(width: 30, height: 30)
                    .background(.primary)
                    .clipShape(.rect(cornerRadius: 8))
            }
        }
        .disableWithOpacity(model.currentStock < 1)
        .frame(maxWidth: .infinity, alignment: .trailing)
        .padding(.top, getRelativeHeight(4.0))
        .scaleEffect(0.8,anchor: .trailing)
    }
}

 #Preview {
    NavigationStack{
        
        let sampleProduct = ProductModel(id: 1, name: "Product Name", thumbnailImage: "https://apixrx.com//public//uploads//all//D1218756-1.jpg", hasDiscount: true, discount: "10%", unitPrice: "₹ 100", strikedPrice: "₹ 200", description: "Product Description", shortDescription: "Short Description", tags: ["tag1", "tag2"], rating: 4, wishListStatus: false, currentStock: 0, buyOptionStatus: true, buyOfferText: "Buy Offer Text", cartAddedStatus: false, cartQty: 0)
        
        ProductCardCell(model: sampleProduct, onSelect: { _ in }, onAddToCart:  { _, _,_  in }, onAddToWishlist: {_, _ in}).attachAllEnvironmentObjects()
    }
 }
