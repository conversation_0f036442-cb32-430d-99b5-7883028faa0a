//
//  RxDetailsViewModel.swift
//  Wasfa
//
//  Created by Apple on 21/02/2025.
//

import SwiftUI

class RxDetailsViewModel: SuperViewModel {
    @Published var rxHistoryModelList: [RxHistoryModel] = []
    @Published var rxDetailsModelList: [RXDetailsModel] = []

    override init() {
        super.init()
      
    }
    
    func getRXHistory() {
        onApiCall(api.RXHistory, parameters: emptyDictionary) {
            self.rxHistoryModelList = $0.data ?? []
        }
    }

    func getRxDetails(id: Int) {
        onApiCall(api.RXDetails, parameters: ["id": id]) { response in
            self.rxDetailsModelList = response.data ?? []
        }
    }

    func onSelectRx(id: Int) {
        routerManager?.push(to: .rxDetails(id: id), appState: appState)
    }
}
