//
//  RxListingView.swift
//  Wasfa
//
//  Created by Apple on 11/06/2025.
//

import SwiftUI

struct RxHistoryView: View {
    @StateObject private var viewModel: RxDetailsViewModel = .init()
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            MainScrollBody(backButtonWithTitle: "Rx") {
                LazyVStack(spacing: 12) {
//                    foreach view

                    if viewModel.rxHistoryModelList.isEmpty {
                        ContentUnavailableView(
                            "No Rx Details Available",
                            systemImage: "doc.text.magnifyingglass",
                            description: Text("There are no data found at the moment. Please check back later.")
                        )
                        .padding()
                    } else {
                        ForEach(viewModel.rxHistoryModelList) {
                            RxHistoryCell(model: $0, onSelect: viewModel.onSelectRx)
                        }
                    }
                }
                .padding(.vertical)
            }
            .background(ColorConstants.WhiteA700)
        }
        .injectEnvironmentValues(viewModel)
        .onLoad(perform: viewModel.getRXHistory)
    }
}

#Preview {
    NavigationStack {
        RxHistoryView().attachAllEnvironmentObjects()
    }
}


struct RxHistoryCell: View {
    let model: RxHistoryModel
    let onSelect: (Int) -> Void
    
    var body: some View {
        
        Button {
            onSelect(model.id)
        } label: {
            VStack(alignment: .leading, spacing: 12) {
                HStack(alignment: .top) {
                    // Product Images Section
                   
                    VStack(alignment: .leading, spacing: 8) {
                        if let firstImage = model.productImages.first {
                            // Display the first image prominently
                            NetworkImageView(path: firstImage)
                                .blendMode(.multiply)
                                .padding(4)
                                .frame(width: 80, height: 80)
                                .background(Color(hex: "#F5F5F5"))
                                .cornerRadius(8)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 8)
                                        .stroke(ColorConstants.Gray200, lineWidth: 1)
                                )
                        }
                        
                        if model.productImages.count > 1 {
                            // Display a smaller section for additional images with a count indicator
                            HStack(alignment: .top, spacing: 4) {
                                let productImages = model.productImages
                                let remainingImages = productImages.count > 3 ? productImages.dropFirst().prefix(1) : productImages.dropFirst().prefix(2)
                              
                                ForEach(remainingImages, id: \.self) { image in
                                    NetworkImageView(path: image)
                                        .blendMode(.multiply)
                                        .padding(4)
                                        .frame(width: 40, height: 40)
                                        .background(Color(hex: "#F5F5F5"))
                                        .cornerRadius(5)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 5)
                                                .stroke(ColorConstants.Gray200, lineWidth: 1)
                                        )
                                }
                                
                                if model.productImages.count > 3 {
                                    Text("+\(model.productImages.count - 2)")
                                        .font(Font.custom("Poppins", size: 12.relativeFontSize))
                                        .fontWeight(.semibold)
                                        .foregroundColor(ColorConstants.Black90001)
                                        .frame(width: 40, height: 40)
                                        .background(Color(hex: "#F5F5F5"))
                                        .cornerRadius(5)
                                }
                            }
                        }
                    }.padding(.vertical, 8)
                    
                    VStack(alignment: .leading, spacing: 6.relativeHeight) {
                        Text("Order ID: #\(model.code)")
                            .font(Font.custom("Poppins", size: 15.relativeFontSize))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.Black90001)
                            .padding(.bottom, 6)
                        
                        HStack(spacing: 16.relativeWidth) {
                            Text("Date")
                                .font(Font.custom("Poppins", size: 12.relativeFontSize))
                                .fontWeight(.regular)
                                .foregroundColor(Color(hex: "#A0A0A0"))
                            
                            Text(model.date)
                                .font(Font.custom("Poppins", size: 12.relativeFontSize))
                                .fontWeight(.semibold)
                                .foregroundColor(Color(hex: "#60655C"))
                        }
                        
                        HStack(spacing: 16.relativeWidth) {
                            Text("Total Products")
                                .font(Font.custom("Poppins", size: 12.relativeFontSize))
                                .fontWeight(.regular)
                                .foregroundColor(Color(hex: "#A0A0A0"))
                           
                            Text(model.totalProductCount.toString)
                                .font(Font.custom("Poppins", size: 12.relativeFontSize))
                                .fontWeight(.semibold)
                                .foregroundColor(Color(hex: "#60655C"))
                        }
                        
                        HStack(spacing: 16.relativeWidth) {
                            Text("Total price paid")
                                .font(Font.custom("Poppins", size: 12.relativeFontSize))
                                .fontWeight(.regular)
                                .foregroundColor(Color(hex: "#A0A0A0"))
                           
                            Text(model.grandTotal)
                                .font(Font.custom("Poppins", size: 12.relativeFontSize))
                                .fontWeight(.semibold)
                                .foregroundColor(Color(hex: "#363A33"))
                        }
                    }.frame(maxWidth: .infinity, alignment: .leading)
                }
                

            }
            .padding(16)
            .frame(maxWidth: .infinity,alignment: .leading)
            .background(RoundedRectangle(cornerRadius: 8).fill(ColorConstants.WhiteA700))
            .overlay(RoundedRectangle(cornerRadius: 8).stroke(Color(hex: "#E8EBE6"), lineWidth: 1))
            .padding(.horizontal, 20.relativeWidth)
        }
    }
}
