<svg width="24" height="26" viewBox="0 0 24 26" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="12" cy="12" r="9" stroke="#1B9ED9" stroke-width="1.1"/>
<g filter="url(#filter0_d_138_3530)">
<path d="M12.521 14.595C12.4083 14.595 12.313 14.5603 12.235 14.491C12.1657 14.413 12.1267 14.296 12.118 14.14L11.832 8.511C11.8147 8.277 11.8667 8.09067 11.988 7.952C12.1093 7.81333 12.287 7.744 12.521 7.744C12.7463 7.744 12.9197 7.81333 13.041 7.952C13.1623 8.09067 13.2143 8.277 13.197 8.511L12.911 14.14C12.9023 14.296 12.8633 14.413 12.794 14.491C12.7247 14.5603 12.6337 14.595 12.521 14.595ZM12.521 17.052C12.2957 17.052 12.1093 16.9783 11.962 16.831C11.8147 16.6837 11.741 16.4973 11.741 16.272C11.741 16.0467 11.8147 15.8647 11.962 15.726C12.1093 15.5873 12.2957 15.518 12.521 15.518C12.7637 15.518 12.95 15.5873 13.08 15.726C13.2187 15.8647 13.288 16.0467 13.288 16.272C13.288 16.4973 13.2187 16.6837 13.08 16.831C12.95 16.9783 12.7637 17.052 12.521 17.052Z" fill="#1B9ED9"/>
</g>
<defs>
<filter id="filter0_d_138_3530" x="7.74121" y="7.744" width="9.54688" height="17.308" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_138_3530"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_138_3530" result="shape"/>
</filter>
</defs>
</svg>
