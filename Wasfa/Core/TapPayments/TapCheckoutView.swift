//
//  TapCheckoutView.swift
//  Wasfa
//
//  Created by Augment Agent on 29/05/2025.
//

import SwiftUI
import UIKit
import goSellSDK

/// SwiftUI wrapper for Tap Checkout SDK
struct TapCheckoutView: UIViewControllerRepresentable {

    // MARK: - Properties
    let paymentRequest: TapPaymentRequest
    let onResult: (TapPaymentResult) -> Void



    // MARK: - UIViewControllerRepresentable

    func makeUIViewController(context: Context) -> TapCheckoutViewController {
        let controller = TapCheckoutViewController()
        controller.paymentRequest = paymentRequest
        controller.onResult = onResult
        controller.coordinator = context.coordinator
        return controller
    }

    func updateUIViewController(_ uiViewController: TapCheckoutViewController, context: Context) {
        // Update if needed
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    // MARK: - Coordinator

    class Coordinator: NSObject {
        let parent: TapCheckoutView

        init(_ parent: TapCheckoutView) {
            self.parent = parent
        }
    }
}

/// UIViewController wrapper for Tap Checkout
class TapCheckoutViewController: UIViewController {

    // MARK: - Properties
    var paymentRequest: TapPaymentRequest?
    var onResult: ((TapPaymentResult) -> Void)?
    var coordinator: TapCheckoutView.Coordinator?

    private var session: Session?
    private var activityIndicator: UIActivityIndicatorView?

    // MARK: - Lifecycle

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        startCheckout()
    }

    // MARK: - Private Methods

    private func setupUI() {
        view.backgroundColor = .clear

        // Add loading indicator
        activityIndicator = UIActivityIndicatorView(style: .large)
        activityIndicator?.translatesAutoresizingMaskIntoConstraints = false
        activityIndicator?.startAnimating()

        if let indicator = activityIndicator {
            view.addSubview(indicator)
            NSLayoutConstraint.activate([
                indicator.centerXAnchor.constraint(equalTo: view.centerXAnchor),
                indicator.centerYAnchor.constraint(equalTo: view.centerYAnchor)
            ])
        }

        print("TapCheckoutView: Loading indicator shown - waiting for Tap SDK to load")
    }

    private func hideLoadingIndicator() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            if let indicator = self.activityIndicator {
                print("TapCheckoutView: Hiding loading indicator - Tap SDK UI is ready")
                indicator.stopAnimating()
                indicator.removeFromSuperview()
                self.activityIndicator = nil
            }
        }
    }

    private func startCheckout() {
        guard let request = paymentRequest else {
            // Hide loading indicator on missing payment request
            hideLoadingIndicator()
            handleResult(.failure(error: .configurationError("Payment request is missing")))
            return
        }

        // This will be implemented after CheckoutSDK-iOS is added
        setupTapCheckout(with: request)
    }

    private func setupTapCheckout(with request: TapPaymentRequest) {
        print("🔍 TapCheckoutView: Setting up Tap checkout")
        print("🔍 Request details: amount=\(request.amount), currency=\(request.currency)")

        // Create and configure Session
        session = Session()
        session?.dataSource = self
        session?.delegate = self

        // Store payment request for data source methods
        self.paymentRequest = request

        // Debug amount validation
        print("🔍 TapCheckoutView: Payment amount validation:")
        print("  - Original Amount: \(request.amount)")
        print("  - Currency: \(request.currency)")
        print("  - Items count: \(request.items.count)")

        // Format amount according to Tap Payments requirements
        let formattedAmount = formatAmountForTapPayments(request.amount, currency: request.currency)
        print("  - Formatted Amount: \(formattedAmount)")
        print("  - Amount String: \(String(format: "%.3f", formattedAmount))")

        // Check for common amount validation issues
        if formattedAmount <= 0 {
            print("❌ TapCheckoutView: Invalid amount - Amount must be greater than 0")
            hideLoadingIndicator()
            handleResult(.failure(error: .configurationError("Invalid amount - Amount must be greater than 0")))
            return
        }
        if formattedAmount < 0.1 {
            print("⚠️ TapCheckoutView: Very small amount - May not meet minimum requirements")
        }
        if formattedAmount > 10000 {
            print("⚠️ TapCheckoutView: Large amount - May exceed maximum limits")
        }

        // Check for precision issues
        if request.amount != formattedAmount {
            print("⚠️ TapCheckoutView: Amount precision adjusted from \(request.amount) to \(formattedAmount)")
        }

        // Start the session
        DispatchQueue.main.async { [weak self] in
            print("🔍 TapCheckoutView: About to start session")
            // Keep loading indicator visible until SDK UI is ready
            // hideLoadingIndicator() will be called when SDK UI appears or in delegate methods
            self?.session?.start()
            print("🔍 TapCheckoutView: Session start() called - keeping loading indicator visible")

            // Fallback: Hide loading indicator after 3 seconds if SDK UI hasn't appeared
            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) { [weak self] in
                if self?.activityIndicator != nil {
                    print("🔍 TapCheckoutView: Fallback timeout - hiding loading indicator after 3 seconds")
                    self?.hideLoadingIndicator()
                }
            }
        }
    }

    /// Helper function to parse Tap API error response
    private func parseTapAPIError(from result: [String: String]?) -> TapPaymentError? {
        guard let resultDict = result else { return nil }

        print("TapCheckoutView: Parsing API error response: \(resultDict)")

        // Check for error code 1225 (Invalid API Key)
        if let errorCode = resultDict["code"], errorCode == "1225" {
            let config = TapPaymentConfig.shared
            let technicalMessage = "Invalid API Key (Code: 1225). Merchant ID: REMOVED_FOR_TESTING (was: \(config.tapMerchantID))"
            print("TapCheckoutView: API Key Error - \(technicalMessage)")
            return .apiKeyError(technicalMessage)
        }

        // Check for other specific error codes
        if let errorCode = resultDict["code"] {
            if let description = resultDict["description"] {
                print("TapCheckoutView: API Error Code \(errorCode): \(description)")
                return .networkError("API Error (Code: \(errorCode)): \(description)")
            }
        }

        // Check for general error description
        if let description = resultDict["description"] {
            print("TapCheckoutView: API Error: \(description)")
            return .networkError("API Error: \(description)")
        }

        return nil
    }

    /// Helper function to parse Tap API error from JSON response
    private func parseTapAPIErrorFromJSON(data: Data?) -> TapPaymentError? {
        guard let data = data else { return nil }

        do {
            if let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                print("TapCheckoutView: Parsing JSON error response: \(json)")

                // Check for errors array
                if let errors = json["errors"] as? [[String: Any]], let firstError = errors.first {
                    let errorCode = firstError["code"] as? String
                    let description = firstError["description"] as? String
                    let errorType = firstError["error"] as? String

                    print("TapCheckoutView: Found error - Code: \(errorCode ?? "nil"), Description: \(description ?? "nil"), Type: \(errorType ?? "nil")")

                    // Handle specific error codes dynamically
                    if let errorCode = errorCode {
                        switch errorCode {
                        case "1225":
                            let config = TapPaymentConfig.shared
                            let technicalMessage = "Invalid API Key (Code: 1225). Merchant ID: REMOVED_FOR_TESTING (was: \(config.tapMerchantID)), Environment: \(config.currentEnvironment)"
                            print("TapCheckoutView: API Key Error - \(technicalMessage)")
                            return .apiKeyError(technicalMessage)
                        case "1117":
                            let technicalMessage = "Invalid Amount (Code: 1117). Amount validation failed: \(description ?? "Unknown amount error")"
                            print("TapCheckoutView: Amount Error - \(technicalMessage)")
                            return .configurationError(technicalMessage)
                        default:
                            // Handle other error codes generically
                            if let description = description {
                                print("TapCheckoutView: Generic API Error - Code: \(errorCode), Description: \(description)")
                                return .networkError("API Error (Code: \(errorCode)): \(description)")
                            }
                        }
                    }

                    // Handle other errors
                    if let description = description {
                        return .networkError("API Error (Code: \(errorCode ?? "unknown")): \(description)")
                    }
                }
            }
        } catch {
            print("TapCheckoutView: Failed to parse JSON error response: \(error)")
        }

        return nil
    }

    /// Extract error code from error description using various patterns
    private func extractErrorCode(from description: String) -> String? {
        // Pattern 1: ErrorCode(rawValue: XXXX)
        if let range = description.range(of: #"ErrorCode\(rawValue:\s*(\d+)\)"#, options: .regularExpression) {
            let match = String(description[range])
            if let codeRange = match.range(of: #"\d+"#, options: .regularExpression) {
                return String(match[codeRange])
            }
        }

        // Pattern 2: Code: XXXX or code XXXX
        if let range = description.range(of: #"[Cc]ode:?\s*(\d+)"#, options: .regularExpression) {
            let match = String(description[range])
            if let codeRange = match.range(of: #"\d+"#, options: .regularExpression) {
                return String(match[codeRange])
            }
        }

        // Pattern 3: Error XXXX or error XXXX
        if let range = description.range(of: #"[Ee]rror:?\s*(\d+)"#, options: .regularExpression) {
            let match = String(description[range])
            if let codeRange = match.range(of: #"\d+"#, options: .regularExpression) {
                return String(match[codeRange])
            }
        }

        // Pattern 4: Any 3-4 digit number that could be an error code
        if let range = description.range(of: #"\b\d{3,4}\b"#, options: .regularExpression) {
            return String(description[range])
        }

        return nil
    }

    /// Extract error type from error description
    private func extractErrorType(from description: String) -> String? {
        // Common error type patterns
        let errorTypes = ["BACKEND_ERROR", "VALIDATION_ERROR", "NETWORK_ERROR", "AUTH_ERROR", "API_ERROR", "CLIENT_ERROR", "SERVER_ERROR"]

        for errorType in errorTypes {
            if description.contains(errorType) {
                return errorType
            }
        }

        // Check for generic error patterns
        if description.lowercased().contains("backend") {
            return "BACKEND_ERROR"
        } else if description.lowercased().contains("validation") {
            return "VALIDATION_ERROR"
        } else if description.lowercased().contains("network") {
            return "NETWORK_ERROR"
        } else if description.lowercased().contains("auth") {
            return "AUTH_ERROR"
        }

        return nil
    }

    /// Extract meaningful description from error message
    private func extractErrorDescription(from description: String) -> String? {
        // Try to extract the meaningful part after the error code
        if let range = description.range(of: #":\s*(.+)$"#, options: .regularExpression) {
            let match = String(description[range])
            let cleanedDescription = match.replacingOccurrences(of: ":", with: "").trimmingCharacters(in: .whitespaces)
            if !cleanedDescription.isEmpty {
                return cleanedDescription
            }
        }

        // If no pattern found, return the original description
        return description
    }

    /// Format amount according to Tap Payments requirements
    /// - Exactly 3 decimal places for all currencies
    /// - Minimum amount 0.100
    /// - Positive decimal value
    /// - ISO standard compliance
    private func formatAmountForTapPayments(_ amount: Double, currency: String) -> Double {
        print("🔧 TapCheckoutView: Formatting amount for Tap Payments compliance")
        print("  - Input amount: \(amount)")
        print("  - Currency: \(currency)")

        // Ensure positive value
        guard amount > 0 else {
            print("  ❌ Amount must be positive, using minimum: 0.100")
            return 0.100
        }

        // Format to exactly 3 decimal places (ISO standard)
        let formattedString = String(format: "%.3f", amount)
        let formattedAmount = amount.roundedDecimal(toPlaces: 3)
//        let formattedAmount = Double(formattedString) ?? amount

        _ = amount.roundedDecimal(toPlaces: 3)

        print("  - Formatted string: '\(formattedString)'")
        print("  - Formatted amount: \(formattedAmount)")

        // Ensure minimum amount requirement (0.100)
        if formattedAmount < 0.100 {
            print("  ⚠️ Amount \(formattedAmount) below minimum 0.100, adjusting to minimum")
            return 0.100
        }

        // Validate the formatting
        let decimalPlaces = formattedString.components(separatedBy: ".").last?.count ?? 0
        if decimalPlaces != 3 {
            print("  ⚠️ Decimal places: \(decimalPlaces), should be exactly 3")
        } else {
            print("  ✅ Amount correctly formatted with 3 decimal places")
        }

        return formattedAmount
    }

    /// Helper function to extract error information from Error parameter and convert to dictionary format
    private func extractErrorInfo(from error: Error?) -> [String: String]? {
        guard let error = error else { return nil }

        print("TapCheckoutView: Extracting error info from Error parameter")

        // Handle NSError with userInfo
        if let nsError = error as NSError? {
            print("TapCheckoutView: NSError domain: \(nsError.domain), code: \(nsError.code)")
            print("TapCheckoutView: NSError userInfo: \(nsError.userInfo)")

            var errorDict: [String: String] = [:]

            // Extract error code from NSError
            errorDict["code"] = String(nsError.code)

            // Extract description from various possible keys
            if let description = nsError.userInfo[NSLocalizedDescriptionKey] as? String {
                errorDict["description"] = description
            } else if let description = nsError.userInfo["description"] as? String {
                errorDict["description"] = description
            } else if let description = nsError.userInfo["error_description"] as? String {
                errorDict["description"] = description
            } else {
                errorDict["description"] = nsError.localizedDescription
            }

            // Extract error type/category
            if let errorType = nsError.userInfo["error"] as? String {
                errorDict["error"] = errorType
            } else {
                errorDict["error"] = nsError.domain
            }

            // Check for specific Tap SDK error patterns
            if nsError.domain.contains("tap") || nsError.domain.contains("gosell") {
                // This is likely a Tap SDK error
                if let tapErrorCode = nsError.userInfo["tap_error_code"] as? String {
                    errorDict["code"] = tapErrorCode
                }
            }

            print("TapCheckoutView: Extracted error dictionary: \(errorDict)")
            return errorDict.isEmpty ? nil : errorDict
        }

        // Handle other error types by trying to extract information from description
        let errorDescription = error.localizedDescription
        print("TapCheckoutView: Parsing error description: \(errorDescription)")

        // Try to extract error information dynamically from error description
        var extractedInfo: [String: String] = [:]

        // Extract error code using regex patterns
        if let codeMatch = extractErrorCode(from: errorDescription) {
            extractedInfo["code"] = codeMatch
            print("TapCheckoutView: Extracted error code: \(codeMatch)")
        }

        // Extract error type (BACKEND_ERROR, VALIDATION_ERROR, etc.)
        if let errorType = extractErrorType(from: errorDescription) {
            extractedInfo["error"] = errorType
            print("TapCheckoutView: Extracted error type: \(errorType)")
        }

        // Extract description
        if let description = extractErrorDescription(from: errorDescription) {
            extractedInfo["description"] = description
            print("TapCheckoutView: Extracted description: \(description)")
        } else {
            extractedInfo["description"] = errorDescription
        }

        // Return extracted info if we found meaningful data
        if !extractedInfo.isEmpty {
            print("TapCheckoutView: Successfully extracted error info: \(extractedInfo)")
            return extractedInfo
        }

        return nil
    }



    /// Generate a valid customer ID using priority system based on Tap Payments documentation:
    /// 1. Primary: Use logged-in user ID if available (simple format)
    /// 2. Secondary: Use simple customer ID format for guests
    private func generateValidCustomerID(from email: String) -> String {
        print("🔧 Starting customer ID generation with priority system")

        // Priority 1: Check if user is logged in and has a valid user ID
        if AppState.isLoggedIn, let user = AppState.user {
            // Use simple format like official documentation: "cus_123"
            let userID = "cus_\(user.id)"
            print("🔧 ✅ PRIMARY: Using logged-in user ID: \(userID) (User: \(user.name ?? "Unknown"))")
            return userID
        }

        // Priority 2: Use simple guest customer ID format
        print("🔧 📧 SECONDARY: No logged-in user, generating simple guest ID for email: \(email)")
        return generateSimpleCustomerID(from: email)
    }

    /// Generate simple customer ID based on Tap Payments documentation patterns
    /// Uses the same format as official examples: "cus_" prefix with simple identifier
    private func generateSimpleCustomerID(from email: String) -> String {
        print("🔧 Generating simple customer ID from: \(email)")

        // Use simple hash-based approach like official documentation suggests
        let emailHash = abs(email.hash) % 999999 // 6-digit max for simplicity
        let customerID = "cus_\(emailHash)"

        print("🔧 Generated simple customer ID: \(customerID)")
        return customerID
    }

    /// Extract meaningful error information from payment failure for user-friendly messages
    private func extractPaymentFailureInfo(charge: Charge?, error: TapSDKError?) -> (errorCode: String?, errorDescription: String?, chargeStatus: Int?) {
        var errorCode: String? = nil
        var errorDescription: String? = nil
        var chargeStatus: Int? = nil

        // Extract information from charge object
        if let charge = charge {
            chargeStatus = charge.status.rawValue
        }

        // Extract information from TapSDKError
        if let error = error {
            let errorString = error.localizedDescription
            print("🔍 TapCheckoutView: Processing TapSDKError: \(errorString)")

            // If we don't have error code yet, try to extract from error description
            if errorCode == nil {
                errorCode = extractErrorCode(from: errorString)
                print("🔍 TapCheckoutView: Extracted error code from TapSDKError: \(errorCode ?? "nil")")
            }

            // If we don't have error description yet, use the error description
            if errorDescription == nil {
                errorDescription = errorString
                print("🔍 TapCheckoutView: Using TapSDKError description: \(errorDescription ?? "nil")")
            }

            // Try to extract additional information from NSError userInfo
            if let nsError = error as? NSError {
                print("🔍 TapCheckoutView: NSError domain: \(nsError.domain), code: \(nsError.code)")
                print("🔍 TapCheckoutView: NSError userInfo: \(nsError.userInfo)")

                // Look for Tap-specific error information
                if let tapErrorCode = nsError.userInfo["tap_error_code"] as? String {
                    errorCode = tapErrorCode
                    print("🔍 TapCheckoutView: Found tap_error_code: \(tapErrorCode)")
                }

                if let tapErrorDescription = nsError.userInfo["tap_error_description"] as? String {
                    errorDescription = tapErrorDescription
                    print("🔍 TapCheckoutView: Found tap_error_description: \(tapErrorDescription)")
                }
            }
        }

//        print("🔍 TapCheckoutView: Final extracted info - Code: \(errorCode ?? "nil"), Description: \(errorDescription ?? "nil"), Status: \(chargeStatus ?? "nil")")
        return (errorCode, errorDescription, chargeStatus)
    }

    private func handleResult(_ result: TapPaymentResult) {
        DispatchQueue.main.async {
          
            self.onResult?(result)
           
        }
    }
}

// MARK: - SessionDataSource Implementation
extension TapCheckoutViewController: SessionDataSource {

    var currency: Currency? {
        print("🔍 SessionDataSource: currency called")
        guard let request = paymentRequest else {
            print("🔍 SessionDataSource: No payment request, using default KWD")
            return .with(isoCode: "KWD")
        }
        print("🔍 SessionDataSource: Using currency: \(request.currency)")
        let currency = Currency.with(isoCode: request.currency)
        print("🔍 SessionDataSource: Currency object created: \(currency)")
        return currency
    }

    var amount: Decimal {
        print("🔍 SessionDataSource: amount called")
        guard let request = paymentRequest else {
            print("🔍 SessionDataSource: No payment request, using default 1.000")
            return 1.000
        }
        let formattedAmount = formatAmountForTapPayments(request.amount, currency: request.currency)
        let decimal = Decimal(formattedAmount)
        print("🔍 SessionDataSource: Amount - original: \(request.amount), formatted: \(formattedAmount), decimal: \(decimal)")
        return decimal
    }

    var items: [PaymentItem]? {
        // For now, return nil to use amount-based payment instead of item-based
        // This is a common approach when items are complex to construct
        return nil
    }

    var customer: Customer? {
        print("🔍 SessionDataSource: customer called")
        guard let request = paymentRequest else {
            print("🔍 SessionDataSource: No payment request, creating default customer")
            do {
                // Use same format as official documentation: "cus_" prefix
//                let customerID = "cus_default"
                let customerID = ""
                let customer = try Customer(identifier: customerID)
                customer.emailAddress = try EmailAddress(emailAddressString: "<EMAIL>")
                print("🔍 SessionDataSource: Default customer created successfully with ID: \(customerID)")
                return customer
            } catch {
                print("🔍 SessionDataSource: ❌ Error creating default customer: \(error)")
                return nil
            }
        }

        print("🔍 SessionDataSource: Creating customer with email: \(request.customerEmail), name: \(request.customerName)")
        print("🔍 SessionDataSource: User login status: \(AppState.isLoggedIn), User: \(AppState.user?.name ?? "nil")")
        do {
            // Generate valid customer ID using priority system (fixes ErrorCode 1105)
            let customerID = generateValidCustomerID(from: request.customerEmail)
            let customer = try Customer(identifier: customerID)
            customer.emailAddress = try EmailAddress(emailAddressString: request.customerEmail)
            customer.firstName = request.customerName

            // Handle phone number
            if let phoneString = request.customerPhone {
                print("🔍 SessionDataSource: Processing phone number: \(phoneString)")
                let cleanedPhone = phoneString.replacingOccurrences(of: "[^0-9+]", with: "", options: .regularExpression)

                if cleanedPhone.hasPrefix("+965") {
                    let phoneWithoutCountryCode = String(cleanedPhone.dropFirst(4))
                    if phoneWithoutCountryCode.count == 8 && phoneWithoutCountryCode.allSatisfy({ $0.isNumber }) {
                        customer.phoneNumber = try PhoneNumber(isdNumber: "965", phoneNumber: phoneWithoutCountryCode)
                    }
                } else if cleanedPhone.hasPrefix("965") {
                    let phoneWithoutCountryCode = String(cleanedPhone.dropFirst(3))
                    if phoneWithoutCountryCode.count == 8 && phoneWithoutCountryCode.allSatisfy({ $0.isNumber }) {
                        customer.phoneNumber = try PhoneNumber(isdNumber: "965", phoneNumber: phoneWithoutCountryCode)
                    }
                } else if cleanedPhone.count == 8 && cleanedPhone.allSatisfy({ $0.isNumber }) {
                    customer.phoneNumber = try PhoneNumber(isdNumber: "965", phoneNumber: cleanedPhone)
                }
            }

            print("🔍 SessionDataSource: Customer created successfully with ID: \(customerID)")
            customer.identifier = ""
            return customer
        } catch {
            print("🔍 SessionDataSource: ❌ Error creating customer: \(error)")
            print("🔍 SessionDataSource: Customer details - email: '\(request.customerEmail)', phone: '\(request.customerPhone ?? "nil")', name: '\(request.customerName)'")
            return nil
        }
    }

    var paymentType: PaymentType {
        print("🔍 SessionDataSource: paymentType called")
        // Use .all to include KNET and credit cards (Apple Pay disabled via empty merchantID)
        let type = PaymentType.all
        print("🔍 SessionDataSource: Using payment type: \(type)")
        return type
    }

    var applePayMerchantID: String {
        print("🔍 SessionDataSource: applePayMerchantID called")
        // Return empty string to disable Apple Pay
        let merchantID = ""
        print("🔍 SessionDataSource: Apple Pay merchant ID: '\(merchantID)' (disabled)")
        return merchantID
    }

    var sdkMode: SDKMode {
        print("🔍 SessionDataSource: sdkMode called")
        let config = TapPaymentConfig.shared
        let mode = config.currentEnvironment == .sandbox ? SDKMode.sandbox : SDKMode.production
        print("🔍 SessionDataSource: Using SDK mode: \(mode)")
        return mode
    }

    var mode: TransactionMode {
        print("🔍 SessionDataSource: mode called")
        // Use the correct transaction mode for purchase
        let transactionMode = TransactionMode.purchase
        print("🔍 SessionDataSource: Using transaction mode: \(transactionMode)")
        return transactionMode
    }

    // CRITICAL: This property is required by the SDK
    var supportedPaymentMethods: [String] {
        print("🔍 SessionDataSource: supportedPaymentMethods called")
        // Return empty array to let SDK determine available methods based on merchant config
        // This matches Flutter implementation when no specific methods are configured
        let methods: [String] = []
        print("🔍 SessionDataSource: Supported payment methods: \(methods) (empty - let SDK decide)")
        return methods
    }

    // CRITICAL: This property is required by the SDK (note: CadTypes not CardTypes)
    var allowedCadTypes: [CardType]? {
        print("🔍 SessionDataSource: allowedCadTypes called")
        // Include KNET, Debit, Credit, and All card types (excluding Apple Pay)
        let cardTypes = [CardType(cardType: .Debit), CardType(cardType: .Credit), CardType(cardType: .All)]
        print("🔍 SessionDataSource: Allowed card types: \(cardTypes)")
        return cardTypes
    }

    // Additional optional properties that might be required
    var merchantID: String? {
        print("🔍 SessionDataSource: merchantID called")
        // CRITICAL FIX: Match Flutter implementation - return empty string
        // The SessionDataSource merchantID is NOT the same as Tap merchant ID
        // Tap merchant identification happens through API keys in SDK initialization
        let merchantID = ""
        print("🔍 SessionDataSource: Merchant ID: '\(merchantID)' (empty string - matches Flutter)")
        return merchantID
    }

    var postURL: String? {
        print("🔍 SessionDataSource: postURL called")
        return nil
    }

    var paymentDescription: String? {
        print("🔍 SessionDataSource: paymentDescription called")
        return "Payment from Wasfa"
    }

    var isUserAllowedToSaveCard: Bool {
        print("🔍 SessionDataSource: isUserAllowedToSaveCard called")
        return false
    }

    var isSaveCardSwitchOnByDefault: Bool {
        print("🔍 SessionDataSource: isSaveCardSwitchOnByDefault called")
        return false
    }

    // Additional required properties from reference implementation
    var require3DSecure: Bool {
        print("🔍 SessionDataSource: require3DSecure called")
        return false
    }

    var receiptSettings: Receipt? {
        print("🔍 SessionDataSource: receiptSettings called")
        return Receipt(email: false, sms: false)
    }

    var authorizeAction: AuthorizeAction {
        print("🔍 SessionDataSource: authorizeAction called")
        return .void(after: 0)
    }

    var paymentReference: Reference? {
        print("🔍 SessionDataSource: paymentReference called")
        return nil
    }

    var destinations: DestinationGroup? {
        print("🔍 SessionDataSource: destinations called")
        return nil
    }

    var shipping: [Shipping]? {
        print("🔍 SessionDataSource: shipping called")
        return nil
    }

    var taxes: [Tax]? {
        print("🔍 SessionDataSource: taxes called")
        return nil
    }

    var paymentMetadata: Metadata? {
        print("🔍 SessionDataSource: paymentMetadata called")
        return nil
    }
}

// MARK: - SessionDelegate Implementation
extension TapCheckoutViewController: SessionDelegate {

    // Called when payment UI is ready and displayed - perfect timing to hide loading indicator
    func paymentInitiated(with charge: Charge?, on session: SessionProtocol) {
        print("🔍 SessionDelegate: paymentInitiated called - SDK UI is ready")
        print("🔍 SessionDelegate: Charge: \(charge?.identifier ?? "nil")")
        // Hide loading indicator now that SDK UI is displayed
        hideLoadingIndicator()
    }

    func paymentSucceed(_ charge: Charge, on session: SessionProtocol) {
        print("🔍 SessionDelegate: paymentSucceed called")
        print("🔍 SessionDelegate: Charge ID: \(charge.identifier), Amount: \(charge.amount), Currency: \(charge.currency.isoCode)")
        hideLoadingIndicator() // Ensure indicator is hidden (in case paymentInitiated wasn't called)
        let result = TapPaymentResult.success(
            transactionId: charge.identifier,
            amount: Double(truncating: charge.amount as NSNumber),
            currency: charge.currency.isoCode
        )
        handleResult(result)
    }

    func paymentFailed(with charge: Charge?, error: TapSDKError?, on session: SessionProtocol) {
        print("🔍 SessionDelegate: paymentFailed called")
        print("🔍 SessionDelegate: Charge: \(charge?.identifier ?? "nil"), Error: \(error?.localizedDescription ?? "nil")")
        hideLoadingIndicator()

        // Extract meaningful error information for user-friendly messages
        let errorInfo = extractPaymentFailureInfo(charge: charge, error: error)

        // Generate user-friendly error message
        let userFriendlyMessage = TapPaymentError.getUserFriendlyErrorMessage(
            errorCode: errorInfo.errorCode,
            errorDescription: errorInfo.errorDescription,
            chargeStatus: errorInfo.chargeStatus
        )

//        print("🔍 SessionDelegate: Generated user-friendly message: \(userFriendlyMessage)")
//        print("🔍 SessionDelegate: Technical details - Code: \(errorInfo.errorCode ?? "nil"), Description: \(errorInfo.errorDescription ?? "nil"), Status: \(errorInfo.chargeStatus ?? "nil")")

        // Create enhanced TapPaymentError with user-friendly message
        let tapError = TapPaymentError.paymentFailed(userFriendlyMessage)
        handleResult(.failure(error: tapError))
    }

    func sessionCancelled(_ session: SessionProtocol) {
        print("🔍 SessionDelegate: sessionCancelled called")
        hideLoadingIndicator()
        handleResult(.cancelled)
    }
}


extension Double {
    func roundedDecimal(toPlaces places: Int) -> Double {
        var decimalValue = Decimal(self)
        var roundedValue = Decimal()
        NSDecimalRound(&roundedValue, &decimalValue, places, .plain)
        return NSDecimalNumber(decimal: roundedValue).doubleValue
    }

    func roundedString(toPlaces places: Int) -> String {
        let roundedValue = self.roundedDecimal(toPlaces: places)
        return String(format: "%.\(places)f", roundedValue)
    }
}
