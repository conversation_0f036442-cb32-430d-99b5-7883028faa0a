//
//  TapPaymentManager.swift
//  Wasfa
//
//  Created by Augment Agent on 29/05/2025.
//

import goSellSDK
import Foundation
import SwiftUI
import UIKit

/// Payment result for async/await operations
enum TapPaymentResult {
    case success(transactionId: String, amount: Double, currency: String)
    case failure(error: TapPaymentError)
    case cancelled
}

/// Custom error types for Tap Payment operations
enum TapPaymentError: LocalizedError {
    case configurationError(String)
    case networkError(String)
    case paymentFailed(String)
    case userCancelled
    case apiKeyError(String)
    case phoneNumberError(String)
    case unknownError(String)

    var errorDescription: String? {
        switch self {
        case .configurationError(let message):
            return "Configuration Error: \(message)"
        case .networkError(let message):
            return "Network Error: \(message)"
        case .paymentFailed(let message):
            return "Payment Failed: \(message)"
        case .userCancelled:
            return "Payment was cancelled by user"
        case .apiKeyError(let message):
            return message
        case .phoneNumberError(let message):
            return message
        case .unknownError(let message):
            return "Unknown Error: \(message)"
        }
    }

    /// User-friendly error message for display in alerts
    var userFriendlyMessage: String {
        switch self {
        case .configurationError:
            return "Payment service configuration error. Please contact support."
        case .networkError(let message):
            return "Network Error: \(message)"
        case .paymentFailed(let message):
            return message
        case .userCancelled:
            return "Payment was cancelled."
        case .apiKeyError(let message):
            return "API Key Error.\(message)"
        case .phoneNumberError(let message):
            return message
        case .unknownError:
            return "An unexpected error occurred. Please try again or contact support."
        }
    }

 
    /// Generate user-friendly error message based on error code and context
    static func getUserFriendlyErrorMessage(errorCode: String?, errorDescription: String?, chargeStatus: Int? = nil) -> String {
        // Handle specific error codes with user-friendly messages
        if let errorCode = errorCode {
            switch errorCode {
            case "1105":
                return "Payment validation failed. Please check your payment details and try again."
            case "1225":
                return "Payment service configuration error. Please contact support."
            case "1117":
                return "Invalid payment amount. Please check the amount and try again."
            case "1001", "1002", "1003":
                return "Payment declined by your bank. Please try a different payment method or contact your bank."
            case "2001", "2002":
                return "Card details are invalid. Please check your card information and try again."
            case "3001", "3002", "3003":
                return "Network connection error. Please check your internet connection and try again."
            case "4001", "4002":
                return "Payment service temporarily unavailable. Please try again later."
            case "5001", "5002":
                return "Transaction limit exceeded. Please try with a smaller amount or contact your bank."
            default:
                // For unknown error codes, provide a generic but helpful message
                return "Payment could not be processed. Please try again or contact support if the problem persists."
            }
        }

        // Handle based on error description patterns
        if let description = errorDescription?.lowercased() {
            if description.contains("network") || description.contains("connection") {
                return "Network connection error. Please check your internet connection and try again."
            } else if description.contains("declined") || description.contains("rejected") {
                return "Payment declined by your bank. Please try a different payment method or contact your bank."
            } else if description.contains("invalid card") || description.contains("card number") {
                return "Card details are invalid. Please check your card information and try again."
            } else if description.contains("insufficient") || description.contains("limit") {
                return "Transaction limit exceeded. Please try with a smaller amount or contact your bank."
            } else if description.contains("expired") {
                return "Your card has expired. Please use a different payment method."
            } else if description.contains("timeout") {
                return "Payment request timed out. Please try again."
            } else if description.contains("authentication") || description.contains("3d secure") {
                return "Payment authentication failed. Please try again or use a different payment method."
            }
        }

        if let status = chargeStatus, let chargeStatus = ChargeStatus(rawValue: status) {
            return getChargeStatusDescription(status: chargeStatus)
        }
        

        // Default fallback message
        return "Payment could not be processed. Please try again or contact support if the problem persists."
    }

    /// Technical error message for debugging
    var technicalMessage: String {
        return errorDescription ?? "Unknown error"
    }
}

enum ChargeStatus: Int {
    case unknown = 0
    case pending = 1
    case succeeded = 2
    case failed = 3
    case canceled = 4
    case declined = 5
    // Add all relevant statuses as per your SDK
}

func getChargeStatusDescription(status: ChargeStatus) -> String {
    switch status {
    case .unknown:
        return "Unknown status"
    case .pending:
        return "Payment is pending"
    case .succeeded:
        return "Payment succeeded"
    case .failed:
        return "Payment failed: Payment failed. Please check your payment details and try again."
    case .canceled:
        return "Payment canceled: Payment was cancelled. You can try again when ready."
    case .declined:
        return "Payment declined: Payment declined by your bank. Please try a different payment method or contact your bank."
    }
}


/// Customer address information for Tap Payment
struct TapPaymentAddress {
    let street: String?
    let city: String?
    let state: String?
    let country: String?
    let postalCode: String?
    let area: String?
    let block: String?
    let building: String?
    let floor: String?
    let apartment: String?
    let addressType: AddressType?

    init(street: String? = nil,
         city: String? = nil,
         state: String? = nil,
         country: String? = nil,
         postalCode: String? = nil,
         area: String? = nil,
         block: String? = nil,
         building: String? = nil,
         floor: String? = nil,
         apartment: String? = nil,
         addressType: AddressType? = .residential)
    {
        self.street = street
        self.city = city
        self.state = state
        self.country = country
        self.postalCode = postalCode
        self.area = area
        self.block = block
        self.building = building
        self.floor = floor
        self.apartment = apartment
        self.addressType = addressType
    }

    /// Create TapPaymentAddress from Wasfa's AddressModel
    init(from addressModel: AddressModel) {
        self.street = addressModel.street
        self.city = addressModel.areaName
        self.state = addressModel.governorateName
        self.country = "KW" // Kuwait ISO code
        self.postalCode = nil // Kuwait doesn't typically use postal codes
        self.area = addressModel.areaName
        self.block = addressModel.block
        self.building = addressModel.building
        self.floor = addressModel.floor
        self.apartment = addressModel.appartment
        self.addressType = addressModel.addressTitle == .homeApartment ? .residential : .commercial
    }
}

/// Payment request structure
struct TapPaymentRequest {
    let amount: Double
    let currency: String
    let customerEmail: String
    let customerName: String
    let customerPhone: String?
    let customerAddress: TapPaymentAddress?
    let items: [TapPaymentItem]
    let description: String?
    let metadata: [String: String]?

    init(amount: Double,
         currency: String = "KWD",
         customerEmail: String,
         customerName: String,
         customerPhone: String? = nil,
         customerAddress: TapPaymentAddress? = nil,
         items: [TapPaymentItem] = [],
         description: String? = nil,
         metadata: [String: String]? = nil)
    {
        self.amount = amount
        self.currency = currency
        self.customerEmail = customerEmail
        self.customerName = customerName
        self.customerPhone = customerPhone
        self.customerAddress = customerAddress
        self.items = items.isEmpty ? [TapPaymentItem(title: "Order Payment", price: amount)] : items
        self.description = description
        self.metadata = metadata
    }
}

/// Payment item structure
struct TapPaymentItem {
    let title: String
    let description: String?
    let price: Double
    let quantity: Int

    init(title: String, description: String? = nil, price: Double, quantity: Int = 1) {
        self.title = title
        self.description = description
        self.price = price
        self.quantity = quantity
    }
}

/// Main Tap Payment Manager
@MainActor
class TapPaymentManager: ObservableObject {
    // MARK: - Properties

    static let shared = TapPaymentManager()

    @Published var isLoading = false
    @Published var lastError: TapPaymentError?

    private var currentContinuation: CheckedContinuation<TapPaymentResult, Never>?
    private let config = TapPaymentConfig.shared

    // MARK: - Initialization

    private init() {
        setupTapSDK()
    }

    // MARK: - Public Methods

    /// Process payment with async/await
    func processPayment(request: TapPaymentRequest, presentingViewController: UIViewController) async -> TapPaymentResult {
        // Validate configuration
        guard config.isConfigurationValid() else {
            let errors = config.getConfigurationErrors().joined(separator: ", ")
            return .failure(error: .configurationError(errors))
        }

        isLoading = true
        lastError = nil

        return await withCheckedContinuation { continuation in
            currentContinuation = continuation

            // This will be implemented after CheckoutSDK-iOS is added
            startTapCheckout(request: request, presentingViewController: presentingViewController)
        }
    }

    /// Check if Tap Payments is properly configured
    func isConfigured() -> Bool {
        return config.isConfigurationValid()
    }

    /// Get configuration status
    func getConfigurationStatus() -> (isValid: Bool, errors: [String]) {
        let errors = config.getConfigurationErrors()
        return (errors.isEmpty, errors)
    }

    // MARK: - Private Methods

    private func setupTapSDK() {
        // Debug environment variables
        config.debugEnvironmentVariables()

        let sandboxKey = config.getSandboxKey()
        let productionKey = config.getProductionKey()
        let currentKey = config.getCurrentAPIKey()
        let merchantID = config.tapMerchantID

        print("TapPaymentManager: Setting up SDK with:")
        print("  - Environment: \(config.currentEnvironment)")
        print("  - Sandbox Key: \(sandboxKey.prefix(10))...")
        print("  - Production Key: \(productionKey.prefix(10))...")
        print("  - Current Key: \(currentKey.prefix(10))...")
        print("  - Merchant ID: REMOVED_FOR_TESTING (was: \(merchantID))")

        // Setup goSellSDK
        GoSellSDK.secretKey = .init(sandbox: sandboxKey, production: productionKey)
        GoSellSDK.language = AppState.language // Set language
        GoSellSDK.mode = .sandbox
//        GoSellSDK.mode = config.currentEnvironment == .sandbox ? .sandbox : .production

        print("TapPaymentManager: SDK setup completed with goSellSDK")

        // Validate configuration
        if !config.isConfigurationValid() {
            let errors = config.getConfigurationErrors()
            print("TapPaymentManager: Configuration errors: \(errors)")
        }
    }

    /// Helper function to parse Tap API error response
    private func parseTapAPIError(from result: [String: String]?) -> TapPaymentError? {
        guard let resultDict = result else { return nil }

        print("TapPaymentManager: Parsing API error response: \(resultDict)")

        // Check for error code 1225 (Invalid API Key)
        if let errorCode = resultDict["code"], errorCode == "1225" {
            let technicalMessage = "Invalid API Key (Code: 1225). Current merchant ID: \(config.tapMerchantID)"
            print("TapPaymentManager: API Key Error - \(technicalMessage)")
            return .apiKeyError(technicalMessage)
        }

        // Check for other specific error codes
        if let errorCode = resultDict["code"] {
            if let description = resultDict["description"] {
                print("TapPaymentManager: API Error Code \(errorCode): \(description)")
                return .networkError("API Error (Code: \(errorCode)): \(description)")
            }
        }

        // Check for general error description
        if let description = resultDict["description"] {
            print("TapPaymentManager: API Error: \(description)")
            return .networkError("API Error: \(description)")
        }

        return nil
    }

    /// Helper function to parse Tap API error from JSON response
    private func parseTapAPIErrorFromJSON(data: Data?) -> TapPaymentError? {
        guard let data = data else { return nil }

        do {
            if let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                print("TapPaymentManager: Parsing JSON error response: \(json)")

                // Check for errors array
                if let errors = json["errors"] as? [[String: Any]], let firstError = errors.first {
                    let errorCode = firstError["code"] as? String
                    let description = firstError["description"] as? String
                    let errorType = firstError["error"] as? String

                    print("TapPaymentManager: Found error - Code: \(errorCode ?? "nil"), Description: \(description ?? "nil"), Type: \(errorType ?? "nil")")

                    // Handle specific error codes dynamically
                    if let errorCode = errorCode {
                        switch errorCode {
                        case "1225":
                            let technicalMessage = "Invalid API Key (Code: 1225). Merchant ID: REMOVED_FOR_TESTING (was: \(config.tapMerchantID)), Environment: \(config.currentEnvironment)"
                            print("TapPaymentManager: API Key Error - \(technicalMessage)")
                            return .apiKeyError(technicalMessage)
                        case "1117":
                            let technicalMessage = "Invalid Amount (Code: 1117). Amount validation failed: \(description ?? "Unknown amount error")"
                            print("TapPaymentManager: Amount Error - \(technicalMessage)")
                            return .configurationError(technicalMessage)
                        default:
                            // Handle other error codes generically
                            if let description = description {
                                print("TapPaymentManager: Generic API Error - Code: \(errorCode), Description: \(description)")
                                return .networkError("API Error (Code: \(errorCode)): \(description)")
                            }
                        }
                    }

                    // Handle other errors
                    if let description = description {
                        return .networkError("API Error (Code: \(errorCode ?? "unknown")): \(description)")
                    }
                }
            }
        } catch {
            print("TapPaymentManager: Failed to parse JSON error response: \(error)")
        }

        return nil
    }

    /// Extract error code from error description using various patterns
    private func extractErrorCode(from description: String) -> String? {
        // Pattern 1: ErrorCode(rawValue: XXXX)
        if let range = description.range(of: #"ErrorCode\(rawValue:\s*(\d+)\)"#, options: .regularExpression) {
            let match = String(description[range])
            if let codeRange = match.range(of: #"\d+"#, options: .regularExpression) {
                return String(match[codeRange])
            }
        }

        // Pattern 2: Code: XXXX or code XXXX
        if let range = description.range(of: #"[Cc]ode:?\s*(\d+)"#, options: .regularExpression) {
            let match = String(description[range])
            if let codeRange = match.range(of: #"\d+"#, options: .regularExpression) {
                return String(match[codeRange])
            }
        }

        // Pattern 3: Error XXXX or error XXXX
        if let range = description.range(of: #"[Ee]rror:?\s*(\d+)"#, options: .regularExpression) {
            let match = String(description[range])
            if let codeRange = match.range(of: #"\d+"#, options: .regularExpression) {
                return String(match[codeRange])
            }
        }

        // Pattern 4: Any 3-4 digit number that could be an error code
        if let range = description.range(of: #"\b\d{3,4}\b"#, options: .regularExpression) {
            return String(description[range])
        }

        return nil
    }

    /// Extract error type from error description
    private func extractErrorType(from description: String) -> String? {
        // Common error type patterns
        let errorTypes = ["BACKEND_ERROR", "VALIDATION_ERROR", "NETWORK_ERROR", "AUTH_ERROR", "API_ERROR", "CLIENT_ERROR", "SERVER_ERROR"]

        for errorType in errorTypes {
            if description.contains(errorType) {
                return errorType
            }
        }

        // Check for generic error patterns
        if description.lowercased().contains("backend") {
            return "BACKEND_ERROR"
        } else if description.lowercased().contains("validation") {
            return "VALIDATION_ERROR"
        } else if description.lowercased().contains("network") {
            return "NETWORK_ERROR"
        } else if description.lowercased().contains("auth") {
            return "AUTH_ERROR"
        }

        return nil
    }

    /// Extract meaningful description from error message
    private func extractErrorDescription(from description: String) -> String? {
        // Try to extract the meaningful part after the error code
        if let range = description.range(of: #":\s*(.+)$"#, options: .regularExpression) {
            let match = String(description[range])
            let cleanedDescription = match.replacingOccurrences(of: ":", with: "").trimmingCharacters(in: .whitespaces)
            if !cleanedDescription.isEmpty {
                return cleanedDescription
            }
        }

        // If no pattern found, return the original description
        return description
    }

    /// Format amount according to Tap Payments requirements
    /// - Exactly 3 decimal places for all currencies
    /// - Minimum amount 0.100
    /// - Positive decimal value
    /// - ISO standard compliance
    private func formatAmountForTapPayments(_ amount: Double, currency: String) -> Double {
        print("🔧 TapPaymentManager: Formatting amount for Tap Payments compliance")
        print("  - Input amount: \(amount)")
        print("  - Currency: \(currency)")

        // Ensure positive value
        guard amount > 0 else {
            print("  ❌ Amount must be positive, using minimum: 0.100")
            return 0.100
        }

        // Format to exactly 3 decimal places (ISO standard)
        let formattedString = String(format: "%.3f", amount)
        let formattedAmount = Double(formattedString) ?? amount

        print("  - Formatted string: '\(formattedString)'")
        print("  - Formatted amount: \(formattedAmount)")

        // Ensure minimum amount requirement (0.100)
        if formattedAmount < 0.100 {
            print("  ⚠️ Amount \(formattedAmount) below minimum 0.100, adjusting to minimum")
            return 0.100
        }

        // Validate the formatting
        let decimalPlaces = formattedString.components(separatedBy: ".").last?.count ?? 0
        if decimalPlaces != 3 {
            print("  ⚠️ Decimal places: \(decimalPlaces), should be exactly 3")
        } else {
            print("  ✅ Amount correctly formatted with 3 decimal places")
        }

        return formattedAmount
    }

    /// Helper function to extract error information from Error parameter and convert to dictionary format
    private func extractErrorInfo(from error: Error?) -> [String: String]? {
        guard let error = error else { return nil }

        print("TapPaymentManager: Extracting error info from Error parameter")

        // Handle NSError with userInfo
        if let nsError = error as NSError? {
            print("TapPaymentManager: NSError domain: \(nsError.domain), code: \(nsError.code)")
            print("TapPaymentManager: NSError userInfo: \(nsError.userInfo)")

            var errorDict: [String: String] = [:]

            // Extract error code from NSError
            errorDict["code"] = String(nsError.code)

            // Extract description from various possible keys
            if let description = nsError.userInfo[NSLocalizedDescriptionKey] as? String {
                errorDict["description"] = description
            } else if let description = nsError.userInfo["description"] as? String {
                errorDict["description"] = description
            } else if let description = nsError.userInfo["error_description"] as? String {
                errorDict["description"] = description
            } else {
                errorDict["description"] = nsError.localizedDescription
            }

            // Extract error type/category
            if let errorType = nsError.userInfo["error"] as? String {
                errorDict["error"] = errorType
            } else {
                errorDict["error"] = nsError.domain
            }

            // Check for specific Tap SDK error patterns
            if nsError.domain.contains("tap") || nsError.domain.contains("gosell") {
                // This is likely a Tap SDK error
                if let tapErrorCode = nsError.userInfo["tap_error_code"] as? String {
                    errorDict["code"] = tapErrorCode
                }
            }

            print("TapPaymentManager: Extracted error dictionary: \(errorDict)")
            return errorDict.isEmpty ? nil : errorDict
        }

        // Handle other error types by trying to extract information from description
        let errorDescription = error.localizedDescription
        print("TapPaymentManager: Parsing error description: \(errorDescription)")

        // Try to extract error information dynamically from error description
        var extractedInfo: [String: String] = [:]

        // Extract error code using regex patterns
        if let codeMatch = extractErrorCode(from: errorDescription) {
            extractedInfo["code"] = codeMatch
            print("TapPaymentManager: Extracted error code: \(codeMatch)")
        }

        // Extract error type (BACKEND_ERROR, VALIDATION_ERROR, etc.)
        if let errorType = extractErrorType(from: errorDescription) {
            extractedInfo["error"] = errorType
            print("TapPaymentManager: Extracted error type: \(errorType)")
        }

        // Extract description
        if let description = extractErrorDescription(from: errorDescription) {
            extractedInfo["description"] = description
            print("TapPaymentManager: Extracted description: \(description)")
        } else {
            extractedInfo["description"] = errorDescription
        }

        // Return extracted info if we found meaningful data
        if !extractedInfo.isEmpty {
            print("TapPaymentManager: Successfully extracted error info: \(extractedInfo)")
            return extractedInfo
        }

        return nil
    }



    private func startTapCheckout(request: TapPaymentRequest, presentingViewController: UIViewController) {
        // For now, we'll use TapCheckoutView which handles the Session-based approach
        // This method will be simplified since the Session logic is in TapCheckoutView

        print("TapPaymentManager: Starting payment with TapCheckoutView")
        print("  - Amount: \(request.amount)")
        print("  - Currency: \(request.currency)")
        print("  - Customer: \(request.customerEmail)")

        // The actual Session implementation is handled by TapCheckoutView
        // This method now just validates the request and delegates to TapCheckoutView

        let formattedAmount = formatAmountForTapPayments(request.amount, currency: request.currency)
        if formattedAmount <= 0 {
            handlePaymentResult(.failure(error: .configurationError("Invalid amount - Amount must be greater than 0")))
            return
        }

        // TapCheckoutView will handle the Session creation and management
        print("TapPaymentManager: Payment request validated, delegating to TapCheckoutView")
    }

    private func handlePaymentResult(_ result: TapPaymentResult) {
        isLoading = false

        switch result {
        case .failure(let error):
            lastError = error
        default:
            lastError = nil
        }

        currentContinuation?.resume(returning: result)
        currentContinuation = nil
    }
}


