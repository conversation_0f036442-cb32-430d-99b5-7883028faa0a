//
//  UIViewController+Extensions.swift
//  Wasfa
//
//  Created by Augment Agent on 29/05/2025.
//

import UIKit

extension UIViewController {
    
    /// Find the top most view controller in the hierarchy
    func topMostViewController() -> UIViewController {
        if let presentedViewController = presentedViewController {
            return presentedViewController.topMostViewController()
        }
        
        if let navigationController = self as? UINavigationController {
            return navigationController.visibleViewController?.topMostViewController() ?? self
        }
        
        if let tabBarController = self as? UITabBarController {
            return tabBarController.selectedViewController?.topMostViewController() ?? self
        }
        
        return self
    }
}
