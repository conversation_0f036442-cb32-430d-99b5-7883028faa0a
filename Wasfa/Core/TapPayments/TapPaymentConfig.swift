//
//  TapPaymentConfig.swift
//  Wasfa
//
//  Created by Augment Agent on 29/05/2025.
//

import Foundation

/// Configuration for Tap Payments SDK
struct TapPaymentConfig {

    // MARK: - Environment
    enum Environment {
        case sandbox
        case production

        var sdkMode: String {
            switch self {
            case .sandbox:
                return "sandbox"
            case .production:
                return "production"
            }
        }
    }

    // MARK: - API Keys Structure
    struct APIKeys {
        let sandbox: String
        let production: String
    }

    // MARK: - Configuration Properties
    static let shared = TapPaymentConfig()

    /// Current environment - should be set based on build configuration
    var currentEnvironment: Environment {
        #if DEBUG
        return .sandbox
        #else
        return .production
        #endif
    }

    /// API Keys - These should be loaded from secure storage or environment variables
    /// DO NOT commit actual API keys to version control
    private let apiKeys = APIKeys(
        sandbox: Bundle.main.infoDictionary?["TapSandboxKey"] as? String ?? "",
        production: Bundle.main.infoDictionary?["TapProductionKey"] as? String ?? ""
    )

    /// Public Keys for client-side operations
    private let publicKeys = (
        sandbox: Bundle.main.infoDictionary?["TapSandboxPublicKey"] as? String ?? "",
        production: Bundle.main.infoDictionary?["TapProductionPublicKey"] as? String ?? ""
    )

    /// Tap Merchant ID - should be provided by Tap integration team
    /// IMPORTANT: M370 might not be correct for your account - get the correct one from Tap Payments
    let tapMerchantID: String = Bundle.main.infoDictionary?["TapMerchantID"] as? String ?? ""

    // MARK: - Public Methods

    /// Get current API key based on environment
    func getCurrentAPIKey() -> String {
        switch currentEnvironment {
        case .sandbox:
            return apiKeys.sandbox
        case .production:
            return apiKeys.production
        }
    }

    /// Get sandbox API key
    func getSandboxKey() -> String {
        return apiKeys.sandbox
    }

    /// Get production API key
    func getProductionKey() -> String {
        return apiKeys.production
    }

    /// Get current public key based on environment
    func getCurrentPublicKey() -> String {
        switch currentEnvironment {
        case .sandbox:
            return publicKeys.sandbox
        case .production:
            return publicKeys.production
        }
    }

    /// Get sandbox public key
    func getSandboxPublicKey() -> String {
        return publicKeys.sandbox
    }

    /// Get production public key
    func getProductionPublicKey() -> String {
        return publicKeys.production
    }

    /// Validate if configuration is complete
    func isConfigurationValid() -> Bool {
        return !apiKeys.sandbox.isEmpty &&
               !apiKeys.production.isEmpty &&
               !tapMerchantID.isEmpty
    }

    /// Get configuration errors
    func getConfigurationErrors() -> [String] {
        var errors: [String] = []

        if apiKeys.sandbox.isEmpty {
            errors.append("Sandbox API key is missing")
        }

        if apiKeys.production.isEmpty {
            errors.append("Production API key is missing")
        }

        if tapMerchantID.isEmpty {
            errors.append("Tap Merchant ID is missing")
        }

        return errors
    }

    /// Debug function to check environment variable access
    func debugEnvironmentVariables() {
        print("🔍 TapPaymentConfig: Checking environment variables...")

        // Check if environment variables are available
        let environment = ProcessInfo.processInfo.environment

        print("📊 Environment Variables Status:")
        print("  - TAP_SANDBOX_KEY: \(environment["TAP_SANDBOX_KEY"] != nil ? "✅ Found" : "❌ Not found")")
        print("  - TAP_PRODUCTION_KEY: \(environment["TAP_PRODUCTION_KEY"] != nil ? "✅ Found" : "❌ Not found")")
        print("  - TAP_MERCHANT_ID: \(environment["TAP_MERCHANT_ID"] != nil ? "✅ Found" : "❌ Not found")")
        print("  - TAP_SANDBOX_PUBLIC_KEY: \(environment["TAP_SANDBOX_PUBLIC_KEY"] != nil ? "✅ Found" : "❌ Not found")")
        print("  - TAP_PRODUCTION_PUBLIC_KEY: \(environment["TAP_PRODUCTION_PUBLIC_KEY"] != nil ? "✅ Found" : "❌ Not found")")

        // Show current values (masked for security)
        print("🔑 Current API Key Sources:")
        print("  - Sandbox Key: \(getSandboxKey().prefix(10))... (from \(environment["TAP_SANDBOX_KEY"] != nil ? "environment" : "hardcoded"))")
        print("  - Production Key: \(getProductionKey().prefix(10))... (from \(environment["TAP_PRODUCTION_KEY"] != nil ? "environment" : "hardcoded"))")
        print("  - Merchant ID: \(tapMerchantID) (from \(environment["TAP_MERCHANT_ID"] != nil ? "environment" : "hardcoded"))")

        // Debug bundle ID detection (for Tap SDK verification)
        print("📱 Bundle ID Detection:")
        print("  - Bundle Identifier: \(Bundle.main.bundleIdentifier ?? "nil")")
        print("  - App Name: \(Bundle.main.infoDictionary?["CFBundleName"] as? String ?? "nil")")
        print("  - App Version: \(Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "nil")")
        print("  - Bundle Version: \(Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "nil")")

        // Check total environment variable count
        print("📈 Total environment variables available: \(environment.count)")

        // List some common environment variables for debugging
        let commonVars = ["PATH", "HOME", "USER", "SIMULATOR_DEVICE_NAME", "SIMULATOR_VERSION_INFO"]
        print("🔧 Common environment variables:")
        for varName in commonVars {
            if let value = environment[varName] {
                print("  - \(varName): \(value.prefix(50))...")
            }
        }
    }

    // MARK: - Private Init
    private init() {}
}

// MARK: - Environment Variables Setup Instructions
/*
 To set up environment variables for API keys:

 1. In Xcode, go to Product > Scheme > Edit Scheme
 2. Select "Run" on the left sidebar
 3. Go to "Arguments" tab
 4. Under "Environment Variables" add:
    - TAP_SANDBOX_KEY: your_sandbox_key_here
    - TAP_PRODUCTION_KEY: your_production_key_here
    - TAP_MERCHANT_ID: your_merchant_id_here

 Alternatively, you can use a .env file or secure keychain storage.

 Note: Apple Pay functionality has been removed from this integration.
 */
