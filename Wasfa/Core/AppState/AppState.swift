//
//  AppState.swift
//  Wasfa
//
//  Created by Apple on 20/09/2024.
//

import SwiftUI

enum InitialScreen {
    case splash, dashboard, authentication
}

enum AppLocale: String, CaseIterable, Identifiable {
    case english = "en"
    case arabic = "ar"

    var id: Self {self}

    var title: String {
        switch self {
        case .english:
            "english"
        case .arabic:
            "عربي"
        }
    }

    var locale: Locale {
        switch self {
        case .english:
            Locale(identifier: rawValue)
        case .arabic:
            Locale(identifier: rawValue)
        }
    }

    var direction: LayoutDirection {
        switch self {
        case .english:
            .leftToRight
        case .arabic:
            .rightToLeft
        }
    }
}

final class AppState: ObservableObject {
    @Published var toast: [Toast] = .init()
    @Published var initialScreen: InitialScreen = .splash
    @Published private(set) var selectedTab: Tab = .home
    @Published var showSideMenu: Bool = false
    @Published var rootViewId = UUID()
    @Published private(set) var selectedCategoryID: Int?
    @AppStorage(AppStorageKey.cartCount) private(set) var cartCount: Int = 0
    @AppStorage(AppStorageKey.wishlistCount) private(set) var wishlistCount: Int = 0
    @AppStorage(AppStorageKey.notificationCount) private(set) var notificationCount: Int = 0
    @AppStorage(AppStorageKey.startUpLanguageShown) private(set) var startUpLanguageShown: Bool = false
    @AppStorage(AppStorageKey.appLocale) private(set) var appLocale: AppLocale = .english
    func updateCartQuantity(_ value: Int?) {
        if let value = value {
            cartCount = value
        }
    }

    func updateNotificationCount(_ value: Int?) {
        if let value = value {
            notificationCount = value
        }
    }

    public func updateSelectedTab(_ value: Tab) {
        selectedTab = value
    }

    public func updateSelectedCategoryID(_ value: Int?) {
        selectedCategoryID = value
    }

    func updateWishlistQuantity(_ value: Int?) {
        if let value = value {
            wishlistCount = value
        }
    }

    @MainActor
    func updateAppLocale(to value: AppLocale,  reset: Bool = false) {
        AppState.language = value.rawValue
        withAnimation(.bouncy) {  self.appLocale = value}
        updateStartUpLanguageShown(true)
        if reset { Utilities.enQueue { self.restRootViewID() }}
    }
    
    

    func updateStartUpLanguageShown(_ value: Bool) {
        startUpLanguageShown = value
    }

    static func updateLoggedInState(_ value: Bool) { isLoggedIn = value }

    func onLogin(message: String, isResetRootID: Bool = true) {
        if isResetRootID { restRootViewID() }
        AppState.updateLoggedInState(true)
        showToast(Toast(type: .success, title: "Success", message: message))
    }

    func onLogout() {
        restRootViewID()
        AppState.user = nil
        AppState.updateLoggedInState(false)
        showToast(Toast(type: .success, title: "Success", message: "Successfully logged out"))
    }

    func showToast(_ value: Toast) {
        toast.removeAll()
        toast.append(value)
    }

    func restRootViewID() {
        // reseting rootViewId variable to a new UUID
        rootViewId = .init()
    }
}

extension AppState {
    static var user: ProfileDetailsModel? {
        get { UserDefaults.user }
        set { UserDefaults.user = newValue }
    }

    static var isLoggedIn: Bool {
        get { UserDefaults.isLoggedIn }
        set { UserDefaults.isLoggedIn = newValue }
    }

    static var language: String {
        get { UserDefaults.language }
        set { UserDefaults.language = newValue }
    }

    static var deviceID: String {
        UIDevice.current.identifierForVendor?.uuidString ?? UUID().uuidString
    }

    static var fcmToken: String {
        get { UserDefaults.fcmToken }
        set { UserDefaults.fcmToken = newValue }
    }
}

private extension UserDefaults {
    static var user: ProfileDetailsModel? {
        get { standard.codableValue(forKey: #function) }
        set { standard.setCodable(newValue, forKey: #function) }
    }

    static var isLoggedIn: Bool {
        get { standard.value(forKey: #function) as? Bool ?? false }
        set { standard.set(newValue, forKey: #function) }
    }

    static var language: String {
        get { standard.value(forKey: #function) as? String ?? Locale.current.language.languageCode?.identifier ?? "" }
        set { standard.set(newValue, forKey: #function) }
    }

    static var fcmToken: String {
        get { standard.value(forKey: #function) as? String ?? "" }
        set { standard.set(newValue, forKey: #function) }
    }
}

enum AppStorageKey {
    static let cartCount = "cartQuantity"
    static let wishlistCount = "wishlistQuantity"
    static let notificationCount = "wishlistQuantity"
    static let startUpLanguageShown = "showStartUpLanguage"
    static let appLocale = "appLocale"
}

extension UserDefaults {
    func setCodable<T: Codable>(_ value: T?, forKey key: String) {
        guard let value = value else {
            removeObject(forKey: key)
            return
        }

        if let data = try? JSONEncoder().encode(value) {
            set(data, forKey: key)
        }
    }

    func codableValue<T: Codable>(forKey key: String) -> T? {
        guard let data = data(forKey: key) else { return nil }
        return try? JSONDecoder().decode(T.self, from: data)
    }
}
