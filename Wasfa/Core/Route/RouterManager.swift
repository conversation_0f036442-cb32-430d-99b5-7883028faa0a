//
//  RouterManager.swift
//  Wasfa
//
//  Created by Apple on 20/09/2024.
//

import SwiftUI

// Enum Types for distinguishing general and tab routes
enum RoutesType: String, Hashable {
    case generalRoute, homeRoute, categoryRoute, wishlistRoute, myAccountRoute
}

@Observable
final class RouterManager {
    // For out side Tab routes navigations
    var generalRouteList = [Route]()
    // For inside Tab routes navigations
    var homeRouteList = [Route]()
    var categoryRouteList = [Route]()
    var wishlistRouteList = [Route]()
    var myAccountRouteList = [Route]()

    init() {}

    // For pushing new screen to the given routes
    func push(to screen: Route, appState: AppState? = nil, where routesType: RoutesType = .generalRoute, forcefully: Bool = true) {
        var routesType: RoutesType = routesType
        if let appState = appState { routesType = mapRouterWithTab(appState: appState) }
        Utilities.enQueue { [self] in
            switch routesType {
            case .generalRoute:
                if !forcefully {
                    guard !generalRouteList.contains(screen) else { return printRouteException() }
                }
                generalRouteList.append(screen)
            case .homeRoute:
                if !forcefully {
                    guard !homeRouteList.contains(screen) else { return printRouteException() }
                }
                homeRouteList.append(screen)
            case .categoryRoute:
                if !forcefully {
                    guard !categoryRouteList.contains(screen) else { return printRouteException() }
                }
                categoryRouteList.append(screen)
            case .wishlistRoute:
                if !forcefully {
                    guard !wishlistRouteList.contains(screen) else { return printRouteException() }
                }
                wishlistRouteList.append(screen)
            case .myAccountRoute:
                if !forcefully {
                    guard !myAccountRouteList.contains(screen) else { return printRouteException() }
                }
                myAccountRouteList.append(screen)
            }

            func printRouteException() { print("Route already exist contain") }
        }
    }

    // For navigate back to previous screen from the given routes
    func goBack(appState: AppState? = nil, where routesType: RoutesType = .generalRoute) {
        var routesType: RoutesType = routesType
        if let appState = appState { routesType = mapRouterWithTab(appState: appState) }
        switch routesType {
        case .generalRoute:
            _ = generalRouteList.popLast()
        case .homeRoute:
            _ = homeRouteList.popLast()
        case .categoryRoute:
            _ = categoryRouteList.popLast()
        case .wishlistRoute:
            _ = wishlistRouteList.popLast()
        case .myAccountRoute:
            _ = myAccountRouteList.popLast()
        }
    }

    // For to replace whole screens from given route with new screens array
    func replace(stack: [Route], where routesType: RoutesType = .generalRoute) {
        switch routesType {
        case .generalRoute:
            generalRouteList = stack
        case .homeRoute:
            homeRouteList = stack
        case .categoryRoute:
            categoryRouteList = stack
        case .wishlistRoute:
            wishlistRouteList = stack
        case .myAccountRoute:
            myAccountRouteList = stack
        }
    }

    // For pop back to a specific screen from given route
    func popToRout(to screen: String, where routesType: RoutesType = .generalRoute) {
        switch routesType {
        case .generalRoute:
            guard generalRouteList.contains(where: { $0.rawValue == screen }) else { return printRouteException() }
            guard let index = generalRouteList.firstIndex(where: { $0.rawValue == screen }) else { return }
            let itemIndex = index + 1
            generalRouteList.removeLast(generalRouteList.count - itemIndex)
        case .homeRoute:
            guard homeRouteList.contains(where: { $0.rawValue == screen }) else { return printRouteException() }
            guard let index = homeRouteList.firstIndex(where: { $0.rawValue == screen }) else { return }
            let itemIndex = index + 1
            homeRouteList.removeLast(homeRouteList.count - itemIndex)
        case .categoryRoute:
            guard categoryRouteList.contains(where: { $0.rawValue == screen }) else { return printRouteException() }
            guard let index = categoryRouteList.firstIndex(where: { $0.rawValue == screen }) else { return }
            let itemIndex = index + 1
            categoryRouteList.removeLast(categoryRouteList.count - itemIndex)
        case .wishlistRoute:
            guard wishlistRouteList.contains(where: { $0.rawValue == screen }) else { return printRouteException() }
            guard let index = wishlistRouteList.firstIndex(where: { $0.rawValue == screen }) else { return }
            let itemIndex = index + 1
            wishlistRouteList.removeLast(wishlistRouteList.count - itemIndex)
        case .myAccountRoute:
            guard myAccountRouteList.contains(where: { $0.rawValue == screen }) else { return printRouteException() }
            guard let index = myAccountRouteList.firstIndex(where: { $0.rawValue == screen }) else { return }
            let itemIndex = index + 1
            myAccountRouteList.removeLast(myAccountRouteList.count - itemIndex)
        }

        func printRouteException() { print("Route not contain") }
    }

//
//    // For pop back to a specific screen from given route
//    func popToRout(to screen: Route, where routesType: RoutesType = .generalRoute) {
//        switch routesType {
//        case .generalRoute:
//            guard generalRouteList.contains(screen) else { return printRouteException() }
//            for (index, item) in generalRouteList.reversed().enumerated() {
//                let reverseIndex: Int = generalRouteList.count - index - 1
//                if item == screen { return }
//                generalRouteList.remove(at: reverseIndex)
//            }
//        case .homeRoute:
//            guard homeRouteList.contains(screen) else { return printRouteException() }
//            for (index, item) in homeRouteList.reversed().enumerated() {
//                let reverseIndex: Int = homeRouteList.count - index - 1
//                if item == screen { return }
//                homeRouteList.remove(at: reverseIndex)
//            }
//        case .categoryRoute:
//            guard categoryRouteList.contains(screen) else { return printRouteException() }
//            for (index, item) in categoryRouteList.reversed().enumerated() {
//                let reverseIndex: Int = categoryRouteList.count - index - 1
//                if item == screen { return }
//                categoryRouteList.remove(at: reverseIndex)
//            }
//        case .wishlistARoute:
//            guard wishlistARouteList.contains(screen) else { return printRouteException() }
//            for (index, item) in wishlistARouteList.reversed().enumerated() {
//                let reverseIndex: Int = wishlistARouteList.count - index - 1
//                if item == screen { return }
//                wishlistARouteList.remove(at: reverseIndex)
//            }
//        case .myAccountRoute:
//            guard myAccountRouteList.contains(screen) else { return printRouteException() }
//            for (index, item) in myAccountRouteList.reversed().enumerated() {
//                let reverseIndex: Int = myAccountRouteList.count - index - 1
//                if item == screen { return }
//                myAccountRouteList.remove(at: reverseIndex)
//            }
//
//        }
//
//        func printRouteException() { print("Route not contain") }
//    }

    func popToAllRoot() {
        generalRouteList.removeAll()
        homeRouteList.removeAll()
        categoryRouteList.removeAll()
        wishlistRouteList.removeAll()
        myAccountRouteList.removeAll()
    }

    func popToRootTabs() {
        homeRouteList.removeAll()
        categoryRouteList.removeAll()
        wishlistRouteList.removeAll()
        myAccountRouteList.removeAll()
    }

    // For pop all the way back to its root view from given route
    func popToRoot(where routesType: RoutesType = .generalRoute) {
        switch routesType {
        case .generalRoute:
            generalRouteList = []
        case .homeRoute:
            homeRouteList = []
        case .categoryRoute:
            categoryRouteList = []
        case .wishlistRoute:
            wishlistRouteList = []
        case .myAccountRoute:
            myAccountRouteList = []
        }
    }

    func checkIfRouteContain(route: Route) -> Bool {
        homeRouteList.contains(route) || categoryRouteList.contains(route) || wishlistRouteList.contains(route) || myAccountRouteList.contains(route)
    }

    // For mapping two enums: Tab -> RoutesType
    func mapTabTypeToRoutesType(from tab: Tab) -> RoutesType {
        switch tab {
        case .home:
            return RoutesType.homeRoute
        case .category:
            return RoutesType.categoryRoute
        case .wishlist:
            return RoutesType.wishlistRoute
        case .myAccount:
            return RoutesType.myAccountRoute
        }
    }

    func mapRouterWithTab(appState: AppState) -> RoutesType {
        switch appState.selectedTab {
        case .home:
            .homeRoute
        case .category:
            .categoryRoute
        case .wishlist:
            .wishlistRoute
        case .myAccount:
            .myAccountRoute
        }
    }
}

struct EnvironmentModifier: ViewModifier {
    @StateObject private var appState = AppState()
    @State private var routerManager = RouterManager()
    func body(content: Content) -> some View {
        content
            .environment(\.colorScheme, .light)
            .environment(\.routerManager, routerManager)
            .environmentObject(appState)
            .environment(\.locale, appState.appLocale.locale)
            .environment(\.layoutDirection, appState.appLocale.direction)
    }
}

extension View {
    func attachAllEnvironmentObjects() -> some View {
        modifier(EnvironmentModifier())
    }
}

struct AppStateKey: EnvironmentKey {
    // Default value for the environment key
    static let defaultValue: AppState = .init()
}

struct RouterManagerKey: EnvironmentKey {
    // Default value for the environment key
    static let defaultValue: RouterManager = .init()
}

extension EnvironmentValues {
    var appState: AppState {
        get { self[AppStateKey.self] }
        set { self[AppStateKey.self] = newValue }
    }

    var routerManager: RouterManager {
        get { self[RouterManagerKey.self] }
        set { self[RouterManagerKey.self] = newValue }
    }
}
