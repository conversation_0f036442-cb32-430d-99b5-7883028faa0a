//
//  Route.swift
//  Wasfa
//
//  Created by Apple on 20/09/2024.
//

import SwiftUI

class CallBack<T: RoutableProtocol>: RoutableProtocol {
    static func == (lhs: CallBack, rhs: CallBack) -> Bool {
        ObjectIdentifier(lhs) == ObjectIdentifier(rhs)
    }

    func hash(into hasher: inout Hasher) {
        hasher.combine(ObjectIdentifier(self))
    }

    let onBack: TypeCallback<T>
    init(onBack: @escaping TypeCallback<T>) {
        self.onBack = onBack
    }
}

enum Route: Equatable, Identifiable, Hashable {
    case splash,
         notification,
         productDetails(id: Int),
         cart(Bool = false),
         checkout(model: CartModel),
         productListing(reuestModel: ProductListingRequest, isSearchFocused: Bool = false, categories: [CategoryModel.Category] = [], subCategories: [CategoryModel.Category] = [], superSubCategories: [CategoryModel.Category] = []),
         brandListing,
         accountSettings,
         orderHistory,
         orderInfo(Int),
         trackOrder(Int),
         orderSuccess,
         orderFailure(message: String),
         signIn,
         otpVerification(String),
         contactUs,
         aboutUs,
         faq,
         termsAndConditions,
         privacyPolicy,
         wishlist,
         deliveryAddress(onSelectFromCheckout: CallBack<AddressModel>? = nil),
         rxListing,
         rxDetails(id: Int)

    var id: String { rawValue }

    var rawValue: String {
        switch self {
        case .splash: "splash"
        case .notification: "notification"
        case .productDetails: "productDetails"
        case .cart: "cart"
        case .checkout: "checkout"
        case .productListing: "productListing"
        case .brandListing: "brandListing"
        case .accountSettings: "accountSettings"
        case .orderHistory: "orderHistory"
        case .orderInfo: "orderInfo"
        case .trackOrder: "trackOrder"
        case .orderSuccess: "orderSuccess"
        case .orderFailure: "orderFailure"
        case .signIn: "signIn"
        case .otpVerification: "otpVerification"
        case .contactUs: "contactUs"
        case .aboutUs: "aboutUs"
        case .faq: "faq"
        case .termsAndConditions: "termsAndConditions"
        case .privacyPolicy: "privacyPolicy"
        case .wishlist: "wishlist"
        case .deliveryAddress: "deliveryAddress"
        case .rxListing: "rxListing"
        case .rxDetails: "rxDetails"
        }
    }
}

extension Route: View {
    var body: some View {
        switch self {
        case .splash:
            Text("Splash")
        case .notification:
            NotificationView()
        case let .productDetails(id):
            ProductDetailView(productID: id)
        case let .cart(isRx):
            CartView(isRx: isRx)
        case let .checkout(model):
            CheckoutView(cartModel: model)
        case let .productListing(model, isSearchFocused, categories, subCategories, superSubCategories):
            ProductListingTabContainerView(productListingRequest: model, isSearchFocused: isSearchFocused, categories: categories, subCategories: subCategories, superSubCategories: superSubCategories)
        case .brandListing:
            BrandListingTabContainerView()
        case .accountSettings:
            AccountSettingsView()
        case .orderHistory:
            OrderHistoryTabContainerView()
        case let .orderInfo(id):
            OrderinfoView(orderID: id)
        case let .trackOrder(id):
            TrackOrderView(orderID: id)
        case .orderSuccess:
            OrderSuccessView()
        case let .orderFailure(message: message):
            OrderFailureView(message: message)
        case .signIn:
            SignInView()
        case let .otpVerification(number):
            OTPVerificationView(phone: number)
        case .contactUs:
            ContactUsView()
        case .aboutUs:
            AboutUsView()
        case .faq:
            FAQView()
        case .termsAndConditions:
            TermsAndConditionsView()
        case .privacyPolicy:
            PrivacyPolicyView()
        case .wishlist:
            WishlistView()
        case let .deliveryAddress(callback):
            DeliveryAddressView(onSelectFromCheckout: callback?.onBack)
        case .rxListing:
            RxHistoryView()
        case let .rxDetails(id):
            RxDetailsView(rxID: id)
        }
    }
}
