//
//  TargetType.swift
//  Wasfa
//
//  Created by Apple on 26/10/2024.
//

import Alamofire
import Foundation

protocol TargetType {
    var baseURL: String { get }

    var path: String { get }

    var method: HTTPMethod { get }

    var task: NetworkTask { get }

    var headers: [String: String]? { get }
}

extension TargetType {
    var headerProgress: [String: String] {
        var data = ["Content-type": "application/json",
                    "X-Requested-With": "XMLHttpRequest"]
        data["App-Language"] = AppState.language
        if let token = UserDefaultsSecure.sharedInstance.getGeneratedToken() {
            data["Authorization"] = "Bearer \(token)"
            print(token)
        }

        return data
    }

    var baseURL: String { AppConstants.Server.baseURL + AppConstants.Server.apiPath }
    var headers: [String: String]? { headerProgress }
}

enum NetworkTask {
    case requestPlain
    case requestParameters(parameters: [String: Any], encoding: ParameterEncoding = JSONEncoding.default)
}
