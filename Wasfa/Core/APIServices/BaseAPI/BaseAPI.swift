//
//  BaseAPI.swift
//  Wasfa
//
//  Created by Apple on 26/10/2024.
//

import Alamofire
import Foundation
import SwiftyJSON

// import UIKit

class BaseAPI<T: TargetType> {
    let imageKey = "image"

    func fetchData<M: Decodable>(target: T, responseClass: M.Type, completionHandler: @escaping (Result<M, AFError>) -> Void) {
        let method = Alamofire.HTTPMethod(rawValue: target.method.rawValue)
        let headers = Alamofire.HTTPHeaders(target.headers ?? [:])
        let parameters = buildParams(task: target.task)
        let apiURL = target.baseURL + target.path
        let params = parameters.0.isEmpty ? nil : parameters.0
        let encoding = parameters.1
        let imageData = params?[imageKey] as? Data
        print(params as Any)
        if let selectedImageData = imageData {
            var headers: HTTPHeaders
            headers = ["Content-type": "multipart/form-data",
                       "Content-Disposition": "form-data"]

            AF.upload(multipartFormData: { multipartFormData in

                if let params = params, !params.isEmpty {
                    for (key, value) in params {
                        if key == self.imageKey {
                            multipartFormData.append(selectedImageData, withName: key, fileName: String(Date.currentTimeStamp) + ".jpeg", mimeType: "image/jpeg")
                        } else {
                            multipartFormData.append((value as! String).data(using: String.Encoding.utf8)!, withName: key)
                        }
                    }
                }

            }, to: URL(string: apiURL)!, usingThreshold: UInt64(), method: method, headers: headers)
                .validate(statusCode: 200 ..< 500)
                .responseData(completionHandler: handleTokenResponse)
                .responseDecodable(of: M.self) { response in
                    switch response.result {
                    case let .success(data):
                        completionHandler(.success(data))
                    case let .failure(error):
                        print(error)

                        completionHandler(.failure(error))
                    }
                }
        } else {
            AF.request(apiURL, method: method, parameters: params, encoding: encoding, headers: headers)
                .validate(statusCode: 200 ..< 500)
                .responseData(completionHandler: handleTokenResponse)
                .responseDecodable(of: M.self) { response in
                    switch response.result {
                    case let .success(data):
                        completionHandler(.success(data))
                    case let .failure(error):
                        print(error)
                        completionHandler(.failure(error))
                    }
                }
        }
    }

    // handle if there is a token in response
    func handleTokenResponse(response: AFDataResponse<Data>) {
        switch response.result {
        case .success:
            if let json = response.data {
                
//                print(response.request)
//                json.printFormattedJSON()
        
               
                

                do {
                    let data = try JSON(data: json)
//                    print(data)
                    if let status = data["status"].int, status == 200 {
                        let data = data["data"]
                        let accessToken = data["token"].string
                        if let accessToken = accessToken {
                            UserDefaultsSecure.sharedInstance.setGeneratedTokenStringValue(value: accessToken)
                        }
                    } else {
//                        print(data)
                    }
                } catch {
                    print("JSON Error", error.localizedDescription)
                }
            }
        case let .failure(error):
            response.data?.printFormattedJSON()
            print(error)
        }
        
        NetworkLogger.log(response: response.response, data: response.data, error: response.error)
    }
}

private func buildParams(task: NetworkTask) -> ([String: Any], ParameterEncoding) {
    switch task {
    case .requestPlain:
        return ([:], URLEncoding.default)
    case let .requestParameters(parameters: parameters, encoding: encoding):
        return (parameters, encoding)
    }
}

class NetworkLogger {
    
    static func log(request: URLRequest) {
       print("\n - - - - - - - - - - OUTGOING - - - - - - - - - - \n")
       defer { print("\n - - - - - - - - - -  END - - - - - - - - - - \n") }
       let urlAsString = request.url?.absoluteString ?? ""
       let urlComponents = URLComponents(string: urlAsString)
       let method = request.httpMethod != nil ? "\(request.httpMethod ?? "")" : ""
       let path = "\(urlComponents?.path ?? "")"
       let query = "\(urlComponents?.query ?? "")"
       let host = "\(urlComponents?.host ?? "")"
       var output = """
       \(urlAsString) \n\n
       \(method) \(path)?\(query) HTTP/1.1 \n
       HOST: \(host)\n
       """
       for (key,value) in request.allHTTPHeaderFields ?? [:] {
          output += "\(key): \(value) \n"
       }
       if let body = request.httpBody {
          output += "\n \(String(data: body, encoding: .utf8) ?? "")"
       }
       print(output)
    }
    
    static func log(response: HTTPURLResponse?, data: Data?, error: Error?) {
       print("\n - - - - - - - - - - INCOMMING - - - - - - - - - - \n")
       defer { print("\n - - - - - - - - - -  END - - - - - - - - - - \n") }
       let urlString = response?.url?.absoluteString
       let components = NSURLComponents(string: urlString ?? "")
       let path = "\(components?.path ?? "")"
       let query = "\(components?.query ?? "")"
       var output = ""
       if let urlString = urlString {
          output += "\(urlString)"
          output += "\n\n"
       }
       if let statusCode =  response?.statusCode {
          output += "HTTP \(statusCode) \(path)?\(query)\n"
       }
       if let host = components?.host {
          output += "Host: \(host)\n"
       }
       for (key, value) in response?.allHeaderFields ?? [:] {
          output += "\(key): \(value)\n"
       }
       if let body = data {
//          output += "\n\(String(data: body, encoding: .utf8) ?? "")\n"
           body.printFormattedJSON()
       }
       if error != nil {
          output += "\nError: \(error!.localizedDescription)\n"
       }
       print(output)
    }
}
