//
//  GenerateTokenModel.swift
//  Wasfa
//
//  Created by Apple on 27/10/2024.
//

import Foundation


// the "auth/generatetoken" api body parameters have this fields
struct GenerateTokenRequest: Codable {
    let deviceID: String
    
    enum CodingKeys: String, CodingKey {
        case deviceID = "device_id"
    }
}

// MARK: - GenerateTokenModel
struct GenerateTokenModel: Codable {
    let token: String
}

