//
//  WishlistModel.swift
//  Wasfa
//
//  Created by Apple on 27/10/2024.
//

import Foundation

// MARK: - WishlistModel

// MARK: - Datum

struct WishlistModel: Codable, Identifiable, Equatable, Hashable {
    let id: Int
    let name: String
    let thumbnailImage, discount: String
    let hasDiscount: Bool
    let currentStock: Int
    let unitPrice, strikedPrice: String
    let description: String?
    let tags: [String]
    let rating: Double
    let addCartStatus: Int

    var strokedPrice: String? {
        unitPrice == strikedPrice ? nil : strikedPrice
    }
}

// MARK: - CartModel

// MARK: - CartModel

struct CartModel: Codable, Equatable, Hashable {
    let cartItems: [CartItem]
    let subTotal, tax, shippingCost, discount: String
    var couponDiscount: String?
    var grandTotal: String
    let grandTotalValue: Double
    var isRx: Bool? = false
    
    
    var isRxList:Bool {
         isRx ?? false
    }

    // MARK: - CartItem
    struct CartItem: Codable, Identifiable, Equatable, Hashable {
        let id, productID: Int
        let productName: String
        let productThumbnailImage, sellerLogo: String
        let variation: String
        let price: Double
        let currentStock: Int
        let unitPrice, currencySymbol: String
        let strikedPrice: String?
        let shippingCost, quantity: Int
        
        var strokedPrice: String? {
            unitPrice == strikedPrice ? nil : strikedPrice
        }

        enum CodingKeys: String, CodingKey {
            case id
            case productID = "productId"
            case productName, productThumbnailImage, currentStock, sellerLogo, variation, price, unitPrice, strikedPrice, currencySymbol, shippingCost, quantity
        }
    }
}

// MARK: - AddressModel

struct AddressModel: Codable, Identifiable, Equatable, Hashable {
    var addressId: Int?
    let addressTitle: AddressTitleType
    let firstName, lastName: String
    let email:String?
    let governorateID: Int
    let governorateName: String
    let areaID: Int
    let areaName, block, phone: String
    var setDefault: Int
    let street, building: String
    let floor, appartment, alternatePhone: String?

    var isDefault: Bool {
        setDefault == 1
    }

    var fullName: String {
        "\(firstName) \(lastName)"
    }

//    var fullAddress: String {
//        "\(block), \(street), \(building), \(floor), \(appartment)"
//    }

    var deliveryAddress: String {
        var components: [String] = []
//        if !addressTitle.name.isEmpty { components.append("\(addressTitle.name): ") }
//        if !firstName.isEmpty || !lastName.isEmpty {
//            components.append("\(firstName) \(lastName)".trimmingCharacters(in: .whitespaces))
//        }
//        if !email.isEmpty { components.append("Email: \(email)") }
//        if !phone.isEmpty { components.append("Phone: \(phone)") }
        if !block.isEmpty { components.append("\(block)") }
        if !street.isEmpty { components.append("\(street)") }
        if !building.isEmpty { components.append("\(building)") }
        if let floor = floor, !floor.isEmpty { components.append("\(floor)") }
        if let appartment = appartment, !appartment.isEmpty { components.append("\(appartment)") }

        return components.joined(separator: ", ")
    }

    enum CodingKeys: String, CodingKey {
        case addressId = "id"
        case addressTitle, firstName, lastName, email
        case governorateID = "governorateId"
        case governorateName
        case areaID = "areaId"
        case areaName, block, phone, setDefault, street, building, floor, appartment, alternatePhone
    }

    var id: Int { addressId ?? 0 }
}

// MARK: - OrderHistoryModel

struct OrderHistoryModel: Codable, Identifiable {
    let id: Int
    let code: String
    let address: Address
    let paymentType: String
    let paymentStatus: String
    let deliveryStatusLabel, deliveryStatus, grandTotal, date: String
    let totalProductCount: Int
    let refundButton: Bool
    let refundRequestStatus: String
    let cancelButton: Bool
    let cancelRequestStatus: String
    let productImages: [String]

    var imageList: [ImageItem] {
        productImages.map { ImageItem(imageUrl: $0) }
    }

    // MARK: - Address

    struct Address: Codable {
        let name: String
        let lastName: String?
        let email: String
        let governorate: String?
        let governorateID: Int?
        let area: String?
        let areaID: Int
        let block: String
        let street: String
        let building: String
        let appartment, floor: String?
        let addressTitle: String
        let alternatePhone: String?
        let phone: String

        enum CodingKeys: String, CodingKey {
            case name
            case lastName = "last_name"
            case email, governorate
            case governorateID = "governorate_id"
            case area
            case areaID = "area_id"
            case block, street, building, appartment, floor
            case addressTitle = "address_title"
            case alternatePhone = "alternate_phone"
            case phone
        }
    }
}

struct ImageItem: Identifiable, Hashable {
    let id = UUID()
    let imageUrl: String
}

// MARK: - TermsModel

struct TermsModel: Codable {
    let content: String
}

// MARK: - PrivacyPolicyModel

struct PrivacyPolicyModel: Codable {
    let content: String
}


struct FAQModel: Codable, Identifiable {
    let id: Int
    let quesion, answer: String
}


// MARK: - AboutUsModel

struct AboutUsModel: Codable {
    let title: String
    let content: String?
}

// MARK: - ReturnPolicyModel

struct ReturnPolicyModel: Codable {
    let policyDetails: String
}

// MARK: - ProfileDetailsModel

struct ProfileDetailsModel: Codable {
    let id: Int
    let phone: String
    let name, email, dob, gender: String?
}

// MARK: - CategoryModel

struct CategoryModel: Codable {
    let totalCount, totalPageCount: Int
    let categories: [Category]

    // MARK: - Category

    struct Category: Codable, Identifiable, Equatable, Hashable {
        let id: UUID = .init()
        let categoryID: Int
        let parentID: Int?
        let superParentID: Int?
        let name: String
        let banner: String
        let icon: String
        var children: [Category]?

        var isExpanded: Bool = false

        var level: Int {
            if let parentID = parentID, parentID != 0 {
                if let children = children, !children.isEmpty {
                    return 1
                }
                if let children = children, children.isEmpty {
                    return 2
                }
            }
            return 0
        }

        enum CodingKeys: String, CodingKey {
            case categoryID = "id"
            case name
            case banner
            case icon
            case children
            case parentID = "parent_id"
            case superParentID = "super_parent_id"
        }
    }
}

// MARK: - ContactPageModel

struct ContactPageModel: Codable {
    let contactEmail, contactPhone, contactAddress: String?
    let facebookLink, instagramLink, twitterLink, youtubeLink, linkedinLink: String?
    let map: String?
}

// MARK: - NotificationModel

struct NotificationModel: Codable, Identifiable {
    let id: UUID = .init()
    let title: String
    let message: String
    let date: String
}

// MARK: - LoginModel

struct LoginModel: Codable {
    let userId: Int
    let token: String
}

// MARK: - ProductModel

// struct ProductModel: Codable {
//    let id: Int
//    let name: String
//    let price: Double
//    let thumbnail: String
// }

// MARK: - BrandModel

struct BrandModel: Codable, Equatable, Hashable {
    let totalCount, totalPageCount: Int
    let brands: [Brand]
}

// MARK: - WishlistAddModel

struct WishlistAddModel: Codable {
    let wishListCount: Int
}

// MARK: - CartAddModel

struct CartAddModel: Codable {
    let cartCount: Int
    let productQuantity: Int
}

// MARK: - OrderPlacementModel

struct OrderPlacementModel: Codable {
    let calculableTotal: Double?
    let grandTotalValue: Double?
    let orderID: Int
    let total: String?
    let orderCode: String

    enum CodingKeys: String, CodingKey {
        case calculableTotal, grandTotalValue
        case orderID = "orderId"
        case total, orderCode
    }
}

// MARK: - PaymentResponseModel
struct PaymentResponseModel: Codable {
    let subTotal, tax, shippingCost, discount: String
    let grandTotal: String
    let grandTotalValue: Double
    let orderID, itemCount: Int
    let deliveryAddress, deliveryStatus, paymentStatus, transactionID: String
    let paymentType, deliveryDate: String

    enum CodingKeys: String, CodingKey {
        case subTotal, tax, shippingCost, discount, grandTotal, grandTotalValue
        case orderID = "orderId"
        case itemCount, deliveryAddress, deliveryStatus, paymentStatus
        case transactionID = "transactionId"
        case paymentType, deliveryDate
    }
}




protocol NamedItem: Identifiable, Equatable, Hashable {
    var id: Int { get }
    var name: String { get }
}

// MARK: - GovernorateModel

struct GovernorateModel: Codable, NamedItem {
    let id: Int
    let name: String
    let countryID: Int

    enum CodingKeys: String, CodingKey {
        case id, name
        case countryID = "country_id"
    }
}

// MARK: - AreaModel

struct AreaModel: Codable, NamedItem {
    let id: Int
    let name: String
    let governorateID: Int

    enum CodingKeys: String, CodingKey {
        case id, name
        case governorateID = "governorateId"
    }
}

// MARK: - OrderDetailsModel

struct OrderDetailsModel: Codable {
    let id: Int
    let code: String
    let address: Address
    let paymentType, paymentStatus, deliveryStatus, subTotal: String
    let discount, deliveryCharge, grandTotal, date: String
    var tracking: [Tracking]
    let itemsList: [ItemsList]
    let cancelButton: Bool
    let cancelRequestStatus: String
}

// MARK: - Address

struct Address: Codable {
    let name, lastName: String
    let governorate, area, alternatePhone: String?
    let governorateID: Int
    let areaID: Int
    let block, street, building: String
    let appartment, floor: String?
    let phone, addressTitle, email: String

    enum CodingKeys: String, CodingKey {
        case name
        case lastName = "last_name"
        case governorate
        case governorateID = "governorate_id"
        case area
        case areaID = "area_id"
        case block, street, building, appartment, floor, phone
        case addressTitle = "address_title"
        case alternatePhone = "alternate_phone"
        case email
    }

    var fullAddress: String {
        var addressString: String = .init()
        if let floor = floor, !floor.isEmpty {
            addressString += ", \(floor)"
        }

        if let appartment = appartment, !appartment.isEmpty {
            addressString += ", \(appartment)"
        }
        if !building.isEmpty {
            addressString += ", \(building)"
        }
        if !block.isEmpty {
            addressString += ", \(block)"
        }
        if !street.isEmpty {
            addressString += ", \(street)"
        }

        if let area = area, !area.isEmpty {
            addressString += ", \(area)"
        }

        if let governorate = governorate, !governorate.isEmpty {
            addressString += ", \(governorate)"
        }

        return addressString.removingLeadingComma()
    }
}

extension String {
    func removingLeadingComma() -> String {
        if hasPrefix(",") {
            return String(dropFirst()) // Remove the first character
        }
        return self // Return unchanged if no comma
    }
}

// MARK: - ItemsList

struct ItemsList: Codable, Identifiable {
    let id, productID: Int
    let productName, variation, price: String
    let thumbnailImage: String
    let quantity: Int

    let deliveryStatus, deliveryStatusString: String
    let refundButton: Bool
    let refundRequestStatus: String
    let cancelButton: Bool
    let cancelRequestStatus: String

    enum CodingKeys: String, CodingKey {
        case id
        case productID = "productId"
        case productName, variation, price, thumbnailImage, quantity, deliveryStatus
        case deliveryStatusString = "delivery_status_string"
        case refundButton, refundRequestStatus, cancelButton, cancelRequestStatus
    }
}

// MARK: - Tracking

struct Tracking: Codable {
    let status, formattedCreatedAt: String

    enum CodingKeys: String, CodingKey {
        case status
        case formattedCreatedAt = "formatted_created_at"
    }
}

// MARK: - PromoCodeModel

struct PromoCodeModel: Codable {
    let status: String
    let offerType: String?
    let promotionDiscount, grandTotal: String
    let grandTotalValue: Double
    let message: String
}

// MARK: - DeliveryChargeModel

struct DeliveryChargeModel: Codable {
    let areaId: Int
    let charge: Double
}
