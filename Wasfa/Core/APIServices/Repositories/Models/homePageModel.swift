//
//  homePageModel.swift
//  Wasfa
//
//  Created by Apple on 27/10/2024.
//

import Foundation

// MARK: - HomePageModel

struct HomePageModel: Codable {
    let cartCount: Int
    let bannerList, offerBannerList: [BannerList]
    let topCategories: [TopCategory]
    let brandsList: [Brand]
    let topSellingProducts, latestProducts, offerProducts: [ProductModel]
    let notificationCount: Int
    let wishListCount: Int

    // MARK: - BannerList

    struct BannerList: Codable, Identifiable, Equatable {
        let id: UUID = .init()
        let image: String
        let linkTo: String?
        let text: String?
        let description: String?
        let title: String?
    }

    // MARK: - BrandsList

    struct BrandsList: Codable, Identifiable, Equatable, Hashable {
        let id: Int
        let name: String
        let logo: String
    }

    // MARK: - TopCategory

    struct TopCategory: Codable, Identifiable {
        let id: Int
        let name: String
        let banner: String
        let icon: String
    }
}

// MARK: - Product

struct ProductModel: Codable, Identifiable, Equatable {
    let id: Int
    let name: String
    let thumbnailImage: String
    let hasDiscount: Bool
    let discount, unitPrice, strikedPrice: String
    let description, shortDescription: String?
    let tags: [String]?
    let rating: Double?
    let wishListStatus: Bool
    let currentStock: Int
    let buyOptionStatus: Bool
    let buyOfferText: String
    let cartAddedStatus: Bool
    var cartQty: Int
   
    var strokedPrice: String? {
        unitPrice == strikedPrice ? nil : strikedPrice
    }

    enum Tag: String, Codable, Equatable {
        case empty = ""
        case skincare = "Skincare"
    }
}


enum CartUpdateType: String, Codable {
    case increment, decrement, remove
}
