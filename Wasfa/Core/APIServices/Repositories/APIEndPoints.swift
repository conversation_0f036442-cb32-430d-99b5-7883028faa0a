//
//  APIEndPoints.swift
//  Wasfa
//
//  Created by Apple on 26/10/2024.
//

import Foundation

enum APIEndPoints {
    // HTTP method: GET
    static let homepage: String = "homepage"
    static let wishlist: String = "auth/Wishlist"
    static let carts: String = "cartList"
    static let checkoutPageDetails: String = "checkoutPageDetails"
    static let addressList: String = "addressList"
    static let orderHistory: String = "orderHistory"
    static let removePromoCode: String = "removePromoCode"
    static let termsAndConditions: String = "terms-and-conditions"
    static let privacyPolicy: String = "privacy-policy"
    static let aboutUs: String = "about-us"
    static let returnPolicy: String = "return-policy"
    static let profileDetails: String = "profile/details"
    static let parentCategoryList: String = "parentCategoryList"
    static let logout: String = "auth/logout"
    static let faq: String = "faq"
    static let contactPage: String = "contactPage"
    static let notifications: String = "notifications"

    // HTTP method: POST
    static let generateToken: String = "auth/generatetoken"
    static let saveFcmToken: String = "saveFcmToken"
    static let signup: String = "auth/signup"
    static let login: String = "auth/login"
    static let categoryList: String = "categoryList"
    static let productsList: String = "productsList"
    static let productDetails: String = "productDetails"
    static let brandList: String = "brandList"
    static let addWishlist: String = "auth/addWishlist"
    static let removeWishlist: String = "auth/removeWishlist"
    static let addToCart: String = "addToCart"
    static let changeQuantity: String = "changeQuantity"
    static let removeCart: String = "removeCart"
    static let placeOrder: String = "placeOrder"
    static let paymentResponse: String = "paymentResponse"
    static let governorateList: String = "governorateList"
    static let areaList: String = "areaList"
    static let addAddress: String = "addAddress"
    static let updateAddress: String = "updateAddress"
    static let deleteAddress: String = "deleteAddress"
    static let orderDetails: String = "orderDetails"
    static let applyPromoCode: String = "applyPromocode"
    static let deliveryChargeByAreaId: String = "deliveryChargeByAreaId"
    static let forgotPassword: String = "auth/password/forget_request"
    static let verifyCode: String = "auth/password/verify_code"
    static let resetPassword: String = "auth/password/confirm_reset"
    static let updateProfile: String = "profile/update"
    static let reorder: String = "reorder"
    static let sendOtp: String = "auth/sendOtp"
    static let verifyOtp: String = "auth/verifyOtp"
    static let cancelOrder: String = "cancelOrder"
    static let cancelItem: String = "cancelItem"
    static let sendContactMessage: String = "sendContactMessage"
    static let updateCartByProductId: String = "updateCartByProductId"

    static let RXHistory: String = "RXHistory"
    static let RXDetails: String = "RXDetails"

    static let addReview: String = "addReview"
}
