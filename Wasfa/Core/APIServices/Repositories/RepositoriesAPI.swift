//
//  RepositoriesAPI.swift
//  Wasfa
//
//  Created by Apple on 26/10/2024.
//

import Alamofire
import Foundation

typealias EmptyStringArray = [String]
typealias CommonApiResponse = ApiBaseModel
typealias CommonResult<T: Codable> = Result<CommonApiResponse<T>, AFError>
typealias ApiResult<T: Codable> = Result<T, AFError>
var emptyDictionary: DictionaryType { [:] }

protocol RepositoriesAPIProtocol {
    // HTTP method: GET

    func homepage(parameters: Parameters, completionHandler: @escaping (CommonResult<HomePageModel>) -> Void)
    func wishlist(parameters: Parameters, completionHandler: @escaping (CommonResult<[WishlistModel]>) -> Void)
    
    func checkoutPageDetails(parameters: Parameters, completionHandler: @escaping (CommonResult<CheckoutDetailsModel>) -> Void)
    func addressList(parameters: Parameters, completionHandler: @escaping (CommonResult<[AddressModel]>) -> Void)
    func orderHistory(parameters: Parameters, completionHandler: @escaping (CommonResult<[OrderHistoryModel]>) -> Void)
    func removePromoCode(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func termsAndConditions(parameters: Parameters, completionHandler: @escaping (CommonResult<AboutUsModel>) -> Void)
    func privacyPolicy(parameters: Parameters, completionHandler: @escaping (CommonResult<AboutUsModel>) -> Void)
    func aboutUs(parameters: Parameters, completionHandler: @escaping (CommonResult<AboutUsModel>) -> Void)
    func returnPolicy(parameters: Parameters, completionHandler: @escaping (CommonResult<ReturnPolicyModel>) -> Void)
    func profileDetails(parameters: Parameters, completionHandler: @escaping (CommonResult<[ProfileDetailsModel]>) -> Void)
    func parentCategoryList(parameters: Parameters, completionHandler: @escaping (CommonResult<[CategoryModel.Category]>) -> Void)
    func logout(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func faq(parameters: Parameters, completionHandler: @escaping (CommonResult<[FAQModel]>) -> Void)
    func contactPage(parameters: Parameters, completionHandler: @escaping (CommonResult<ContactPageModel>) -> Void)
    func notifications(parameters: Parameters, completionHandler: @escaping (CommonResult<[NotificationModel]>) -> Void)

    // HTTP method: POST
    func carts(parameters: Parameters, completionHandler: @escaping (CommonResult<CartModel>) -> Void)
    func generateToken(parameters: Parameters, completionHandler: @escaping (CommonResult<GenerateTokenModel>) -> Void)
    func saveFcmToken(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func signup(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func login(parameters: Parameters, completionHandler: @escaping (CommonResult<LoginModel>) -> Void)
    func categoryList(parameters: Parameters, completionHandler: @escaping (CommonResult<CategoryModel>) -> Void)
    func productsList(parameters: Parameters, completionHandler: @escaping (CommonResult<ProductListingModel>) -> Void)
    func productDetails(parameters: Parameters, completionHandler: @escaping (CommonResult<[ProductDetailsModel]>) -> Void)
    func brandList(parameters: Parameters, completionHandler: @escaping (CommonResult<BrandModel>) -> Void)
    func addWishlist(parameters: Parameters, completionHandler: @escaping (CommonResult<WishlistAddModel>) -> Void)
    func removeWishlist(parameters: Parameters, completionHandler: @escaping (CommonResult<WishlistAddModel>) -> Void)
    func addToCart(parameters: Parameters, completionHandler: @escaping (CommonResult<CartAddModel>) -> Void)
    func changeQuantity(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func removeCart(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func placeOrder(parameters: Parameters, completionHandler: @escaping (CommonResult<OrderPlacementModel>) -> Void)
    func paymentResponse(parameters: Parameters, completionHandler: @escaping (CommonResult<PaymentResponseModel>) -> Void)
    func governorateList(parameters: Parameters, completionHandler: @escaping (CommonResult<[GovernorateModel]>) -> Void)
    func areaList(parameters: Parameters, completionHandler: @escaping (CommonResult<[AreaModel]>) -> Void)
    func addAddress(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func updateAddress(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func deleteAddress(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func orderDetails(parameters: Parameters, completionHandler: @escaping (CommonResult<[OrderDetailsModel]>) -> Void)
    func applyPromoCode(parameters: Parameters, completionHandler: @escaping (CommonResult<PromoCodeModel>) -> Void)
    func deliveryChargeByAreaId(parameters: Parameters, completionHandler: @escaping (CommonResult<DeliveryChargeModel>) -> Void)
    func forgotPassword(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func verifyCode(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func resetPassword(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func updateProfile(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func reorder(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func sendOtp(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func verifyOtp(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func cancelOrder(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func cancelItem(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func sendContactMessage(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func updateCartByProductId(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func RXHistory(parameters: Parameters, completionHandler: @escaping (CommonResult<[RxHistoryModel]>) -> Void)
    func RXDetails(parameters: Parameters, completionHandler: @escaping (CommonResult<[RXDetailsModel]>) -> Void)
    func addReview(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
}

import Alamofire
import Foundation

class RepositoriesAPI: BaseAPI<RepositoriesNetworking>, RepositoriesAPIProtocol {
    public static let shared = RepositoriesAPI()
    
    override private init() {
        print("RepositoriesAPI class is initialized")
    }
    
    // MARK: - GET Requests
    
    func homepage(parameters: Parameters, completionHandler: @escaping (CommonResult<HomePageModel>) -> Void) {
        fetchData(target: .homepage, responseClass: CommonApiResponse<HomePageModel>.self, completionHandler: completionHandler)
    }
    
    func wishlist(parameters: Parameters, completionHandler: @escaping (CommonResult<[WishlistModel]>) -> Void) {
        fetchData(target: .wishlist, responseClass: CommonApiResponse<[WishlistModel]>.self, completionHandler: completionHandler)
    }
    
   
    
    func checkoutPageDetails(parameters: Parameters, completionHandler: @escaping (CommonResult<CheckoutDetailsModel>) -> Void) {
        fetchData(target: .checkoutPageDetails, responseClass: CommonApiResponse<CheckoutDetailsModel>.self, completionHandler: completionHandler)
    }
    
    func addressList(parameters: Parameters, completionHandler: @escaping (CommonResult<[AddressModel]>) -> Void) {
        fetchData(target: .addressList, responseClass: CommonApiResponse<[AddressModel]>.self, completionHandler: completionHandler)
    }
    
    func orderHistory(parameters: Parameters, completionHandler: @escaping (CommonResult<[OrderHistoryModel]>) -> Void) {
        fetchData(target: .orderHistory, responseClass: CommonApiResponse<[OrderHistoryModel]>.self, completionHandler: completionHandler)
    }
    
    func removePromoCode(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .removePromoCode, responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    
    func termsAndConditions(parameters: Parameters, completionHandler: @escaping (CommonResult<AboutUsModel>) -> Void) {
        fetchData(target: .termsAndConditions, responseClass: CommonApiResponse<AboutUsModel>.self, completionHandler: completionHandler)
    }
    
    func privacyPolicy(parameters: Parameters, completionHandler: @escaping (CommonResult<AboutUsModel>) -> Void) {
        fetchData(target: .privacyPolicy, responseClass: CommonApiResponse<AboutUsModel>.self, completionHandler: completionHandler)
    }
    
    func aboutUs(parameters: Parameters, completionHandler: @escaping (CommonResult<AboutUsModel>) -> Void) {
        fetchData(target: .aboutUs, responseClass: CommonApiResponse<AboutUsModel>.self, completionHandler: completionHandler)
    }
    
    func returnPolicy(parameters: Parameters, completionHandler: @escaping (CommonResult<ReturnPolicyModel>) -> Void) {
        fetchData(target: .returnPolicy, responseClass: CommonApiResponse<ReturnPolicyModel>.self, completionHandler: completionHandler)
    }
    
    func profileDetails(parameters: Parameters, completionHandler: @escaping (CommonResult<[ProfileDetailsModel]>) -> Void) {
        fetchData(target: .profileDetails, responseClass: CommonApiResponse<[ProfileDetailsModel]>.self, completionHandler: completionHandler)
    }
    
    func parentCategoryList(parameters: Parameters, completionHandler: @escaping (CommonResult<[CategoryModel.Category]>) -> Void) {
        fetchData(target: .parentCategoryList, responseClass: CommonApiResponse<[CategoryModel.Category]>.self, completionHandler: completionHandler)
    }
    
    func logout(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .logout(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    
    func faq(parameters: Parameters, completionHandler: @escaping (CommonResult<[FAQModel]>) -> Void) {
        fetchData(target: .faq, responseClass: CommonApiResponse<[FAQModel]>.self, completionHandler: completionHandler)
    }
    
    func contactPage(parameters: Parameters, completionHandler: @escaping (CommonResult<ContactPageModel>) -> Void) {
        fetchData(target: .contactPage, responseClass: CommonApiResponse<ContactPageModel>.self, completionHandler: completionHandler)
    }
    
    func notifications(parameters: Parameters, completionHandler: @escaping (CommonResult<[NotificationModel]>) -> Void) {
        fetchData(target: .notifications, responseClass: CommonApiResponse<[NotificationModel]>.self, completionHandler: completionHandler)
    }
    
    // MARK: - POST Requests
    func carts(parameters: Parameters, completionHandler: @escaping (CommonResult<CartModel>) -> Void) {
        fetchData(target: .carts(parameters: parameters), responseClass: CommonApiResponse<CartModel>.self, completionHandler: completionHandler)
    }
    
    func generateToken(parameters: Parameters, completionHandler: @escaping (CommonResult<GenerateTokenModel>) -> Void) {
        fetchData(target: .generateToken(parameters: parameters), responseClass: CommonApiResponse<GenerateTokenModel>.self, completionHandler: completionHandler)
    }
    
    func saveFcmToken(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .saveFcmToken(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    
    func signup(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .signup(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    
    func login(parameters: Parameters, completionHandler: @escaping (CommonResult<LoginModel>) -> Void) {
        fetchData(target: .login(parameters: parameters), responseClass: CommonApiResponse<LoginModel>.self, completionHandler: completionHandler)
    }
    
    func categoryList(parameters: Parameters, completionHandler: @escaping (CommonResult<CategoryModel>) -> Void) {
        fetchData(target: .categoryList(parameters: parameters), responseClass: CommonApiResponse<CategoryModel>.self, completionHandler: completionHandler)
    }
    
    func productsList(parameters: Parameters, completionHandler: @escaping (CommonResult<ProductListingModel>) -> Void) {
        fetchData(target: .productsList(parameters: parameters), responseClass: CommonApiResponse<ProductListingModel>.self, completionHandler: completionHandler)
    }
    
    func productDetails(parameters: Parameters, completionHandler: @escaping (CommonResult<[ProductDetailsModel]>) -> Void) {
        fetchData(target: .productDetails(parameters: parameters), responseClass: CommonApiResponse<[ProductDetailsModel]>.self, completionHandler: completionHandler)
    }
    
    func brandList(parameters: Parameters, completionHandler: @escaping (CommonResult<BrandModel>) -> Void) {
        fetchData(target: .brandList(parameters: parameters), responseClass: CommonApiResponse<BrandModel>.self, completionHandler: completionHandler)
    }
    
    func addWishlist(parameters: Parameters, completionHandler: @escaping (CommonResult<WishlistAddModel>) -> Void) {
        fetchData(target: .addWishlist(parameters: parameters), responseClass: CommonApiResponse<WishlistAddModel>.self, completionHandler: completionHandler)
    }
    
    func removeWishlist(parameters: Parameters, completionHandler: @escaping (CommonResult<WishlistAddModel>) -> Void) {
        fetchData(target: .removeWishlist(parameters: parameters), responseClass: CommonApiResponse<WishlistAddModel>.self, completionHandler: completionHandler)
    }
    
    func addToCart(parameters: Parameters, completionHandler: @escaping (CommonResult<CartAddModel>) -> Void) {
        fetchData(target: .addToCart(parameters: parameters), responseClass: CommonApiResponse<CartAddModel>.self, completionHandler: completionHandler)
    }
    
    func changeQuantity(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .changeQuantity(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    
    func removeCart(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .removeCart(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    
    func placeOrder(parameters: Parameters, completionHandler: @escaping (CommonResult<OrderPlacementModel>) -> Void) {
        fetchData(target: .placeOrder(parameters: parameters), responseClass: CommonApiResponse<OrderPlacementModel>.self, completionHandler: completionHandler)
    }
    
    func paymentResponse(parameters: Parameters, completionHandler: @escaping (CommonResult<PaymentResponseModel>) -> Void) {
        fetchData(target: .paymentResponse(parameters: parameters), responseClass: CommonApiResponse<PaymentResponseModel>.self, completionHandler: completionHandler)
    }
    
    func governorateList(parameters: Parameters, completionHandler: @escaping (CommonResult<[GovernorateModel]>) -> Void) {
        fetchData(target: .governorateList(parameters: parameters), responseClass: CommonApiResponse<[GovernorateModel]>.self, completionHandler: completionHandler)
    }
    
    func areaList(parameters: Parameters, completionHandler: @escaping (CommonResult<[AreaModel]>) -> Void) {
        fetchData(target: .areaList(parameters: parameters), responseClass: CommonApiResponse<[AreaModel]>.self, completionHandler: completionHandler)
    }
    
    func addAddress(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .addAddress(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    
    func updateAddress(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .updateAddress(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    
    func deleteAddress(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .deleteAddress(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    
    func orderDetails(parameters: Parameters, completionHandler: @escaping (CommonResult<[OrderDetailsModel]>) -> Void) {
        fetchData(target: .orderDetails(parameters: parameters), responseClass: CommonApiResponse<[OrderDetailsModel]>.self, completionHandler: completionHandler)
    }
    
    func applyPromoCode(parameters: Parameters, completionHandler: @escaping (CommonResult<PromoCodeModel>) -> Void) {
        fetchData(target: .applyPromoCode(parameters: parameters), responseClass: CommonApiResponse<PromoCodeModel>.self, completionHandler: completionHandler)
    }
    
    func deliveryChargeByAreaId(parameters: Parameters, completionHandler: @escaping (CommonResult<DeliveryChargeModel>) -> Void) {
        fetchData(target: .deliveryChargeByAreaId(parameters: parameters), responseClass: CommonApiResponse<DeliveryChargeModel>.self, completionHandler: completionHandler)
    }
    
    func forgotPassword(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .forgotPassword(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    
    func verifyCode(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .verifyCode(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    
    func resetPassword(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .resetPassword(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    
    func updateProfile(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .updateProfile(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    
    func reorder(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .reorder(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    
    func sendOtp(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .sendOtp(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    
    func verifyOtp(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .verifyOtp(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    
    func cancelOrder(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .cancelOrder(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    
    func cancelItem(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .cancelItem(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    
    func sendContactMessage(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .sendContactMessage(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    
    func updateCartByProductId(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .updateCartByProductId(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    
    func RXHistory(parameters: Parameters, completionHandler: @escaping (CommonResult<[RxHistoryModel]>) -> Void) {
        fetchData(target: .RXHistory, responseClass: CommonApiResponse<[RxHistoryModel]>.self, completionHandler: completionHandler)
    }
    
    func RXDetails(parameters: Parameters, completionHandler: @escaping (CommonResult<[RXDetailsModel]>) -> Void) {
        fetchData(target: .RXDetails(parameters: parameters), responseClass: CommonApiResponse<[RXDetailsModel]>.self, completionHandler: completionHandler)
    }
    
    func addReview(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .addReview(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
}
