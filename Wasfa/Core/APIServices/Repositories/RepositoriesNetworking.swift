//
//  RepositoriesNetworking.swift
//  Wasfa
//
//  Created by Apple on 26/10/2024.
//

import Alamofire

typealias Parameter = [String: Any]

enum RepositoriesNetworking {
    // HTTP method: GET
    case homepage
    case wishlist

    case checkoutPageDetails
    case addressList
    case orderHistory
    case removePromoCode
    case termsAndConditions
    case privacyPolicy
    case aboutUs
    case returnPolicy
    case profileDetails
    case parentCategoryList

    case faq
    case contactPage
    case notifications
    case RXHistory
    

    // HTTP method: POST
    case carts(parameters: Parameter)
    case generateToken(parameters: Parameter)
    case saveFcmToken(parameters: Parameter)
    case signup(parameters: Parameter)
    case login(parameters: Parameter)
    case categoryList(parameters: Parameter)
    case productsList(parameters: Parameter)
    case productDetails(parameters: Parameter)
    case brandList(parameters: Parameter)
    case addWishlist(parameters: Parameter)
    case removeWishlist(parameters: Parameter)
    case addToCart(parameters: Parameter)
    case changeQuantity(parameters: Parameter)
    case removeCart(parameters: Parameter)
    case placeOrder(parameters: Parameter)
    case paymentResponse(parameters: Parameter)
    case governorateList(parameters: Parameter)
    case areaList(parameters: Parameter)
    case addAddress(parameters: Parameter)
    case updateAddress(parameters: Parameter)
    case deleteAddress(parameters: Parameter)
    case orderDetails(parameters: Parameter)
    case applyPromoCode(parameters: Parameter)
    case deliveryChargeByAreaId(parameters: Parameter)
    case forgotPassword(parameters: Parameter)
    case verifyCode(parameters: Parameter)
    case resetPassword(parameters: Parameter)
    case updateProfile(parameters: Parameter)
    case reorder(parameters: Parameter)
    case sendOtp(parameters: Parameter)
    case verifyOtp(parameters: Parameter)
    case cancelOrder(parameters: Parameter)
    case cancelItem(parameters: Parameter)
    case sendContactMessage(parameters: Parameter)
    case logout(parameters: Parameter)
    case updateCartByProductId(parameters: Parameter)

    case RXDetails(parameters: Parameter)

    case addReview(parameters: Parameter)
}

extension RepositoriesNetworking: TargetType {
    var path: String {
        switch self {
        // HTTP method: GET
        case .homepage: APIEndPoints.homepage
        case .wishlist: APIEndPoints.wishlist
        case .checkoutPageDetails: APIEndPoints.checkoutPageDetails
        case .addressList: APIEndPoints.addressList
        case .orderHistory: APIEndPoints.orderHistory
        case .removePromoCode: APIEndPoints.removePromoCode
        case .termsAndConditions: APIEndPoints.termsAndConditions
        case .privacyPolicy: APIEndPoints.privacyPolicy
        case .aboutUs: APIEndPoints.aboutUs
        case .returnPolicy: APIEndPoints.returnPolicy
        case .profileDetails: APIEndPoints.profileDetails
        case .parentCategoryList: APIEndPoints.parentCategoryList
        case .logout: APIEndPoints.logout
        case .faq: APIEndPoints.faq
        case .contactPage: APIEndPoints.contactPage
        case .notifications: APIEndPoints.notifications
        case .RXHistory: APIEndPoints.RXHistory
        case let .carts(parameters):  "\(APIEndPoints.carts)?\(urlParameters(from: parameters))" 

        // HTTP method: POST

        case .generateToken: APIEndPoints.generateToken
        case .saveFcmToken: APIEndPoints.saveFcmToken
        case .signup: APIEndPoints.signup
        case .login: APIEndPoints.login
        case .categoryList: APIEndPoints.categoryList
        case .productsList: APIEndPoints.productsList
        case .productDetails: APIEndPoints.productDetails
        case .brandList: APIEndPoints.brandList
        case .addWishlist: APIEndPoints.addWishlist
        case .removeWishlist: APIEndPoints.removeWishlist
        case .addToCart: APIEndPoints.addToCart
        case .changeQuantity: APIEndPoints.changeQuantity
        case .removeCart: APIEndPoints.removeCart
        case .placeOrder: APIEndPoints.placeOrder
        case .paymentResponse: APIEndPoints.paymentResponse
        case .governorateList: APIEndPoints.governorateList
        case .areaList: APIEndPoints.areaList
        case .addAddress: APIEndPoints.addAddress
        case .updateAddress: APIEndPoints.updateAddress
        case .deleteAddress: APIEndPoints.deleteAddress

        case .orderDetails: APIEndPoints.orderDetails
        case .applyPromoCode: APIEndPoints.applyPromoCode
        case .deliveryChargeByAreaId: APIEndPoints.deliveryChargeByAreaId
        case .forgotPassword: APIEndPoints.forgotPassword
        case .verifyCode: APIEndPoints.verifyCode
        case .resetPassword: APIEndPoints.resetPassword
        case .updateProfile: APIEndPoints.updateProfile
        case .reorder: APIEndPoints.reorder
        case .sendOtp: APIEndPoints.sendOtp
        case .verifyOtp: APIEndPoints.verifyOtp
        case .cancelOrder: APIEndPoints.cancelOrder
        case .cancelItem: APIEndPoints.cancelItem
        case .sendContactMessage: APIEndPoints.sendContactMessage
        case .updateCartByProductId: APIEndPoints.updateCartByProductId

        case .RXDetails: APIEndPoints.RXDetails
        case .addReview: APIEndPoints.addReview
        }
    }

    var method: HTTPMethod {
        switch self {
        // HTTP method: GET
        case .homepage,
             .wishlist,

             .checkoutPageDetails,
             .addressList,
             .orderHistory,
             .removePromoCode,
             .termsAndConditions,
             .privacyPolicy,
             .aboutUs,
             .returnPolicy,
             .profileDetails,
             .parentCategoryList,
             .carts,
             .faq,
             .contactPage,
             .notifications,
             .RXHistory:
            .get

        // HTTP method: POST
        case
            .generateToken,
            .saveFcmToken,
            .signup,
            .login,
            .categoryList,
            .productsList,
            .productDetails,
            .brandList,
            .addWishlist,
            .removeWishlist,
            .addToCart,
            .changeQuantity,
            .removeCart,
            .placeOrder,
            .paymentResponse,
            .governorateList,
            .areaList,
            .addAddress,
            .updateAddress,
            .deleteAddress,
            .orderDetails,
            .applyPromoCode,
            .deliveryChargeByAreaId,
            .forgotPassword,
            .verifyCode,
            .resetPassword,
            .updateProfile,
            .reorder,
            .sendOtp,
            .verifyOtp,
            .cancelOrder,
            .cancelItem,
            .logout,
            .sendContactMessage,
            .updateCartByProductId,

            .RXDetails,
            .addReview:
            .post
        }
    }

    var task: NetworkTask {
        switch self {
        // HTTP method: GET
        case .homepage,
             .wishlist,

             .checkoutPageDetails,
             .addressList,
             .orderHistory,
             .removePromoCode,
             .termsAndConditions,
             .privacyPolicy,
             .aboutUs,
             .returnPolicy,
             .profileDetails,
             .parentCategoryList,
             .carts,
             .faq,
             .contactPage,
             .notifications,
             .RXHistory:
            .requestPlain

        // HTTP method: POST with parameters
        case

            let .generateToken(parameters),
            let .saveFcmToken(parameters),
            let .signup(parameters),
            let .login(parameters),
            let .categoryList(parameters),
            let .productsList(parameters),
            let .productDetails(parameters),
            let .brandList(parameters),
            let .addWishlist(parameters),
            let .removeWishlist(parameters),
            let .addToCart(parameters),
            let .changeQuantity(parameters),
            let .removeCart(parameters),
            let .placeOrder(parameters),
            let .paymentResponse(parameters),
            let .governorateList(parameters),
            let .areaList(parameters),
            let .addAddress(parameters),
            let .updateAddress(parameters),
            let .deleteAddress(parameters),
            let .orderDetails(parameters),
            let .applyPromoCode(parameters),
            let .deliveryChargeByAreaId(parameters),
            let .forgotPassword(parameters),
            let .verifyCode(parameters),
            let .resetPassword(parameters),
            let .updateProfile(parameters),
            let .reorder(parameters),
            let .sendOtp(parameters),
            let .verifyOtp(parameters),
            let .cancelOrder(parameters),
            let .cancelItem(parameters),
            let .logout(parameters),
            let .sendContactMessage(parameters),
            let .updateCartByProductId(parameters),

            let .RXDetails(parameters),
            let .addReview(parameters):
            .requestParameters(parameters: parameters)
        }
    }
}



func urlParameters(from parameters: [String: Any]) -> String {
    parameters
        .compactMap { key, value in
            guard let escapedKey = key.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed),
                  let escapedValue = "\(value)".addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed)
            else { return nil }
            return "\(escapedKey)=\(escapedValue)"
        }
        .joined(separator: "&")
}
