import Foundation
import FirebaseMessaging
import FirebaseCore
import SDWebImageSVGCoder
import SDWebImageWebPCoder
import SDWebImage
import SDWebImageSVGKitPlugin
import UIKit


/// AppDelegate responsible for handling app lifecycle and notifications
class AppDelegate: NSObject, UIApplicationDelegate {
    
    // MARK: - App Lifecycle
    
    func application(_ application: UIApplication,
                     didFinishLaunchingWithOptions launchOptions: [UIApplication
                         .LaunchOptionsKey: Any]? = nil) -> Bool
    {
        // Configure app services
        configureFirebase()
        configurePushNotifications(application)
        configureImageHandling()
        
        return true
    }
    
    // MARK: - Configuration Methods
    
    /// Configures Firebase services
    private func configureFirebase() {
        FirebaseApp.configure()
    }
    
    /// Configures push notification services
    private func configurePushNotifications(_ application: UIApplication) {
        UNUserNotificationCenter.current().delegate = self
        
        let authOptions: UNAuthorizationOptions = [.alert, .badge, .sound]
        UNUserNotificationCenter.current().requestAuthorization(options: authOptions) { granted, error in
            if let error = error {
                print("Notification permission error: \(error.localizedDescription)")
                return
            }
            
            print("Notification permission granted: \(granted)")
        }

        // Register for remote notifications
        application.registerForRemoteNotifications()

        // Set messaging delegate for FCM tokens
        Messaging.messaging().delegate = self
    }
    
    /// Configures image handling services
    private func configureImageHandling() {
        // Add WebP/SVG/PDF support
        SDImageCodersManager.shared.addCoder(SDImageWebPCoder.shared)
        SDImageCodersManager.shared.addCoder(SDImageSVGCoder.shared)
        
        // Add default HTTP header
        SDWebImageDownloader.shared.setValue(
            "image/webp,image/apng,image/*,*/*;q=0.8", 
            forHTTPHeaderField: "Accept"
        )
        
        // Configure caching
        configureSdWebImageCache()
    }
    
    /// Configures SDWebImage caching system
    private func configureSdWebImageCache() {
        // Add multiple caches
        let cache = SDImageCache(namespace: "tiny")
        cache.config.maxMemoryCost = 100 * 1024 * 1024 // 100MB memory
        cache.config.maxDiskSize = 50 * 1024 * 1024 // 50MB disk
        
        SDImageCachesManager.shared.addCache(cache)
        SDWebImageManager.defaultImageCache = SDImageCachesManager.shared
        
        // Set default image loader
        SDWebImageManager.defaultImageLoader = SDImageLoadersManager.shared
    }
}

// MARK: - UNUserNotificationCenterDelegate
// extend with firebase messaging delegate methods
extension AppDelegate:  UNUserNotificationCenterDelegate, MessagingDelegate  {
    
    // MARK: - Handle FCM Token Refresh
    func messaging(_ messaging: Messaging, didReceiveRegistrationToken fcmToken: String?) {
        guard let fcmToken = fcmToken else { return }
        print("FCM Token: \(fcmToken)")
        // You can send this FCM token to your server for targeting notifications
        // sendTokenToServer(fcmToken)
        AppState.fcmToken = fcmToken
    }
    
    
    // MARK: - Remote Notification Registration Callbacks
    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        // Pass the device token to Firebase Messaging
        Messaging.messaging().apnsToken = deviceToken
    }
    
    // MARK: - Handle Incoming Notifications (Foreground)
    func userNotificationCenter(_ center: UNUserNotificationCenter,
                                willPresent notification: UNNotification,
                                withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void)
    {
        
        // Notification received while app is in the foreground
           print("📩 Foreground notification: \(notification.request.content.userInfo)")
           NotificationCenter.default.post(name: .notificationAppeared, object: nil, userInfo: notification.request.content.userInfo)
        
        // This method is called when a notification is delivered to a foreground app.
        completionHandler([.badge, .sound, .banner, .list]) // Show alert, play sound, update badge even in foreground
    }
    
    // MARK: - Handle Notification Tap (Background & Terminated)
    func userNotificationCenter(_ center: UNUserNotificationCenter,
                                didReceive response: UNNotificationResponse,
                                withCompletionHandler completionHandler: @escaping () -> Void)
    {
        // This method is called when the user taps on a notification
        let userInfo = response.notification.request.content.userInfo
        handleNotificationTap(userInfo: userInfo) // Handle the notification payload
        completionHandler()
    }
    
    
    func application(_ application: UIApplication,
                     didReceiveRemoteNotification userInfo: [AnyHashable: Any],
                     fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
        print("📥 Silent notification received: \(userInfo)")
        NotificationCenter.default.post(name: .notificationAppeared, object: nil, userInfo: userInfo)
        completionHandler(.newData)
    }
    
    
    // MARK: - Helper Method to Handle Notification Data
    private func handleNotificationTap(userInfo: [AnyHashable: Any]) {
        print("Notification data received: \(userInfo)")

        // Perform actions based on the notification payload.
        // For example, navigate to a specific screen based on notification data.
        // You can use userInfo to extract information from the notification.

        // Example: If your notification contains a screen identifier, you can route to that screen.
        NotificationCenter.default.post(name: .notificationTapped, object: nil, userInfo: userInfo)
    }
    
}

extension Notification.Name {
    static let notificationTapped = Notification.Name("notification.tapped")
    static let notificationAppeared = Notification.Name("notification.appeared")
    static let notificationPermissionGranted = Notification.Name("notification.permission.granted")
}
