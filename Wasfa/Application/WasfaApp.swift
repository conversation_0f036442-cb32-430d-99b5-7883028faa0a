//
//  WasfaApp.swift
//  Wasfa

import SDWebImageSVGKitPlugin
import Swift<PERSON>

@main
struct WasfaApp: App {
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate

    init() {
//        let SVGCoder = SDImageSVGKCoder.shared
//        SDImageCodersManager.shared.addCoder(SVGCoder)

        // Set a fixed PPI to avoid issues with SVG rendering
//        SVGKImage.defaultPPI = 72
    }

    var body: some Scene {
        WindowGroup {
            MainView()
                .attachAllEnvironmentObjects()
                .onLoad {
                    #if DEBUG
                    print(UserDefaults.standard.dictionaryRepresentation())
                    #endif
                }
        }
    }
}
