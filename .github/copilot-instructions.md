> This document defines how GitHub Copilot and contributors are expected to assist in the Wasfa iOS codebase, promoting consistency, safety, and long-term maintainability using modern SwiftUI practices.


# 🛠 Copilot Instructions for SwiftUI iOS Development

## 🔹 Core Principles
- **Single File Modifications** 📝
  - All changes must remain within the original file
  - No external file creation or dependencies
  - Preserve all existing functionality and data
  - Maintain original business logic integrity

## 🔹 Code Organization
- **View Structure** 📐
  ```swift
  struct MainView: View {
      // 1. Properties
      @State private var viewState
      @EnvironmentObject private var viewModel
      
      // 2. Computed Properties
      private var derivedData: String {
          viewModel.userName.uppercased()
      }
      
      // 3. Body Implementation
      var body: some View {
          content
      }
      
      // 4. Helper Views (within same file)
      private struct SubView: View
      
      // 5. Helper Methods
      private func helperMethod()
  }
  ```

- **Component Hierarchy** 🎯
  - Break large views into:
    1. Main container view
    2. Feature-specific subviews
    3. Reusable UI components
    4. Helper methods and extensions

## 🔹 SwiftUI Best Practices
- **State Management** 💾
  ```swift
  // Prefer
  @State private var localState
  @StateObject private var viewModel
  @EnvironmentObject private var appState
  
  // Avoid
  @ObservedObject in view creation
  var mutableState
  ```

- **Performance Optimizations** ⚡️
  - Use `@ViewBuilder` for conditional views
  - Implement `Equatable` for custom views
  - Leverage `LazyVStack` and `LazyHGrid`
  - Apply `task` modifier for async operations

## 🔹 View Modifiers
- **Custom Modifiers** 🎨
  ```swift
  extension View {
      func customModifier() -> some View {
          self.modifier(CustomViewModifier())
      }
  }
  ```
  
  Use custom modifiers when:
  - A style is reused in multiple views
  - You want to separate layout/styling from view logic

## 🔹 Error Handling
- **Structured Error Flow** 🚨
  ```swift
  enum ViewError: LocalizedError {
      case dataFailure
      case networkError
      
      var errorDescription: String? {
          switch self {
              case .dataFailure: "Data loading failed"
              case .networkError: "Network unavailable"
          }
      }
  }
  ```

  // Use this in ViewModels:
  @Published var viewError: ViewError?

  // In SwiftUI View:
  .alert("Oops", isPresented: .constant(viewError != nil)) {
      Button("OK", role: .cancel) {}
  } message: {
      Text(viewError?.localizedDescription ?? "")
  }

## 🔹 Testing Considerations
- **ViewModifier Testing** 🧪
  ```swift
  // Make views testable without external dependencies
  struct ContentView: View {
      let dataProvider: DataProviding // Protocol
      
      var body: some View {
          // Implementation
      }
  }
  ```

#Preview {
    ProfileView(userService: MockUserService())
        .attachAllEnvironmentObjects()
}

## 🔹 Accessibility
- **Essential Practices** 🌐
  ```swift
  Button(action: handleTap) {
      Text("Action")
  }
  .accessibilityLabel("Descriptive label")
  .accessibilityHint("Explains the action")
  .accessibilityAddTraits(.isButton)
  ```

## ✅ Project Goals

- Maintain a clean, scalable, and production-ready SwiftUI architecture.
- Follow Apple's latest Swift & SwiftUI guidelines (Swift 5.10, iOS 17+).
- Prioritize performance, safety, accessibility, and modern design practices.

---

## 🧱 General Coding Standards

- Use **Swift 5.10+** and **iOS 17**+ APIs only.
- Follow the [Swift API Design Guidelines](https://swift.org/documentation/api-design-guidelines/).
- Use **SwiftUI** for all UI components.
- Prefer `struct` over `class` unless reference semantics are required.
- Avoid force unwraps (`!`) unless absolutely safe.
- Use `.localized()` strings when working with user-facing text.
- Prioritize **type safety**, **readability**, and **reusability**.

---

## 🧭 Architecture Guidelines

- Follow **MVVM** or **Composable Architecture (TCA)** when specified.
- ViewModel classes must conform to `ObservableObject`.
- State should be marked with `@Published` and accessed via `@StateObject` or `@ObservedObject`.
- Use `@Environment(\.dismiss)` and `@EnvironmentObject` appropriately.
- Data flow must be unidirectional.

---

## 🖼 SwiftUI View Best Practices

- Views must be small, focused, and reusable.
- Use custom `ViewModifier`s to avoid repeating styles.
- Use `@ViewBuilder` and `some View` for conditional views.
- Apply `accessibilityLabel`, `accessibilityValue`, etc., where appropriate.
- Avoid deeply nested views. Break them into subviews as needed.
- Support **dark mode** and **Dynamic Type**.

---

## 🔁 Navigation & State Management

- Use `NavigationStack` and `NavigationPath` for deep linking.
- Use `@State`, `@Binding`, `@StateObject`, and `@EnvironmentObject` according to state ownership.
- Use `.sheet`, `.popover`, and `.fullScreenCover` with data-driven triggers, not hardcoded booleans.

---

## 🎨 Theming & Modifiers

- Centralize colors, fonts, and spacings in a `Theme` or `DesignSystem` struct.
- Use `Color`, `Font`, and `EdgeInsets` extensions for consistency.
- Avoid hardcoded values like `.padding(12)` in production-level code.

// Define spacing constants in DesignSystem or Theme
enum Spacing {
    static let small: CGFloat = 8
    static let medium: CGFloat = 16
    static let large: CGFloat = 24
}

---

## 🧪 Testing & Previewing

- Add SwiftUI previews using `.previewLayout(.sizeThatFits)` where meaningful.
- Use `.attachAllEnvironmentObjects()` (if defined) in preview environments.
- Add unit tests for ViewModels and logic layers using `XCTest`.

---

## 📡 Networking (if applicable)

- Use `async/await` or Combine for all asynchronous tasks.
- Use `Codable` for parsing.
- Errors must be gracefully handled in the UI and ViewModel.
- Avoid third-party libraries unless explicitly approved.

---

## 🛑 Avoid

- UIKit unless explicitly needed (use `UIViewRepresentable` or `UIViewControllerRepresentable` if required).
- Deprecated or legacy Swift/SwiftUI APIs.
- Repeating style code in multiple views (use modifiers).

---

## 🧱 Component Guidelines

- Components should be isolated and previewable.
- All shared components go into `Shared/Components/`
- Components must:
  - Support both light and dark mode
  - Have meaningful names (e.g., `RoundedCardView`)
  - Avoid business logic (keep it in ViewModel)

## 📌 Commit Guidelines (Optional)

- Use descriptive commit messages:
  - `feat: Add user profile header`
  - `fix: Resolve layout bug on iPad`
  - `refactor: Extract reusable view modifier`

## 🔗 Additional Resources
- [Design System (Figma)](https://www.figma.com/design/6G4ai0Vk12fDeptsIG88Hw/Wasfa-App-Final--Copy-?node-id=1-11848&t=WfkpSpVcYLbZrXGE-1)
<!-- - [Git Flow Strategy](docs/git-strategy.md)
- [Code Review Checklist](docs/review-checklist.md) -->

---

## 🤖 Copilot Behavior Expectations

- Analyze entire file context before suggesting code.
- Do not remove or alter business logic without clear justification.
- Respect file structure and Swift access control (`private`, `internal`, `public`).
- If you encounter ambiguity, comment the issue or suggest an improvement.
- Do not modify logic blocks inside `if`, `guard`, `onAppear`, or `task` unless instructed.
- Avoid suggesting changes inside ViewModel `.sink`, `.map`, or `async/await` blocks unless explicitly requested.

---

## ⏳ Concurrency Patterns

- Use `@MainActor` on ViewModels and UI-facing async functions.
- Use `task(priority:)` on views only for safe, lightweight async tasks.
- Prefer `Task {}` over `.onAppear` for async lifecycle handling.
- Cancel tasks when appropriate using `Task.cancel()` or `.task(id:)` for automatic cancellation.

---

## 🧱 Dependency Injection

- Use protocol-based abstractions for services (e.g., `UserServiceProtocol`, `DataProvider`).
- Inject via initializer for testable views:
  ```swift
  struct ProfileView: View {
      let userService: UserServiceProtocol
  }
  ```
- Avoid hardcoded singletons like `UserDefaults.standard` inside views.

---



## 🖋 Typography & Font Scaling

- Use `.font(.custom("FontName", size:))` with `.dynamicTypeSize(...)` for accessibility.
- Avoid hardcoded `.font(.system(size:))` unless there's a specific design reason.

---

## 🔐 Data Privacy & Secure Storage

- Do not store sensitive data in `UserDefaults`.
- Use Keychain or secure enclave APIs for credentials and tokens.
- Use `@MainActor` for UI-related state mutations to prevent race conditions.

---

## 🧹 Linting & Formatting

- Follow project-defined `.swiftlint.yml` and SwiftFormat rules.
- Use:
  - 2-space indentation
  - Trailing commas
  - Grouped access control (`private`, `internal`, `public`)
- All code must compile and lint cleanly before merge.

---

## ⚠️ Copilot Limitations

- Copilot is assistive — generated code must be reviewed for:
  - Security flaws
  - Performance issues
  - Accessibility compliance
  - Logic or architectural violations

---

## 🧭 Apple-Aligned Folder Structure Guidance

This project follows a folder structure inspired by Apple’s recommended practices from WWDC sample apps such as [Fruta](https://developer.apple.com/documentation/swiftui/fruta_building_a_feature-rich_app_with_swiftui) and [Scrumdinger](https://developer.apple.com/tutorials/app-dev-training/). It emphasizes clarity, modularity, and separation of concerns.

### ✅ Folder Structure Example

- Each feature should contain:
  - Views
  - ViewModels
  - Models
  - Services (if applicable)

---

```
📁 Wasfa/
├── 📁 App/
│   ├── AppDelegate.swift
│   ├── WasfaApp.swift
│   ├── LaunchScreen/
│   │   └── SplashView.swift
│   └── Environment/
│       ├── AppState.swift
│       └── NetworkMonitor.swift

├── 📁 Features/
│   ├── 📁 Home/
│   │   ├── HomeView.swift
│   │   ├── HomeViewModel.swift
│   │   ├── HomeModel.swift
│   │   ├── Components/
│   │   │   └── FeaturedCarouselView.swift
│   │   └── Services/
│   │       └── HomeAPI.swift
│   │
│   ├── 📁 Profile/
│   │   ├── ProfileView.swift
│   │   ├── ProfileViewModel.swift
│   │   ├── ProfileModel.swift
│   │   ├── EditProfileView.swift
│   │   └── Services/
│   │       └── ProfileAPI.swift

├── 📁 Shared/
│   ├── 📁 Components/
│   │   ├── PrimaryButton.swift
│   │   └── ReusableSheetView.swift
│   ├── 📁 Modifiers/
│   │   └── ShadowModifier.swift
│   ├── 📁 Extensions/
│   │   └── View+Extensions.swift
│   ├── 📁 Constants/
│   │   ├── Colors.swift
│   │   ├── Fonts.swift
│   │   └── Strings.swift
│   └── 📁 Utilities/
│       └── DateFormatter+Custom.swift

├── 📁 Networking/
│   ├── APIClient.swift
│   ├── Endpoint.swift
│   └── ErrorHandling.swift

├── 📁 Resources/
│   ├── Assets.xcassets
│   ├── LaunchScreen.storyboard (if UIKit used)
│   └── Localizable.strings

├── 📁 Tests/
│   ├── HomeTests/
│   │   └── HomeViewModelTests.swift
│   └── SharedTests/
│       └── APICacheTests.swift
```

This structure enables:
- Clear separation of features and concerns
- Easy scaling and modularization
- Alignment with Apple's architectural principles for SwiftUI apps

