# Add any directories, files, or patterns you don't want to be tracked by version control

## OS X files
.DS_Store
.DS_Store?
.Trashes
.Spotlight-V100
*.swp

## Xcode build files
DerivedData/
build/

## Xcode private settings
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
# Xcode
#
# gitignore contributors: remember to update Global/Xcode.gitignore, Objective-C.gitignore & Swift.gitignore

## Build generated
build/
DerivedData/

## Various settings
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata/

## Other
*.moved-aside
*.xccheckout
*.xcscmblueprint

##R.Generated
*.generated.swift

## Obj-C/Swift specific
*.hmap
*.ipa
*.dSYM.zip
*.dSYM

## Playgrounds
timeline.xctimeline
playground.xcworkspace

# Swift Package Manager
#
# Add this line if you want to avoid checking in source code from Swift Package Manager dependencies.
# Packages/
# Package.pins
# Package.resolved
.build/

# CocoaPods
#
# We recommend against adding the Pods directory to your .gitignore. However
# you should judge for yourself, the pros and cons are mentioned at:
# https://guides.cocoapods.org/using/using-cocoapods.html#should-i-check-the-pods-directory-into-source-control
#
Pods/

# Carthage
#
# Add this line if you want to avoid checking in source code from Carthage dependencies.
# Carthage/Checkouts

Carthage/Build

# Firebase configuration files (sensitive)
GoogleService-Info.plist
google-services.json

# Environment files
.env
.env.local
.env.production
.env.staging

# API Keys and secrets
secrets.plist
config.plist

# Backup files
*.bak
*.backup
*.orig

# Log files
*.log

# Temporary files
*.tmp
*.temp

# Archive files
*.zip
*.tar.gz
*.rar

# IDEs
.vscode/
.idea/

# Simulator screenshots
simulator_screenshot_*.png

# Added by Claude Task Master
# Logs
logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
# Task files
tasks.json
tasks/

# Tap Payment API Keys (keep these secure!)
TapPaymentConfig.local.swift
.env
.env.local
*.env

# Local configuration files
*Config.local.swift
*Keys.local.swift

# API Keys and Secrets
secrets.plist
config.local.json