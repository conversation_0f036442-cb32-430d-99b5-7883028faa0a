{"name": "xcode build server", "version": "0.2", "bspVersion": "2.0", "languages": ["c", "cpp", "objective-c", "objective-cpp", "swift"], "argv": ["/Users/<USER>/.vscode/extensions/fireplusteam.vscode-ios-0.5.11/xcode-build-server/xcode-build-server"], "workspace": "/Users/<USER>/Desktop/<PERSON>him ikka/Wasfa/Wasfa.xcodeproj/project.xcworkspace", "build_root": "/Users/<USER>/Library/Developer/Xcode/DerivedData/Wasfa-alduybpkrnteixhlczodqggoykrq", "scheme": "Was<PERSON>", "kind": "xcode"}