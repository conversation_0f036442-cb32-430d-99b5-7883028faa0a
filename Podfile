# Uncomment the next line to define a global platform for your project
platform :ios, '17.0'

target 'Wasfa' do
  # Comment the next line if you don't want to use dynamic frameworks
  use_frameworks!

  # Tap Payments SDK
  pod 'goSellSDK'
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      # Ensure deployment target
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '17.0'

      # Exclude i386 and arm64 for simulator to avoid legacy architecture issues
      config.build_settings['EXCLUDED_ARCHS[sdk=iphonesimulator*]'] = 'i386 arm64'

      # Match Flutter podspec simulator settings
      config.build_settings['ONLY_ACTIVE_ARCH'] = 'NO'
    end
  end
end
