// 🔐 Tap Payment Configuration Template
// Copy this file to TapPaymentConfig.local.swift and add your actual API keys
// Add TapPaymentConfig.local.swift to .gitignore to keep your keys secure

import Foundation

/// Local configuration for Tap Payment API keys
/// This file should NOT be committed to version control
struct TapPaymentLocalConfig {

    // MARK: - API Keys
    // Replace these placeholder values with your actual Tap Payment API keys

    static let sandboxKey = "sk_test_YOUR_SANDBOX_KEY_HERE"
    static let productionKey = "sk_live_YOUR_PRODUCTION_KEY_HERE"
    static let merchantID = "YOUR_MERCHANT_ID_HERE"

    // MARK: - Public Keys
    static let sandboxPublicKey = "pk_test_YOUR_SANDBOX_PUBLIC_KEY_HERE"
    static let productionPublicKey = "pk_live_YOUR_PRODUCTION_PUBLIC_KEY_HERE"

    // Note: Apple Pay functionality has been removed from this integration

    // MARK: - Environment Detection
    static var isProduction: Bool {
        #if DEBUG
        return false
        #else
        return true
        #endif
    }

    // MARK: - Current Keys
    static var currentSandboxKey: String {
        // Priority: Environment Variable > Local Config > Fallback
        return ProcessInfo.processInfo.environment["TAP_SANDBOX_KEY"] ?? sandboxKey
    }

    static var currentProductionKey: String {
        return ProcessInfo.processInfo.environment["TAP_PRODUCTION_KEY"] ?? productionKey
    }

    static var currentMerchantID: String {
        return ProcessInfo.processInfo.environment["TAP_MERCHANT_ID"] ?? merchantID
    }
}

// MARK: - Setup Instructions
/*
 🚀 Setup Instructions:

 1. Copy this file to TapPaymentConfig.local.swift
 2. Replace placeholder values with your actual API keys
 3. Add TapPaymentConfig.local.swift to .gitignore
 4. Optionally, set up environment variables in Xcode scheme for additional security

 Environment Variables (Optional but Recommended):
 - TAP_SANDBOX_KEY
 - TAP_PRODUCTION_KEY
 - TAP_MERCHANT_ID
 - TAP_SANDBOX_PUBLIC_KEY
 - TAP_PRODUCTION_PUBLIC_KEY

 The configuration will use environment variables if available,
 otherwise fall back to the local config values.
 */
