# Wasfa - Modern iOS E-commerce App

<div align="center">
  <img src="Wasfa/Assets.xcassets/img_wasfa_logo_1.imageset/img_wasfa_logo_1.png" alt="Wasfa Logo" width="200"/>
  
  [![iOS](https://img.shields.io/badge/iOS-15.0+-blue.svg)](https://developer.apple.com/ios/)
  [![Swift](https://img.shields.io/badge/Swift-5.9-orange.svg)](https://swift.org/)
  [![SwiftUI](https://img.shields.io/badge/SwiftUI-4.0-green.svg)](https://developer.apple.com/xcode/swiftui/)
  [![Xcode](https://img.shields.io/badge/Xcode-15.0+-blue.svg)](https://developer.apple.com/xcode/)
  [![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
</div>

## 📱 Overview

Wasfa is a comprehensive iOS e-commerce application built with SwiftUI, offering a modern and intuitive shopping experience. The app features product browsing, cart management, user authentication, order tracking, and secure payment processing.

## ✨ Features

### 🛍️ Core Shopping Features
- **Product Catalog**: Browse products by categories with advanced filtering and sorting
- **Search & Discovery**: Intelligent search with autocomplete and suggestions
- **Product Details**: Comprehensive product information with image galleries
- **Shopping Cart**: Add, remove, and manage items with quantity controls
- **Wishlist**: Save favorite products for later purchase
- **Reviews & Ratings**: User-generated product reviews and ratings

### 👤 User Management
- **Authentication**: Secure sign-up and sign-in with email/password
- **Profile Management**: Edit personal information and preferences
- **Address Management**: Multiple delivery addresses with easy selection
- **Order History**: Track past orders with detailed information

### 🚚 Order & Payment
- **Checkout Process**: Streamlined multi-step checkout experience
- **Payment Integration**: Secure payment processing with multiple methods
- **Order Tracking**: Real-time order status updates and delivery tracking
- **Delivery Options**: Multiple delivery time slots and methods

### 🔔 Additional Features
- **Push Notifications**: Order updates and promotional notifications
- **Multi-language Support**: Localized content and interface
- **Dark Mode**: Full dark mode support
- **Offline Support**: Basic functionality when offline

## 🏗️ Architecture

### Design Pattern
- **MVVM (Model-View-ViewModel)**: Clean separation of concerns
- **Repository Pattern**: Centralized data management
- **Dependency Injection**: Modular and testable code structure

### Key Components
- **SwiftUI Views**: Modern declarative UI framework
- **Combine Framework**: Reactive programming for data flow
- **Firebase Integration**: Backend services and authentication
- **Core Data**: Local data persistence
- **URLSession**: Network layer for API communication

## 🛠️ Tech Stack

### Frameworks & Libraries
- **SwiftUI**: User interface framework
- **Combine**: Reactive programming
- **Firebase**: Backend services (Auth, Firestore, Storage, Messaging)
- **SDWebImage**: Async image loading and caching
- **FSPagerView**: Carousel and pager views
- **WrappingHStack**: Flexible layout components

### Development Tools
- **Xcode 15.0+**: IDE and development environment
- **Swift Package Manager**: Dependency management
- **Git**: Version control
- **Firebase Console**: Backend management

## 📋 Requirements

- **iOS 15.0+**
- **Xcode 15.0+**
- **Swift 5.9+**
- **macOS 13.0+** (for development)

## 🚀 Getting Started

### 1. Clone the Repository
```bash
git clone https://github.com/BurhanRabbani/Wasfa.git
cd Wasfa
```

### 2. Firebase Configuration
1. Create a new Firebase project at [Firebase Console](https://console.firebase.google.com/)
2. Add an iOS app to your Firebase project
3. Download `GoogleService-Info.plist` from Firebase Console
4. Replace the template file in the project:
   ```bash
   cp path/to/your/GoogleService-Info.plist Wasfa/GoogleService-Info.plist
   ```

### 3. Install Dependencies
Dependencies are managed via Swift Package Manager and are included in the project:
- Open `Wasfa.xcodeproj` in Xcode
- Dependencies will be resolved automatically

### 4. Build and Run
1. Select your target device or simulator
2. Press `Cmd + R` to build and run the project

## 📁 Project Structure

```
Wasfa/
├── Application/           # App lifecycle and configuration
├── Core/                 # Core business logic
│   ├── APIServices/      # Network layer and API calls
│   ├── AppState/         # Global app state management
│   └── Route/            # Navigation and routing
├── Views/                # SwiftUI views organized by feature
│   ├── Dashboard/        # Main dashboard and tabs
│   ├── HomeView/         # Home screen components
│   ├── ProductDetail/    # Product detail screens
│   ├── CartView/         # Shopping cart functionality
│   ├── CheckoutView/     # Checkout process
│   └── ...              # Other feature views
├── Global/               # Shared components and utilities
│   ├── Components/       # Reusable UI components
│   ├── Constants/        # App constants and configurations
│   ├── CustomViews/      # Custom SwiftUI views
│   ├── Extensions/       # Swift extensions
│   └── Utilities/        # Helper functions and utilities
├── Resources/            # App resources
│   └── Fonts/           # Custom fonts
└── Assets.xcassets/     # Images, colors, and other assets
```

## 🔧 Configuration

### Environment Variables
Create your Firebase configuration by copying the template:
```bash
cp GoogleService-Info.plist.template Wasfa/GoogleService-Info.plist
```

Update the following values in your `GoogleService-Info.plist`:
- `API_KEY`: Your Firebase API key
- `PROJECT_ID`: Your Firebase project ID
- `STORAGE_BUCKET`: Your Firebase storage bucket
- `GCM_SENDER_ID`: Your FCM sender ID
- `GOOGLE_APP_ID`: Your Google app ID

## 🧪 Testing

### Running Tests
```bash
# Run unit tests
xcodebuild test -scheme Wasfa -destination 'platform=iOS Simulator,name=iPhone 15'

# Run UI tests
xcodebuild test -scheme WasfaUITests -destination 'platform=iOS Simulator,name=iPhone 15'
```

### Test Coverage
- Unit tests for ViewModels and business logic
- UI tests for critical user flows
- Integration tests for API endpoints

## 🚀 Deployment

### App Store Deployment
1. Update version and build numbers in `Info.plist`
2. Archive the project (`Product > Archive`)
3. Upload to App Store Connect
4. Submit for review

### TestFlight Distribution
1. Archive the project
2. Upload to App Store Connect
3. Add external testers in TestFlight

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

### Development Workflow
1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👥 Team

- **Lead Developer**: [BurhanRabbani](https://github.com/BurhanRabbani)

## 📞 Support

For support and questions:
- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/BurhanRabbani/Wasfa/issues)

## 🙏 Acknowledgments

- Firebase for backend services
- SwiftUI community for inspiration and resources
- Open source contributors for the amazing libraries used

---

<div align="center">
  Made with ❤️ by the Wasfa Team
</div>
