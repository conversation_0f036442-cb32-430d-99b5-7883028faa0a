import SwiftUI

/// This View draws the WrappingHStack content taking into account the passed width, alignment and spacings.
/// Note that the passed LineManager and ContentManager should be reused whenever possible.
struct InternalWrappingHStack: View {
    let width: CGFloat
    let alignment: HorizontalAlignment
    let spacing: WrappingHStack.Spacing
    let lineSpacing: CGFloat
    let firstItemOfEachLine: [Int]
    let contentManager: ContentManager

    init(width: CGFloat, alignment: HorizontalAlignment, spacing: WrappingHStack.Spacing, lineSpacing: CGFloat, contentManager: ContentManager) {
        self.width = width
        self.alignment = alignment
        self.spacing = spacing
        self.lineSpacing = lineSpacing
        self.contentManager = contentManager
        self.firstItemOfEachLine = {
            var firstOfEach = [Int]()
            var currentWidth: Double = width
            for (index, element) in contentManager.items.enumerated() {
                switch element {
                case .newLine:
                    firstOfEach += [index]
                    currentWidth = width
                case .any where contentManager.isVisible(viewIndex: index):
                    let itemWidth = contentManager.widths[index]
                    if currentWidth + itemWidth + spacing.minSpacing > width {
                        currentWidth = itemWidth
                        firstOfEach.append(index)
                    } else {
                        currentWidth += itemWidth + spacing.minSpacing
                    }
                default:
                    break
                }
            }

            return firstOfEach
        }()
    }

    func shouldHaveSideSpacers(line i: Int) -> Bool {
        if case .constant = spacing {
            return true
        }
        if case .dynamic = spacing, hasExactlyOneElement(line: i) {
            return true
        }
        return false
    }

    var totalLines: Int {
        firstItemOfEachLine.count
    }

    func startOf(line i: Int) -> Int {
        firstItemOfEachLine[i]
    }

    func endOf(line i: Int) -> Int {
        i == totalLines - 1 ? contentManager.items.count - 1 : firstItemOfEachLine[i + 1] - 1
    }

    func hasExactlyOneElement(line i: Int) -> Bool {
        startOf(line: i) == endOf(line: i)
    }

    var body: some View {
        VStack(alignment: alignment, spacing: lineSpacing) {
            ForEach(0 ..< totalLines, id: \.self) { lineIndex in
                HStack(spacing: 0) {
                        if alignment == .center || alignment == .trailing, shouldHaveSideSpacers(line: lineIndex) {
                            Spacer(minLength: 0)
                        }

                    ForEach(startOf(line: lineIndex) ... endOf(line: lineIndex), id: \.self) {
                        if case .dynamicIncludingBorders = spacing,
                           startOf(line: lineIndex) == $0
                        {
                            Spacer(minLength: spacing.minSpacing)
                        }

                        if case .any(let anyView) = contentManager.items[$0], contentManager.isVisible(viewIndex: $0) {
                            anyView
                        }

                        if endOf(line: lineIndex) != $0 {
                            if case .any = contentManager.items[$0], !contentManager.isVisible(viewIndex: $0) { } else {
                                if case .constant(let exactSpacing) = spacing {
                                    Spacer(minLength: 0)
                                        .frame(width: exactSpacing)
                                } else {
                                    Spacer(minLength: spacing.minSpacing)
                                }
                            }
                        } else if case .dynamicIncludingBorders = spacing {
                            Spacer(minLength: spacing.minSpacing)
                        }
                    }

                        if alignment == .center || alignment == .leading, shouldHaveSideSpacers(line: lineIndex) {
                            Spacer(minLength: 0)
                        }
                }

                .frame(maxWidth: .infinity, alignment: Alignment(horizontal: .center, vertical: .top))
            }
        }
    }
}
