// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 52;
	objects = {

/* Begin PBXBuildFile section */
		AF0EE8FF2603577D008FB2E9 /* WrappingHStackExampleApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF0EE8FE2603577D008FB2E9 /* WrappingHStackExampleApp.swift */; };
		AF0EE9012603577D008FB2E9 /* ExampleView.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF0EE9002603577D008FB2E9 /* ExampleView.swift */; };
		AF0EE9032603577E008FB2E9 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = AF0EE9022603577E008FB2E9 /* Assets.xcassets */; };
		AF37EE46291294F500B4B709 /* WrappingHStack in Resources */ = {isa = PBXBuildFile; fileRef = AF37EE45291294F500B4B709 /* WrappingHStack */; };
		AF37EE5E291296E200B4B709 /* WrappingHStack in Frameworks */ = {isa = PBXBuildFile; productRef = AF37EE5D291296E200B4B709 /* WrappingHStack */; };
		AF382A892938CB6800448B55 /* WrappingHStackExampleUITests.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF382A882938CB6800448B55 /* WrappingHStackExampleUITests.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		AF382A8C2938CB6800448B55 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = AF0EE8F32603577D008FB2E9 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = AF0EE8FA2603577D008FB2E9;
			remoteInfo = WrappingHStackExample;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		AF0EE8FB2603577D008FB2E9 /* WrappingHStackExample.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = WrappingHStackExample.app; sourceTree = BUILT_PRODUCTS_DIR; };
		AF0EE8FE2603577D008FB2E9 /* WrappingHStackExampleApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WrappingHStackExampleApp.swift; sourceTree = "<group>"; };
		AF0EE9002603577D008FB2E9 /* ExampleView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExampleView.swift; sourceTree = "<group>"; };
		AF0EE9022603577E008FB2E9 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		AF0EE9072603577E008FB2E9 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		AF37EE45291294F500B4B709 /* WrappingHStack */ = {isa = PBXFileReference; lastKnownFileType = wrapper; name = WrappingHStack; path = ..; sourceTree = "<group>"; };
		AF382A862938CB6800448B55 /* WrappingHStackExampleUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = WrappingHStackExampleUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		AF382A882938CB6800448B55 /* WrappingHStackExampleUITests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WrappingHStackExampleUITests.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		AF0EE8F82603577D008FB2E9 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AF37EE5E291296E200B4B709 /* WrappingHStack in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AF382A832938CB6800448B55 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		AF0EE8F22603577D008FB2E9 = {
			isa = PBXGroup;
			children = (
				AF37EE45291294F500B4B709 /* WrappingHStack */,
				AF0EE8FD2603577D008FB2E9 /* WrappingHStackExample */,
				AF382A872938CB6800448B55 /* WrappingHStackExampleUITests */,
				AF0EE8FC2603577D008FB2E9 /* Products */,
			);
			sourceTree = "<group>";
		};
		AF0EE8FC2603577D008FB2E9 /* Products */ = {
			isa = PBXGroup;
			children = (
				AF0EE8FB2603577D008FB2E9 /* WrappingHStackExample.app */,
				AF382A862938CB6800448B55 /* WrappingHStackExampleUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		AF0EE8FD2603577D008FB2E9 /* WrappingHStackExample */ = {
			isa = PBXGroup;
			children = (
				AF0EE9002603577D008FB2E9 /* ExampleView.swift */,
				AF0EE8FE2603577D008FB2E9 /* WrappingHStackExampleApp.swift */,
				AF0EE9022603577E008FB2E9 /* Assets.xcassets */,
				AF0EE9072603577E008FB2E9 /* Info.plist */,
			);
			path = WrappingHStackExample;
			sourceTree = "<group>";
		};
		AF382A872938CB6800448B55 /* WrappingHStackExampleUITests */ = {
			isa = PBXGroup;
			children = (
				AF382A882938CB6800448B55 /* WrappingHStackExampleUITests.swift */,
			);
			path = WrappingHStackExampleUITests;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		AF0EE8FA2603577D008FB2E9 /* WrappingHStackExample */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AF0EE90A2603577E008FB2E9 /* Build configuration list for PBXNativeTarget "WrappingHStackExample" */;
			buildPhases = (
				AF0EE8F72603577D008FB2E9 /* Sources */,
				AF0EE8F82603577D008FB2E9 /* Frameworks */,
				AF0EE8F92603577D008FB2E9 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = WrappingHStackExample;
			packageProductDependencies = (
				AF37EE5D291296E200B4B709 /* WrappingHStack */,
			);
			productName = WrappingHStackExample;
			productReference = AF0EE8FB2603577D008FB2E9 /* WrappingHStackExample.app */;
			productType = "com.apple.product-type.application";
		};
		AF382A852938CB6800448B55 /* WrappingHStackExampleUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AF382A8E2938CB6800448B55 /* Build configuration list for PBXNativeTarget "WrappingHStackExampleUITests" */;
			buildPhases = (
				AF382A822938CB6800448B55 /* Sources */,
				AF382A832938CB6800448B55 /* Frameworks */,
				AF382A842938CB6800448B55 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				AF382A8D2938CB6800448B55 /* PBXTargetDependency */,
			);
			name = WrappingHStackExampleUITests;
			productName = WrappingHStackExampleUITests;
			productReference = AF382A862938CB6800448B55 /* WrappingHStackExampleUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		AF0EE8F32603577D008FB2E9 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1410;
				LastUpgradeCheck = 1240;
				TargetAttributes = {
					AF0EE8FA2603577D008FB2E9 = {
						CreatedOnToolsVersion = 12.4;
					};
					AF382A852938CB6800448B55 = {
						CreatedOnToolsVersion = 14.1;
						TestTargetID = AF0EE8FA2603577D008FB2E9;
					};
				};
			};
			buildConfigurationList = AF0EE8F62603577D008FB2E9 /* Build configuration list for PBXProject "WrappingHStackExample" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = AF0EE8F22603577D008FB2E9;
			packageReferences = (
				AF37EE5C291296E200B4B709 /* XCRemoteSwiftPackageReference "WrappingHStack" */,
			);
			productRefGroup = AF0EE8FC2603577D008FB2E9 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				AF0EE8FA2603577D008FB2E9 /* WrappingHStackExample */,
				AF382A852938CB6800448B55 /* WrappingHStackExampleUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		AF0EE8F92603577D008FB2E9 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AF37EE46291294F500B4B709 /* WrappingHStack in Resources */,
				AF0EE9032603577E008FB2E9 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AF382A842938CB6800448B55 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		AF0EE8F72603577D008FB2E9 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AF0EE9012603577D008FB2E9 /* ExampleView.swift in Sources */,
				AF0EE8FF2603577D008FB2E9 /* WrappingHStackExampleApp.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AF382A822938CB6800448B55 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AF382A892938CB6800448B55 /* WrappingHStackExampleUITests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		AF382A8D2938CB6800448B55 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = AF0EE8FA2603577D008FB2E9 /* WrappingHStackExample */;
			targetProxy = AF382A8C2938CB6800448B55 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		AF0EE9082603577E008FB2E9 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.4;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		AF0EE9092603577E008FB2E9 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.4;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		AF0EE90B2603577E008FB2E9 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = RB643832BT;
				ENABLE_PREVIEWS = YES;
				INFOPLIST_FILE = WrappingHStackExample/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.danielkloeck.WrappingHStackExample;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		AF0EE90C2603577E008FB2E9 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = RB643832BT;
				ENABLE_PREVIEWS = YES;
				INFOPLIST_FILE = WrappingHStackExample/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.danielkloeck.WrappingHStackExample;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		AF382A8F2938CB6800448B55 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RB643832BT;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.1;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.danielkloeck.WrappingHStackExampleUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = WrappingHStackExample;
			};
			name = Debug;
		};
		AF382A902938CB6800448B55 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RB643832BT;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.1;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.danielkloeck.WrappingHStackExampleUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = WrappingHStackExample;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		AF0EE8F62603577D008FB2E9 /* Build configuration list for PBXProject "WrappingHStackExample" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AF0EE9082603577E008FB2E9 /* Debug */,
				AF0EE9092603577E008FB2E9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AF0EE90A2603577E008FB2E9 /* Build configuration list for PBXNativeTarget "WrappingHStackExample" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AF0EE90B2603577E008FB2E9 /* Debug */,
				AF0EE90C2603577E008FB2E9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AF382A8E2938CB6800448B55 /* Build configuration list for PBXNativeTarget "WrappingHStackExampleUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AF382A8F2938CB6800448B55 /* Debug */,
				AF382A902938CB6800448B55 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		AF37EE5C291296E200B4B709 /* XCRemoteSwiftPackageReference "WrappingHStack" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/dkk/WrappingHStack.git";
			requirement = {
				branch = main;
				kind = branch;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		AF37EE5D291296E200B4B709 /* WrappingHStack */ = {
			isa = XCSwiftPackageProductDependency;
			package = AF37EE5C291296E200B4B709 /* XCRemoteSwiftPackageReference "WrappingHStack" */;
			productName = WrappingHStack;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = AF0EE8F32603577D008FB2E9 /* Project object */;
}
