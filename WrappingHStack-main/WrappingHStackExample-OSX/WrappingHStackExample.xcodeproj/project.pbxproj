// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		AF37EE272912918900B4B709 /* WrappingHStackExampleApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF37EE262912918900B4B709 /* WrappingHStackExampleApp.swift */; };
		AF37EE292912918900B4B709 /* ExampleView.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF37EE282912918900B4B709 /* ExampleView.swift */; };
		AF37EE2B2912918900B4B709 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = AF37EE2A2912918900B4B709 /* Assets.xcassets */; };
		AF37EE5B291295ED00B4B709 /* WrappingHStack in Frameworks */ = {isa = PBXBuildFile; productRef = AF37EE5A291295ED00B4B709 /* WrappingHStack */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		AF37EE232912918900B4B709 /* WrappingHStackExample.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = WrappingHStackExample.app; sourceTree = BUILT_PRODUCTS_DIR; };
		AF37EE262912918900B4B709 /* WrappingHStackExampleApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WrappingHStackExampleApp.swift; sourceTree = "<group>"; };
		AF37EE282912918900B4B709 /* ExampleView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExampleView.swift; sourceTree = "<group>"; };
		AF37EE2A2912918900B4B709 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		AF37EE2F2912918900B4B709 /* WrappingHStackExample.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = WrappingHStackExample.entitlements; sourceTree = "<group>"; };
		AF37EE42291293EA00B4B709 /* WrappingHStack */ = {isa = PBXFileReference; lastKnownFileType = wrapper; name = WrappingHStack; path = ..; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		AF37EE202912918900B4B709 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AF37EE5B291295ED00B4B709 /* WrappingHStack in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		AF37EE1A2912918900B4B709 = {
			isa = PBXGroup;
			children = (
				AF37EE42291293EA00B4B709 /* WrappingHStack */,
				AF37EE252912918900B4B709 /* WrappingHStackExample */,
				AF37EE242912918900B4B709 /* Products */,
			);
			sourceTree = "<group>";
		};
		AF37EE242912918900B4B709 /* Products */ = {
			isa = PBXGroup;
			children = (
				AF37EE232912918900B4B709 /* WrappingHStackExample.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		AF37EE252912918900B4B709 /* WrappingHStackExample */ = {
			isa = PBXGroup;
			children = (
				AF37EE262912918900B4B709 /* WrappingHStackExampleApp.swift */,
				AF37EE282912918900B4B709 /* ExampleView.swift */,
				AF37EE2A2912918900B4B709 /* Assets.xcassets */,
				AF37EE2F2912918900B4B709 /* WrappingHStackExample.entitlements */,
			);
			path = WrappingHStackExample;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		AF37EE222912918900B4B709 /* WrappingHStackExample */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AF37EE322912918900B4B709 /* Build configuration list for PBXNativeTarget "WrappingHStackExample" */;
			buildPhases = (
				AF37EE1F2912918900B4B709 /* Sources */,
				AF37EE202912918900B4B709 /* Frameworks */,
				AF37EE212912918900B4B709 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = WrappingHStackExample;
			packageProductDependencies = (
				AF37EE5A291295ED00B4B709 /* WrappingHStack */,
			);
			productName = WrappingHStackExample;
			productReference = AF37EE232912918900B4B709 /* WrappingHStackExample.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		AF37EE1B2912918900B4B709 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1400;
				LastUpgradeCheck = 1400;
				TargetAttributes = {
					AF37EE222912918900B4B709 = {
						CreatedOnToolsVersion = 14.0.1;
					};
				};
			};
			buildConfigurationList = AF37EE1E2912918900B4B709 /* Build configuration list for PBXProject "WrappingHStackExample" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = AF37EE1A2912918900B4B709;
			packageReferences = (
				AF37EE59291295ED00B4B709 /* XCRemoteSwiftPackageReference "WrappingHStack" */,
			);
			productRefGroup = AF37EE242912918900B4B709 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				AF37EE222912918900B4B709 /* WrappingHStackExample */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		AF37EE212912918900B4B709 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AF37EE2B2912918900B4B709 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		AF37EE1F2912918900B4B709 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AF37EE292912918900B4B709 /* ExampleView.swift in Sources */,
				AF37EE272912918900B4B709 /* WrappingHStackExampleApp.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		AF37EE302912918900B4B709 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 12.3;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		AF37EE312912918900B4B709 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 12.3;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
			};
			name = Release;
		};
		AF37EE332912918900B4B709 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = WrappingHStackExample/WrappingHStackExample.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RB643832BT;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.danielkloeck.WrappingHStackExample;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		AF37EE342912918900B4B709 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = WrappingHStackExample/WrappingHStackExample.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RB643832BT;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.danielkloeck.WrappingHStackExample;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		AF37EE1E2912918900B4B709 /* Build configuration list for PBXProject "WrappingHStackExample" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AF37EE302912918900B4B709 /* Debug */,
				AF37EE312912918900B4B709 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AF37EE322912918900B4B709 /* Build configuration list for PBXNativeTarget "WrappingHStackExample" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AF37EE332912918900B4B709 /* Debug */,
				AF37EE342912918900B4B709 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		AF37EE59291295ED00B4B709 /* XCRemoteSwiftPackageReference "WrappingHStack" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/dkk/WrappingHStack.git";
			requirement = {
				branch = main;
				kind = branch;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		AF37EE5A291295ED00B4B709 /* WrappingHStack */ = {
			isa = XCSwiftPackageProductDependency;
			package = AF37EE59291295ED00B4B709 /* XCRemoteSwiftPackageReference "WrappingHStack" */;
			productName = WrappingHStack;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = AF37EE1B2912918900B4B709 /* Project object */;
}
