// 🏠 Tap Payment Address Integration Example
// This file shows how to use the new address functionality in the Wasfa app

import Foundation
import SwiftUI

// MARK: - Example 1: Using Existing Wasfa Address in Checkout

extension CheckoutViewModel {
    
    /// Enhanced payment processing with address support
    func processPaymentWithAddress(selectedAddress: AddressModel?) {
        // Create payment address from Wasfa address model
        let paymentAddress: TapPaymentAddress?
        if let address = selectedAddress {
            paymentAddress = TapPaymentAddress(from: address)
            print("✅ Using customer address: \(address.fullName), \(address.street), \(address.areaName)")
        } else {
            paymentAddress = nil
            print("ℹ️ No address provided - payment will proceed without address")
        }
        
        // Create enhanced payment request
        let request = TapPaymentRequest(
            amount: getTotalAmount(),
            currency: "KWD",
            customerEmail: getCustomerEmail(),
            customerName: getCustomerName(),
            customerPhone: getCustomerPhone(),
            customerAddress: paymentAddress,  // ✅ Address included
            items: getPaymentItems(),
            description: "Order from Wasfa",
            metadata: getOrderMetadata()
        )
        
        // Process payment with address
        Task {
            let result = await TapPaymentManager.shared.processPayment(
                request: request,
                presentingViewController: getCurrentViewController()!
            )
            
            await handlePaymentResult(result)
        }
    }
}

// MARK: - Example 2: Manual Address Creation

extension CheckoutViewModel {
    
    /// Create payment with manually specified address
    func processPaymentWithManualAddress() {
        // Create address manually
        let address = TapPaymentAddress(
            street: "Ahmad Al-Jaber Street",
            city: "Kuwait City",
            state: "Capital Governorate",
            country: "KW",
            postalCode: nil, // Kuwait doesn't use postal codes
            area: "Sharq",
            block: "Block 1",
            building: "Building 15",
            floor: "Floor 3",
            apartment: "Apartment 7",
            addressType: .residential
        )
        
        let request = TapPaymentRequest(
            amount: 45.250,
            customerEmail: "<EMAIL>",
            customerName: "Ahmed Al-Mansouri",
            customerPhone: "+96566805546",
            customerAddress: address
        )
        
        // Process payment
        Task {
            let result = await TapPaymentManager.shared.processPayment(
                request: request,
                presentingViewController: getCurrentViewController()!
            )
            
            await handlePaymentResult(result)
        }
    }
}

// MARK: - Example 3: Backward Compatible Payment (No Address)

extension CheckoutViewModel {
    
    /// Existing payment flow - no changes required
    func processPaymentWithoutAddress() {
        // This continues to work exactly as before
        let request = TapPaymentRequest(
            amount: getTotalAmount(),
            customerEmail: getCustomerEmail(),
            customerName: getCustomerName(),
            customerPhone: getCustomerPhone()
            // ✅ No address parameter - backward compatible
        )
        
        Task {
            let result = await TapPaymentManager.shared.processPayment(
                request: request,
                presentingViewController: getCurrentViewController()!
            )
            
            await handlePaymentResult(result)
        }
    }
}

// MARK: - Example 4: Integration with DeliveryAddressView

extension DeliveryAddressViewModel {
    
    /// Process payment using selected delivery address
    func processPaymentWithDeliveryAddress() {
        guard let selectedAddress = selectedAddress else {
            print("❌ No delivery address selected")
            return
        }
        
        // Convert delivery address to payment address
        let paymentAddress = TapPaymentAddress(from: selectedAddress)
        
        // Create payment request with delivery address
        let request = TapPaymentRequest(
            amount: getOrderTotal(),
            customerEmail: selectedAddress.email,
            customerName: selectedAddress.fullName,
            customerPhone: selectedAddress.phone,
            customerAddress: paymentAddress,
            items: getOrderItems()
        )
        
        // Navigate to payment
        Task {
            let result = await TapPaymentManager.shared.processPayment(
                request: request,
                presentingViewController: getCurrentViewController()!
            )
            
            switch result {
            case .success(let transactionId, let amount, let currency):
                print("✅ Payment successful: \(transactionId), \(amount) \(currency)")
                // Handle successful payment with address
                
            case .failure(let error):
                print("❌ Payment failed: \(error.userFriendlyMessage)")
                // Handle payment failure
                
            case .cancelled:
                print("ℹ️ Payment cancelled by user")
                // Handle cancellation
            }
        }
    }
}

// MARK: - Example 5: Address Validation Helper

extension TapPaymentAddress {
    
    /// Validate address completeness for Kuwait
    var isCompleteKuwaitAddress: Bool {
        return street != nil &&
               area != nil &&
               block != nil &&
               building != nil &&
               country == "KW"
    }
    
    /// Get formatted address string for display
    var formattedAddress: String {
        var components: [String] = []
        
        if let building = building { components.append("Building \(building)") }
        if let floor = floor { components.append("Floor \(floor)") }
        if let apartment = apartment { components.append("Apt \(apartment)") }
        if let street = street { components.append(street) }
        if let block = block { components.append("Block \(block)") }
        if let area = area { components.append(area) }
        if let state = state { components.append(state) }
        
        return components.joined(separator: ", ")
    }
}

// MARK: - Example 6: Testing Address Integration

class TapPaymentAddressTests {
    
    /// Test address creation from Wasfa AddressModel
    func testAddressFromWasfaModel() {
        let wasfaAddress = AddressModel(
            addressId: 1,
            addressTitle: .homeApartment,
            firstName: "Ahmed",
            lastName: "Al-Mansouri",
            email: "<EMAIL>",
            governorateID: 1,
            governorateName: "Capital",
            areaID: 1,
            areaName: "Sharq",
            block: "Block 5",
            phone: "+96566805546",
            setDefault: 1,
            street: "Street 10",
            building: "Building 20",
            floor: "Floor 2",
            appartment: "Apartment 3",
            alternatePhone: nil
        )
        
        let paymentAddress = TapPaymentAddress(from: wasfaAddress)
        
        assert(paymentAddress.street == "Street 10")
        assert(paymentAddress.city == "Sharq")
        assert(paymentAddress.state == "Capital")
        assert(paymentAddress.country == "KW")
        assert(paymentAddress.addressType == .residential)
        
        print("✅ Address conversion test passed")
    }
    
    /// Test payment request with address
    func testPaymentRequestWithAddress() {
        let address = TapPaymentAddress(
            street: "Test Street",
            city: "Test City",
            country: "KW",
            addressType: .residential
        )
        
        let request = TapPaymentRequest(
            amount: 10.0,
            customerEmail: "<EMAIL>",
            customerName: "Test User",
            customerAddress: address
        )
        
        assert(request.customerAddress != nil)
        assert(request.customerAddress?.street == "Test Street")
        
        print("✅ Payment request with address test passed")
    }
    
    /// Test backward compatibility
    func testBackwardCompatibility() {
        let request = TapPaymentRequest(
            amount: 10.0,
            customerEmail: "<EMAIL>",
            customerName: "Test User"
            // No address - should work fine
        )
        
        assert(request.customerAddress == nil)
        
        print("✅ Backward compatibility test passed")
    }
}

// MARK: - Usage Notes

/*
 🎯 Key Points for Implementation:
 
 1. ✅ **Optional Address**: Address is always optional - existing code continues to work
 
 2. ✅ **Automatic Conversion**: Use `TapPaymentAddress(from: addressModel)` to convert Wasfa addresses
 
 3. ✅ **Manual Creation**: Create addresses manually for custom scenarios
 
 4. ✅ **Error Handling**: Address creation failures don't break payment flow
 
 5. ✅ **Logging**: Comprehensive logging helps with debugging
 
 6. ✅ **Validation**: Use helper methods to validate address completeness
 
 7. ✅ **Testing**: Test both with and without addresses to ensure compatibility
 
 🚀 Ready for Production: The address integration is fully backward compatible
    and ready for immediate use in the Wasfa app!
*/
