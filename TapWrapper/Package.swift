// swift-tools-version: 6.1
// The swift-tools-version declares the minimum version of Swift required to build this package.
import PackageDescription

let package = Package(
    name: "TapWrapper",
    platforms: [.iOS(.v13)],
    products: [
        .library(
            name: "TapWrapper",
            targets: ["TapWrapper"]
        ),
    ],
    dependencies: [
        .package(url: "https://github.com/Tap-Payments/CheckoutSDK-iOS", from: "0.9.0"),
        .package(url: "https://github.com/Tap-Payments/TapCardInputKit-iOS", from: "2.0.0"),
        .package(url: "https://github.com/Tap-Payments/TapUIKit-iOS", from: "2.0.0"),
        .package(url: "https://github.com/Tap-Payments/TapThemeManager-iOS", from: "2.0.0"),
        .package(url: "https://github.com/Tap-Payments/TapLocalizationKit-iOS", from: "2.0.0"),
        .package(url: "https://github.com/Tap-Payments/TapAdditionsKit-iOS", from: "2.0.0"),
        .package(url: "https://github.com/Tap-Payments/TapNetworkManager-iOS", from: "2.0.0"),
    ],
    targets: [
        .target(
            name: "TapWrapper",
            dependencies: [
                .product(name: "TapCheckoutSDK", package: "CheckoutSDK-iOS"),
                .product(name: "TapCardInputKit", package: "TapCardInputKit-iOS"),
                .product(name: "TapUIKit", package: "TapUIKit-iOS"),
                .product(name: "TapThemeManager", package: "TapThemeManager-iOS"),
                .product(name: "TapLocalizationKit", package: "TapLocalizationKit-iOS"),
                .product(name: "TapAdditionsKit", package: "TapAdditionsKit-iOS"),
                .product(name: "TapNetworkManager", package: "TapNetworkManager-iOS"),
            ]
        ),
        .testTarget(
            name: "TapWrapperTests",
            dependencies: ["TapWrapper"]
        ),
    ]
)
