# Wasfa Setup Guide

This guide will help you set up the Wasfa iOS e-commerce app for development.

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

- **macOS 13.0+** (Ventura or later)
- **Xcode 15.0+** with iOS 17.0+ SDK
- **Git** for version control
- **Firebase Account** (free tier is sufficient)
- **Apple Developer Account** (for device testing and App Store deployment)

## 🚀 Quick Start

### 1. Clone the Repository

```bash
git clone https://github.com/BurhanRabbani/Wasfa.git
cd Wasfa
```

### 2. Firebase Setup

#### Create Firebase Project
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project" or "Add project"
3. Enter project name: `wasfa-ios` (or your preferred name)
4. Enable Google Analytics (optional)
5. Click "Create project"

#### Add iOS App to Firebase
1. In Firebase Console, click "Add app" and select iOS
2. Enter your iOS bundle ID: `com.wasfa.app` (or your custom bundle ID)
3. Enter app nickname: `Wasfa iOS`
4. Download `GoogleService-Info.plist`

#### Configure Firebase Services
Enable the following services in Firebase Console:

**Authentication:**
1. Go to Authentication > Sign-in method
2. Enable Email/Password
3. Enable Google (optional)

**Firestore Database:**
1. Go to Firestore Database
2. Click "Create database"
3. Start in test mode (for development)
4. Choose a location close to your users

**Storage:**
1. Go to Storage
2. Click "Get started"
3. Start in test mode

**Cloud Messaging:**
1. Go to Cloud Messaging
2. No additional setup required

### 3. Configure the iOS Project

#### Add Firebase Configuration
1. Copy your downloaded `GoogleService-Info.plist` to the project:
   ```bash
   cp ~/Downloads/GoogleService-Info.plist Wasfa/GoogleService-Info.plist
   ```

2. Verify the file is added to the Xcode project:
   - Open `Wasfa.xcodeproj` in Xcode
   - Check that `GoogleService-Info.plist` appears in the project navigator
   - Ensure it's added to the Wasfa target

#### Update Bundle Identifier (if needed)
1. In Xcode, select the Wasfa project
2. Go to Targets > Wasfa > General
3. Update Bundle Identifier to match your Firebase configuration
4. Update Team and Signing Certificate for device testing

### 4. Install Dependencies

Dependencies are managed via Swift Package Manager and should resolve automatically:

1. Open `Wasfa.xcodeproj` in Xcode
2. Wait for package resolution to complete
3. If packages don't resolve, go to File > Packages > Resolve Package Versions

**Included Packages:**
- Firebase iOS SDK
- SDWebImage
- FSPagerView (local package)
- WrappingHStack (local package)
- TapWrapper (local package)

### 5. Build and Run

1. Select your target device or simulator (iOS 15.0+)
2. Press `Cmd + R` to build and run
3. The app should launch successfully

## 🔧 Advanced Configuration

### Environment Configuration

For different environments (Development, Staging, Production), you can:

1. Create multiple Firebase projects
2. Use different `GoogleService-Info.plist` files
3. Set up Xcode schemes for each environment

### Push Notifications Setup

1. **Apple Developer Portal:**
   - Enable Push Notifications capability for your App ID
   - Generate APNs certificates or keys

2. **Firebase Console:**
   - Go to Project Settings > Cloud Messaging
   - Upload your APNs certificate or key

3. **Xcode:**
   - Enable Push Notifications capability in project settings
   - Add Background Modes capability with "Remote notifications"

### API Configuration

If you have a custom backend API:

1. Update `APIEndPoints.swift` with your API base URL
2. Configure authentication tokens
3. Update network layer in `BaseAPI.swift`

## 🧪 Testing Setup

### Unit Tests
```bash
# Run all unit tests
xcodebuild test -scheme Wasfa -destination 'platform=iOS Simulator,name=iPhone 15'

# Run specific test class
xcodebuild test -scheme Wasfa -destination 'platform=iOS Simulator,name=iPhone 15' -only-testing:WasfaTests/ProductViewModelTests
```

### UI Tests
```bash
# Run UI tests
xcodebuild test -scheme WasfaUITests -destination 'platform=iOS Simulator,name=iPhone 15'
```

### Test Data Setup
For testing, you can:
1. Use Firebase Emulator Suite for local testing
2. Create test data in Firestore
3. Use mock data in unit tests

## 📱 Device Testing

### Development Team Setup
1. In Xcode, select Wasfa project
2. Go to Signing & Capabilities
3. Select your development team
4. Enable "Automatically manage signing"

### Physical Device Testing
1. Connect your iOS device
2. Trust the developer certificate on device
3. Select your device in Xcode
4. Build and run (`Cmd + R`)

## 🚀 Deployment

### TestFlight Distribution
1. Archive the project (`Product > Archive`)
2. Upload to App Store Connect
3. Add external testers in TestFlight
4. Distribute for testing

### App Store Release
1. Update version and build numbers
2. Archive and upload to App Store Connect
3. Fill in App Store metadata
4. Submit for review

## 🔍 Troubleshooting

### Common Issues

**Firebase Configuration Error:**
```
Error: GoogleService-Info.plist not found
```
**Solution:** Ensure `GoogleService-Info.plist` is added to the Xcode project and target.

**Package Resolution Failed:**
```
Error: Failed to resolve package dependencies
```
**Solution:** 
1. Go to File > Packages > Reset Package Caches
2. Clean build folder (`Cmd + Shift + K`)
3. Rebuild project

**Signing Issues:**
```
Error: No signing certificate found
```
**Solution:**
1. Check Apple Developer account status
2. Download certificates in Xcode Preferences > Accounts
3. Select correct development team

**Simulator Issues:**
```
Error: App crashes on simulator
```
**Solution:**
1. Reset simulator (Device > Erase All Content and Settings)
2. Try different simulator version
3. Check console logs for specific errors

### Getting Help

If you encounter issues:

1. Check existing [GitHub Issues](https://github.com/BurhanRabbani/Wasfa/issues)
2. Create a new issue with:
   - Xcode version
   - iOS version
   - Error messages
   - Steps to reproduce

## 📚 Additional Resources

- [Firebase iOS Documentation](https://firebase.google.com/docs/ios/setup)
- [SwiftUI Documentation](https://developer.apple.com/documentation/swiftui)
- [Xcode Documentation](https://developer.apple.com/documentation/xcode)
- [App Store Connect Guide](https://developer.apple.com/app-store-connect/)

## ✅ Verification Checklist

After setup, verify:

- [ ] Project builds without errors
- [ ] App launches on simulator
- [ ] Firebase connection works (check console logs)
- [ ] Authentication flow works
- [ ] Product data loads correctly
- [ ] Push notifications are configured (if needed)
- [ ] Tests pass successfully

---

**Need help?** Contact <NAME_EMAIL> or create an issue on GitHub.
