# Contributing to <PERSON><PERSON>

Thank you for your interest in contributing to Wasfa! This document provides guidelines and information for contributors.

## 🤝 Code of Conduct

By participating in this project, you agree to abide by our Code of Conduct. Please treat all contributors with respect and create a welcoming environment for everyone.

## 🚀 Getting Started

### Prerequisites
- macOS 13.0 or later
- Xcode 15.0 or later
- Swift 5.9 or later
- Git
- Firebase account (for backend services)

### Setting Up Development Environment

1. **Fork and Clone**
   ```bash
   git clone https://github.com/YOUR_USERNAME/Wasfa.git
   cd Wasfa
   ```

2. **Configure Firebase**
   - Copy `GoogleService-Info.plist.template` to `GoogleService-Info.plist`
   - Update with your Firebase project credentials

3. **Open in Xcode**
   ```bash
   open Wasfa.xcodeproj
   ```

4. **Build and Test**
   - Select a simulator or device
   - Press `Cmd + R` to build and run
   - Press `Cmd + U` to run tests

## 📝 How to Contribute

### Reporting Bugs

Before creating bug reports, please check existing issues to avoid duplicates.

**Bug Report Template:**
```markdown
**Describe the bug**
A clear description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior**
What you expected to happen.

**Screenshots**
If applicable, add screenshots.

**Environment:**
- Device: [e.g. iPhone 15]
- iOS Version: [e.g. 17.0]
- App Version: [e.g. 1.0.0]
```

### Suggesting Features

Feature requests are welcome! Please provide:
- Clear description of the feature
- Use case and benefits
- Possible implementation approach
- Mockups or wireframes (if applicable)

### Pull Requests

1. **Create a Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make Changes**
   - Follow our coding standards
   - Add tests for new functionality
   - Update documentation as needed

3. **Test Your Changes**
   ```bash
   # Run tests
   xcodebuild test -scheme Wasfa -destination 'platform=iOS Simulator,name=iPhone 15'
   
   # Check for build warnings
   xcodebuild build -scheme Wasfa -destination 'platform=iOS Simulator,name=iPhone 15'
   ```

4. **Commit Changes**
   ```bash
   git add .
   git commit -m "feat: add amazing new feature"
   ```

5. **Push and Create PR**
   ```bash
   git push origin feature/your-feature-name
   ```

## 📋 Coding Standards

### Swift Style Guide

We follow the [Swift API Design Guidelines](https://swift.org/documentation/api-design-guidelines/) and [Ray Wenderlich Swift Style Guide](https://github.com/raywenderlich/swift-style-guide).

#### Key Points:
- Use meaningful variable and function names
- Follow camelCase naming convention
- Use 4 spaces for indentation
- Maximum line length: 120 characters
- Add documentation comments for public APIs

#### Example:
```swift
/// Represents a product in the e-commerce catalog
struct Product: Codable, Identifiable {
    let id: String
    let name: String
    let price: Double
    let imageURL: URL?
    
    /// Calculates the discounted price based on the given percentage
    /// - Parameter discountPercentage: The discount percentage (0-100)
    /// - Returns: The discounted price
    func discountedPrice(discountPercentage: Double) -> Double {
        return price * (1 - discountPercentage / 100)
    }
}
```

### SwiftUI Guidelines

- Use `@StateObject` for view model creation
- Use `@ObservedObject` for passed view models
- Prefer `@State` for simple view state
- Extract complex views into separate files
- Use view modifiers consistently

#### Example:
```swift
struct ProductCardView: View {
    @ObservedObject var viewModel: ProductCardViewModel
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            AsyncImage(url: viewModel.product.imageURL) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fit)
            } placeholder: {
                ProgressView()
            }
            .frame(height: 200)
            
            Text(viewModel.product.name)
                .font(.headline)
                .lineLimit(2)
            
            Text("$\(viewModel.product.price, specifier: "%.2f")")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
}
```

### Architecture Guidelines

- Follow MVVM pattern
- Use dependency injection
- Keep ViewModels testable
- Separate business logic from UI logic
- Use protocols for abstraction

## 🧪 Testing Guidelines

### Unit Tests
- Test all ViewModels
- Test business logic functions
- Mock external dependencies
- Aim for 80%+ code coverage

### UI Tests
- Test critical user flows
- Test accessibility features
- Use Page Object pattern for maintainability

### Example Test:
```swift
class ProductViewModelTests: XCTestCase {
    var viewModel: ProductViewModel!
    var mockRepository: MockProductRepository!
    
    override func setUp() {
        super.setUp()
        mockRepository = MockProductRepository()
        viewModel = ProductViewModel(repository: mockRepository)
    }
    
    func testLoadProducts() async {
        // Given
        let expectedProducts = [Product.mock()]
        mockRepository.productsToReturn = expectedProducts
        
        // When
        await viewModel.loadProducts()
        
        // Then
        XCTAssertEqual(viewModel.products, expectedProducts)
        XCTAssertFalse(viewModel.isLoading)
    }
}
```

## 📚 Documentation

- Update README.md for significant changes
- Add inline documentation for complex functions
- Update API documentation
- Include code examples where helpful

## 🔄 Git Workflow

### Commit Messages
Follow [Conventional Commits](https://www.conventionalcommits.org/):

```
type(scope): description

feat: add user authentication
fix: resolve cart calculation bug
docs: update installation guide
style: format code according to style guide
refactor: extract common UI components
test: add unit tests for checkout flow
```

### Branch Naming
- `feature/feature-name` - New features
- `fix/bug-description` - Bug fixes
- `docs/documentation-update` - Documentation updates
- `refactor/component-name` - Code refactoring

## 🎯 Areas for Contribution

We welcome contributions in these areas:

### High Priority
- Performance optimizations
- Accessibility improvements
- Unit test coverage
- UI/UX enhancements

### Medium Priority
- New payment methods
- Additional languages
- Advanced search features
- Social sharing

### Low Priority
- Code refactoring
- Documentation improvements
- Developer tools

## 📞 Getting Help

- **Questions**: Open a GitHub Discussion
- **Bugs**: Create a GitHub Issue
- **Security**: Email <EMAIL>
- **General**: Join our community discussions

## 🏆 Recognition

Contributors will be recognized in:
- README.md contributors section
- Release notes
- Special thanks in app credits

Thank you for contributing to Wasfa! 🙏
