# 🔐 Environment Variables Setup Guide for Tap Payment API Keys

## 📋 **Understanding ProcessInfo.processInfo.environment**

### **What it accesses:**
`ProcessInfo.processInfo.environment` in iOS accesses **runtime environment variables** that are available to the app process when it launches. This includes:

1. **✅ Xcode Scheme Environment Variables** (Primary method for iOS development)
2. **✅ System Environment Variables** (Inherited from macOS system)
3. **✅ Launch Arguments** (Converted to environment variables)

### **What it does NOT access:**
- ❌ `.env` files (requires additional libraries like `SwiftDotEnv`)
- ❌ `.plist` files (different API required)
- ❌ Keychain items (different API required)
- ❌ Configuration files (different API required)

## 🛠️ **Step-by-Step Xcode Environment Variables Setup**

### **Step 1: Open Scheme Editor**

1. **In Xcode**, click on the **scheme name** (next to the stop button)
2. Select **"Edit Scheme..."** from the dropdown
3. **Alternative**: Go to **Product > Scheme > Edit Scheme...**

### **Step 2: Navigate to Environment Variables**

1. In the scheme editor, select **"Run"** from the left sidebar
2. Click on the **"Arguments"** tab at the top
3. Scroll down to find the **"Environment Variables"** section

### **Step 3: Add Tap Payment Environment Variables**

Click the **"+"** button in the Environment Variables section and add:

```
Variable Name: TAP_SANDBOX_KEY
Value: sk_test_e7HZn30mqsuEPLCN9JbUO8I6

Variable Name: TAP_PRODUCTION_KEY  
Value: ********************************

Variable Name: TAP_MERCHANT_ID
Value: M370

Variable Name: TAP_SANDBOX_PUBLIC_KEY
Value: pk_test_n6wzypZ01i4JOrsR5oMYcCAx

Variable Name: TAP_PRODUCTION_PUBLIC_KEY
Value: pk_live_8waQDiY6Vo0HrKznGNmFy54u
```

### **Step 4: Save and Close**

1. Click **"Close"** to save the scheme changes
2. The environment variables are now available to your app

## 🧪 **Testing Environment Variable Setup**

### **Method 1: Use the Debug Function**

The app now includes a debug function that will show environment variable status in the console:

```swift
// This is automatically called when TapPaymentManager initializes
config.debugEnvironmentVariables()
```

**Expected Console Output:**
```
🔍 TapPaymentConfig: Checking environment variables...
📊 Environment Variables Status:
  - TAP_SANDBOX_KEY: ✅ Found
  - TAP_PRODUCTION_KEY: ✅ Found
  - TAP_MERCHANT_ID: ✅ Found
  - TAP_SANDBOX_PUBLIC_KEY: ✅ Found
  - TAP_PRODUCTION_PUBLIC_KEY: ✅ Found
🔑 Current API Key Sources:
  - Sandbox Key: sk_test_e7... (from environment)
  - Production Key: sk_live_8q... (from environment)
  - Merchant ID: M370 (from environment)
```

### **Method 2: Manual Testing**

Add this code temporarily to test environment variable access:

```swift
let environment = ProcessInfo.processInfo.environment
print("TAP_SANDBOX_KEY found: \(environment["TAP_SANDBOX_KEY"] != nil)")
print("TAP_SANDBOX_KEY value: \(environment["TAP_SANDBOX_KEY"] ?? "Not found")")
```

## 🔒 **Security Best Practices**

### **Development Environment:**
- ✅ **Use Xcode Scheme Environment Variables** for development
- ✅ **Never commit API keys** to version control
- ✅ **Use different schemes** for different environments (Debug/Release)
- ✅ **Mask sensitive values** in logs (already implemented)

### **Production Environment:**
- ✅ **Use CI/CD environment variables** for production builds
- ✅ **Consider using Keychain** for additional security
- ✅ **Implement key rotation** procedures
- ✅ **Monitor API key usage** through Tap Payment dashboard

### **Team Collaboration:**
- ✅ **Share setup instructions** (this document)
- ✅ **Don't share actual API keys** in documentation
- ✅ **Use placeholder values** in examples
- ✅ **Document environment variable names** clearly

## 🔄 **Alternative Methods (Advanced)**

### **Option 1: .env File Support**

If you want to use `.env` files, you would need to:

1. Add a library like `SwiftDotEnv` to your project
2. Create a `.env` file in your project root
3. Load the file manually in your app

**Example .env file:**
```
TAP_SANDBOX_KEY=sk_test_your_key_here
TAP_PRODUCTION_KEY=sk_live_your_key_here
TAP_MERCHANT_ID=your_merchant_id_here
```

### **Option 2: Plist Configuration**

Create a `TapConfig.plist` file:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>TAP_SANDBOX_KEY</key>
    <string>sk_test_your_key_here</string>
    <key>TAP_PRODUCTION_KEY</key>
    <string>sk_live_your_key_here</string>
    <key>TAP_MERCHANT_ID</key>
    <string>your_merchant_id_here</string>
</dict>
</plist>
```

### **Option 3: Keychain Storage**

For maximum security, store API keys in the iOS Keychain:

```swift
import Security

class KeychainManager {
    static func store(key: String, value: String) {
        // Keychain storage implementation
    }
    
    static func retrieve(key: String) -> String? {
        // Keychain retrieval implementation
    }
}
```

## 🎯 **Current Implementation Status**

### **✅ What's Working:**
- Environment variable reading via `ProcessInfo.processInfo.environment`
- Fallback to hardcoded values if environment variables not found
- Debug function to verify environment variable setup
- Secure logging (API keys are masked in console output)

### **✅ What's Configured:**
- `TAP_SANDBOX_KEY` environment variable support
- `TAP_PRODUCTION_KEY` environment variable support  
- `TAP_MERCHANT_ID` environment variable support
- `TAP_SANDBOX_PUBLIC_KEY` environment variable support
- `TAP_PRODUCTION_PUBLIC_KEY` environment variable support

### **🎯 Recommended Next Steps:**
1. **Set up environment variables** in Xcode scheme (as described above)
2. **Run the app** and check console for debug output
3. **Verify environment variables** are being read correctly
4. **Remove hardcoded API keys** once environment variables are working
5. **Set up different schemes** for different environments if needed

## 🚀 **Quick Setup Checklist**

- [ ] Open Xcode scheme editor (Product > Scheme > Edit Scheme)
- [ ] Go to Run > Arguments > Environment Variables
- [ ] Add TAP_SANDBOX_KEY with your sandbox key
- [ ] Add TAP_PRODUCTION_KEY with your production key
- [ ] Add TAP_MERCHANT_ID with your merchant ID
- [ ] Add TAP_SANDBOX_PUBLIC_KEY with your sandbox public key
- [ ] Add TAP_PRODUCTION_PUBLIC_KEY with your production public key
- [ ] Save and close the scheme editor
- [ ] Run the app and check console for environment variable debug output
- [ ] Verify "✅ Found" status for all environment variables

**Once environment variables are working, you can remove the hardcoded fallback values for enhanced security!** 🔐✨
